image:  
  name: atlassian/default-image:4

clone:  
  depth: 3

options:  
  docker: true
  size: 1x
  max-time: 20

definitions:  
  caches:  
    sonar: /root/.sonar/cache
  services:  
    docker:  
      memory: 3072
    docker2x:
      memory: 4096
      type: docker
  steps:  
    - step: &version
        name: Generate version
        script:
          - pipe: docker://jiffybbadmin/bbpipe:version-1.0.0
            variables:
              COMPONENTNAME: "${BITBUCKET_REPO_SLUG}"
        artifacts:
          - env.txt
    - step: &sonarcloud
        name: analyze code with SonarCloud
        image:
            name: registry.jiffy.ai/jiffy/jiffybase:dotnetsdk-8.0-23.11.01
            run-as-user: 0
        size: 2x
        clone:
          depth: full
        caches:
            - dotnetcore
        script:
          - apt-get update && apt-get install -y --no-install-recommends openjdk-17-jre zlib1g fontconfig libfreetype6 libx11-6 libxext6 libxrender1 libjpeg62
          - dotnet tool install --global dotnet-sonarscanner
          - dotnet tool install --global coverlet.console --version 6.0.2
          - export PATH="$PATH:/root/.dotnet/tools"
          - dotnet sonarscanner begin /k:"${SONAR_PROJECT_KEY}" /o:"${SONAR_OGRANIZAION_KEY}" /d:sonar.host.url="${SONAR_HOST_URL}" /d:sonar.login="${SONAR_TOKEN}" /d:sonar.qualitygate.wait=true /d:sonar.cs.opencover.reportsPaths="coverage.xml" /d:sonar.dotnet.excludeTestProjects=true /d:sonar.exclusions="test/**/*"
          - cp src/nuget.config /root/.nuget/NuGet/NuGet.Config
          - dotnet restore src/Mq/Mq.csproj
          - dotnet build --no-incremental test/MqTest/MqTest.csproj
          - coverlet test/MqTest/bin/net9.0/MqTest.dll --target "dotnet" --targetargs "test --no-build test/MqTest/MqTest.csproj" -f=opencover -o="coverage.xml"	
          - dotnet sonarscanner end /d:sonar.token="${SONAR_TOKEN}"
    - step: &dockerlint
        name: Docker file lint using Hadolint
        image: hadolint/hadolint:latest-debian
        script:
          - hadolint src/Dockerfile
        condition:
          changesets:
            includePaths:
              - "src/Dockerfile"
    - step: &helmpublish
        name: Helm - Packaging and Publish
        script:
          - pipe: docker://jiffybbadmin/bbpipe:helmpublish-1.0.1
            variables:
              nexus_username: ${nexus_username}
              nexus_password: ${nexus_password}
        artifacts:
          - env.txt
    - step: &dockerbuild
        name: Docker Build, publish to AWS ECR
        caches:
          - docker
        script:
          - source env.txt #Getting VERSION variable
          - pipe: docker://jiffybbadmin/bbpipe:dockerbuild-1.0.0
            variables:
              BUILD_ARG: "--build-arg NEXUS_NUGET_READ_USERNAME=${NEXUS_NUGET_READ_USERNAME} 
                          --build-arg NEXUS_NUGET_READ_PASSWORD=${NEXUS_NUGET_READ_PASSWORD} 
                          --build-arg PackageVersion=${VERSION} 
                          --build-arg GitCommitHash=${BITBUCKET_COMMIT} 
                          --build-arg commitId=${BITBUCKET_COMMIT} 
                          --build-arg componentVersion=${VERSION}"
              ECR_ID: "${ECR_ID}"
              AWS_ACCESS_KEY_ID: "${ECR_AWS_ACCESS_KEY_ID}"
              AWS_SECRET_ACCESS_KEY: "${ECR_AWS_SECRET_ACCESS_KEY}"
              AWS_DEFAULT_REGION: "${AWS_DEFAULT_REGION}"
              SOURCE_DIR: "src"
              DockerfileName: "Dockerfile"
        services:
          - docker
        artifacts:
          - env.txt
    - step: &gittag
        name: Git Tag
        script:
          - source env.txt
          - git tag -am "[skip ci] latest build number." "${VERSION}"
          - git push origin "${VERSION}"
        artifacts:
          - env.txt
    - step: &deploydev
        name: Deploy to dev env
        script:
          - pipe: docker://jiffybbadmin/bbpipe:deploydev-1.0.0
            variables:
              SERVICE_NAME: "${BITBUCKET_REPO_SLUG}"
              NAMESPACE: "dev-workflow"
              CLUSTER_URL: "https://dev-workflow.cluster.jiffy.ai"
              HOST_NAME: "dev-workflow.cluster.jiffy.ai"
              HELMARGS: ""
              JENKINS_TOKEN: ${JENKINS_TOKEN}
        artifacts:
          - env.txt
    - step: &at-integration
        name: Deployment to the AT-Integration environment, K6, and image tagging.
        script:
          - pipe: docker://jiffybbadmin/bbpipe:atintegration-1.0.0
            variables:
              service_name: "${BITBUCKET_REPO_SLUG}"
              componentName: "${BITBUCKET_REPO_SLUG}"
              test_suite: "all"
              test_name: "regressionTest"
              helmArg: ""
              skipK6: ""
              enableCucumber: ""
              dep_components: ""
              AWS_ACCESS_KEY_ID: ${ECR_AWS_ACCESS_KEY_ID}
              AWS_SECRET_ACCESS_KEY: ${ECR_AWS_SECRET_ACCESS_KEY}
              AWS_DEFAULT_REGION: "${AWS_DEFAULT_REGION}"
    - step: &generate_sbom
        name: Create SBOM
        clone:
          depth: full
        script:
          - pipe: docker://jiffybbadmin/bbpipe:dependencytracker-1.0.0
            variables:
              NEXUS_USERNAME: ${nexus_username}
              NEXUS_PASSWORD: ${nexus_password}
              PRODUCT: "apex"
        artifacts:
          - sbom/**

pipelines:
  branches:
    develop:
      - step: *version
      - parallel:
          - step: *sonarcloud
          - step: *dockerlint
      - step: *dockerbuild
      - step: *helmpublish
      - step: *gittag
      - parallel:
          - step: *deploydev
          - step: *at-integration
  custom:
    sonar:
      - parallel:
          - step: *sonarcloud
          - step: *dockerlint
    featurebuildhelmpublish:
      - step: *version
      - parallel:
          - step: *sonarcloud
          - step: *dockerlint
      - step: *dockerbuild
      - step: *helmpublish
      - step: *gittag
    scheduled-sbom-dt:
      - step: *generate_sbom
  pull-requests:
    '**':
      - parallel:
          - step: *sonarcloud
          - step: *dockerlint