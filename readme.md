# COMPILING, TESTING, PACKAGING AND PUBLISHING TO ECR

## To compile

Please ensure following arguments are passed:

1. PackageVersion - manually input version number
    * For. e.g. 2.0.0
2. GitCommitHash - git commit sha value
    * For. e.g. 05e83f51e9f40ce165b5e99b264a6a751e6e8c8c


below command compiles and also generates a nuget package:


```shell

dotnet build net-service-templateprocessor\src\Mq\Mq.csproj --configuration Release -p:PackageVersion=$(PackageVersion) -p:GitCommitHash=$(GitCommitHash)

```


> NOTE: Please use right variables in case that is being changed

Folders where the output files are available:

1. Binaries
    * net-service-templateprocessor\src\Mq\bin\net8.0\Net.Service.Mq.dll

## To run the tests

use dotnet test to run the test associated with the project, use coverlet to capture the component coverage

to install coverlet tool (usually needed to be done once)

```shell

dotnet tool install --global coverlet.console	

```

to get the coverage report, use the coverlet command


```shell

dotnet build --no-incremental test\TemplateProcessorTest\TemplateProcessorTest.csproj
coverlet test\TemplateProcessorTest\bin\net8.0\TemplateProcessorTest.dll --target "dotnet" --targetargs "test --no-build test\TemplateProcessorTest\TemplateProcessorTest.csproj" -f=opencover -o="test\TemplateProcessorTest\bin\TemplateProcessorTest.Coverage.xml"

```

test results would be stored to file - test\TemplateProcessorTest\bin\TemplateProcessorTest.Coverage.xml

>
> SONAR scan exclusions : appsettings.yaml, appsettings.*.yaml, launchSettings.json, *.generated.cs, *.resx, Resources.Designer.cs
>

## Docker build

Dockerfile is located in the following folder: net-template-processor\src\Dockerfile

this publishes an image to the configured repository with iamge name "templateprocessor"

> NOTE: Please change the name of the image as required

```shell

docker build . -t templateprocessor --progress=plain

```

> TODO: We will need to push the image to ECR. Please let know the steps.


## Configuration values

appsettings.yaml has the required configuations. Currently below properties are configurable:

```yaml
TemplateOptions:
    CacheOptions:
        CacheExpiryInSeconds: 86400
```

> TODO: We will need a helm chart map for the above. We will extend with other required options in the future