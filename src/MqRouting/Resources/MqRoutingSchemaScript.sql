-- CREATE USER IF NOT EXISTS postgresuser;
-- CREATE DATABASE mqrouting;
-- GRANT ALL PRIVILEGES ON DATABASE postgres TO postgresuser;

CREATE TABLE IF NOT EXISTS "mq_routing_rules"
(
    "tenant_id" TEXT NOT NULL,
    "app_id" TEXT NOT NULL,
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "created_on" TIMESTAMP NOT NULL,
    "last_modified_on" TIMESTAMP NOT NULL,
    "description" TEXT NULL,
    "category" TEXT NULL,
    "tags" TEXT NULL,
    "is_enabled" BOOLEAN NOT NULL,
    "enable_condition" TEXT NULL,
    "condition_parameters" TEXT NULL,
    "json_schema" TEXT NULL,
    "sample" TEXT NULL,
    "inappinfo" TEXT NULL,
    "emailinfo" TEXT NULL,
    "smsinfo" TEXT NULL,
    "workflowinfo" TEXT NULL,
    "webhookinfo" TEXT NULL,
    "recipient_roles" TEXT NULL,
    "recipient_orgs" TEXT NULL,
    "nav_variables" TEXT NULL,
    "nav_pageurlexpression" TEXT NULL,
    "can_opt_out" BOOLEAN NULL,
    "recipients_required" BOOLEAN NULL,
    PRIMARY KEY ("tenant_id", "app_id", "id")
);

CREATE INDEX IF NOT EXISTS IDX_mq_routing_rules_lookup_by_type ON "mq_routing_rules" ("tenant_id", "app_id", "type");

ALTER TABLE "mq_routing_rules" DROP COLUMN IF EXISTS "notifications";
ALTER TABLE "mq_routing_rules" DROP COLUMN IF EXISTS "notifications_override";

ALTER TABLE "mq_routing_rules" ADD COLUMN IF NOT EXISTS "recipient_roles" TEXT NULL;
ALTER TABLE "mq_routing_rules" ADD COLUMN IF NOT EXISTS "recipient_orgs" TEXT NULL;
ALTER TABLE "mq_routing_rules" ADD COLUMN IF NOT EXISTS "inappinfo" TEXT NULL;
ALTER TABLE "mq_routing_rules" ADD COLUMN IF NOT EXISTS "emailinfo" TEXT NULL;
ALTER TABLE "mq_routing_rules" ADD COLUMN IF NOT EXISTS "smsinfo" TEXT NULL;
ALTER TABLE "mq_routing_rules" ADD COLUMN IF NOT EXISTS "workflowinfo" TEXT NULL;
ALTER TABLE "mq_routing_rules" ADD COLUMN IF NOT EXISTS "webhookinfo" TEXT NULL;
ALTER TABLE "mq_routing_rules" ADD COLUMN IF NOT EXISTS "can_opt_out" BOOLEAN NULL;
ALTER TABLE "mq_routing_rules" ADD COLUMN IF NOT EXISTS "recipients_required" BOOLEAN NULL;
ALTER TABLE "mq_routing_rules" ADD COLUMN IF NOT EXISTS "nav_variables" TEXT NULL;
ALTER TABLE "mq_routing_rules" ADD COLUMN IF NOT EXISTS "nav_pageurlexpression" TEXT NULL;


SELECT pg_advisory_xact_lock(floor(random() * 100000 + 1)::int);
-- NOTE: NOTE: NOTE: NOTE: all functions definition goes below here

CREATE OR REPLACE FUNCTION update_dt() RETURNS trigger AS $$
BEGIN
    IF NEW.created_on IS NULL THEN
        NEW.created_on := NOW();
    END IF;

    NEW.last_modified_on := NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER set_lastmod
BEFORE INSERT OR UPDATE ON
  mq_routing_rules
FOR EACH ROW EXECUTE PROCEDURE
  update_dt();