{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"MqRouting": {"commandName": "Project", "launchBrowser": false, "launchUrl": "mqrouting/swagger-ui", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "http://localhost:5002", "dotnetRunMessages": true}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/mqrouting/swagger-ui", "publishAllPorts": true}}}