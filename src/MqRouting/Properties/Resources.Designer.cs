//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace MqRouting.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("MqRouting.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to -- CREATE USER IF NOT EXISTS postgresuser;
        ///-- CREATE DATABASE mqrouting;
        ///-- GRANT ALL PRIVILEGES ON DATABASE postgres TO postgresuser;
        ///
        ///CREATE TABLE IF NOT EXISTS &quot;mq_routing_rules&quot;
        ///(
        ///    &quot;tenant_id&quot; TEXT NOT NULL,
        ///    &quot;app_id&quot; TEXT NOT NULL,
        ///    &quot;id&quot; TEXT NOT NULL,
        ///    &quot;name&quot; TEXT NOT NULL,
        ///    &quot;type&quot; TEXT NOT NULL,
        ///    &quot;created_on&quot; TIMESTAMP NOT NULL,
        ///    &quot;last_modified_on&quot; TIMESTAMP NOT NULL,
        ///    &quot;description&quot; TEXT NULL,
        ///    &quot;category&quot; TEXT NULL,
        ///    &quot;tags&quot; TEXT NULL,
        ///    &quot;is_enabled&quot; BOO [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string MqRoutingSchemaScript {
            get {
                return ResourceManager.GetString("MqRoutingSchemaScript", resourceCulture);
            }
        }
    }
}
