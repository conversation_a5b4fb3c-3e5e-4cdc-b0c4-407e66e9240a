---
Serilog:
  MinimumLevel:
    Default: Information
    Override:
      Microsoft: Information
      System: Information
      MqRouting: Debug
      Apex.Services: Debug
DeploymentOptions:
  ReleaseNamespace: dev-workflow
  OtlpExporterUrl: http://jaeger-collector.tracing.svc.cluster.local:4317
ServiceOptions:
  WorkflowUrl: https://dev-workflow.cluster.jiffy.ai
  ClsUrl: https://dev-workflow.cluster.jiffy.ai/cls
  ConfigManagerUrl: https://dev-workflow.cluster.jiffy.ai/config-management
  PamUrl: https://dev-workflow.cluster.jiffy.ai/pam
  ModelRepoUrl: https://dev-workflow.cluster.jiffy.ai/model-repo
  JiffyDriveUrl: https://dev-workflow.cluster.jiffy.ai/drive
  MessengerUrl: http://workhorse-messenger.dev-workflow.svc.cluster.local:5000/messenger
  TemplateProcessorUrl: https://dev-workflow.cluster.jiffy.ai/tp
IAM:
  tenantId: jiffy
  tenantName: jiffy
  graceTimeForTokenExpireInSeconds: 120
  url: https://dev-workflow.cluster.jiffy.ai
  retryPolicyClientName: defaultWithLargeRetry
MqRoutingOptions:
  StreamProcessingUri: http://localhost:9095
NATS:
  ModelRepoUrl: http://localhost:8383
  Server: host.docker.internal:3100
  Subject: event-router-nats-subject
  Queue: event-router-nats-subject-queue
MqOptions: