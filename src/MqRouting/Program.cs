var userProfileFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), "secrets-store");
await Apex.BaseProgram<MqRouting.Startup>.Main(
	new Apex.BaseProgramStartupInfo
	{
		Args = args,
#if DEBUG
		ListenPort = 5001,
		HealthPort = 19013,
		PrometheusPort = 9013,
#endif
		AppConfigurationCallback = (cb) =>
		cb
			.AddPropertiesFile(Path.Combine(userProfileFolder, "db.properties"))
			.AddPropertiesFile(Path.Combine(userProfileFolder, "principal.properties"))
			.AddPropertiesFile(Path.Combine(userProfileFolder, "extra.properties"))
	})
	.RunAsync();