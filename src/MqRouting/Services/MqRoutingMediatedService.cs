using Ai.Jiffy.Models.Mediator.In;
using Ai.Jiffy.Models.Mediator.Out;
using Apex.Models;
using Apex.Services;
using Google.Protobuf.Collections;
using Jiffy.Model;
using MqRouting.Models;
using System.IO.Compression;
using System.Security.Cryptography;
using System.Text;

namespace MqRouting.Services;

/// <summary>
/// Class MqRoutingMediatedService.
/// </summary>
/// <param name="_logger">The logger.</param>
/// <param name="_celToJsService">The CEL to js translator service.</param>
/// <param name="_mqRoutingRulesService">The mq routing rules service.</param>
/// <param name="_tokenGenerationService">The token generation service.</param>
/// <param name="_appManagerService">The app manager service.</param>
/// <remarks>Initializes a new instance of the <see cref="MqRoutingMediatedService" /> class.</remarks>
public sealed partial class MqRoutingMediatedService(ILogger<MqRoutingMediatedService> _logger,
	ICelToJsService _celToJsService,
	IMqRoutingRulesService _mqRoutingRulesService, ITokenGenerationService _tokenGenerationService, IAppManagerService _appManagerService, IJiffyDriveRestService _jiffyDriveRestService) : IMqRoutingMediatedService
{
	/// <summary>
	/// Publishes the components - routes.
	/// </summary>
	/// <param name="publishBody">The publish body.</param>
	/// <param name="memoryStream">The memory stream.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>public Task.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	public async Task PublishAsync(PublishBody publishBody, MemoryStream memoryStream, CancellationToken cancellationToken = default)
	{
		_logger.LogInformation("PUBLISH - mq route - PublishBody={PublishBody}", publishBody);
		try
		{
			ArgumentNullException.ThrowIfNull(publishBody.Application);
			ArgumentException.ThrowIfNullOrEmpty(publishBody.Application.Id);
			ArgumentNullException.ThrowIfNull(publishBody.Environment);
			ArgumentException.ThrowIfNullOrEmpty(publishBody.Environment.TenantId);

			var applicationId = publishBody.Application.Id!;
			var tenantId = publishBody.Environment.TenantId!;

			using var zip = new ZipArchive(memoryStream, ZipArchiveMode.Create, true);

			if (publishBody.Component is not null)
			{
				_logger.LogInformation("PUBLISH - mq route - Components#={Component}", publishBody.Component.Count);
				foreach (var componentId in publishBody.Component.Select(c => c.Id))
				{
					_logger.LogInformation("PUBLISH - mq route - ComponentId={ComponentId}", componentId);
					var routes = await _mqRoutingRulesService.GetAllRoutingRulesAsync(tenantId, applicationId);
					_logger.LogInformation("PUBLISH - mq route - ComponentId={ComponentId} Routes#={Routes}", componentId, routes?.Count);

					var dataModel = new MediatorServiceResponse<List<Models.MqRoutingRule>?>(routes, publishBody.IsNotReady);
					var layerResponse = new LayerResponse<List<Models.MqRoutingRule>?>(dataModel, null);
					var layerMetadata = new LayerMetaData("application/schema+json");

					var dataModelJson = layerResponse.ToJSON();
					var layerId = Convert.ToHexString(SHA256.HashData(System.Text.Encoding.UTF8.GetBytes(dataModelJson)));
					var path = $"components/{componentId}/metadata/deployment.jiffy-json.v1/{layerId}";
					_logger.LogInformation("PUBLISH - mq route - ComponentId={ComponentId} LayerId={LayerId} Path={Path}", componentId, layerId, path);

					var zipEntry = zip.CreateEntry(path);
					{
						using var writerZipEntryStream = new StreamWriter(zipEntry.Open());
						await writerZipEntryStream.WriteAsync(dataModelJson);
					}

					var metadataPath = $"{path}-metadata.json";
					var zipEntryMetadata = zip.CreateEntry(metadataPath);
					{
						using var writerZipEntryStream = new StreamWriter(zipEntryMetadata.Open());
						await writerZipEntryStream.WriteAsync(layerMetadata.ToJSON());
					}
				}
			}
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "ERROR IN PUBLISH - mq route - PublishBody={PublishBody}", publishBody);
			throw;
		}
	}

	/// <summary>
	/// Deploys the routes to the application.
	/// </summary>
	/// <param name="deploymentRequest">The deployment request.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>public Task.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	public async Task<DeploymentResponse> DeployAsync(DeploymentRequest deploymentRequest, CancellationToken cancellationToken = default)
	{
		_logger.LogInformation("DEPLOY - mq routes - DeploymentRequest={DeploymentRequest}", deploymentRequest);
		try
		{
			ArgumentNullException.ThrowIfNull(deploymentRequest.Environment);
			ArgumentException.ThrowIfNullOrEmpty(deploymentRequest.Environment.TenantId);
			ArgumentNullException.ThrowIfNull(deploymentRequest.Application);
			ArgumentException.ThrowIfNullOrEmpty(deploymentRequest.Application.Id);

			var hasUpdates = false;
			var tenantId = deploymentRequest.Environment.TenantId;
			var applicationId = deploymentRequest.Application.Id;

			_logger.LogInformation("DEPLOY - mq route - trying to resolve user id - TenantId={TenantId} AppId={AppId}", tenantId, applicationId);
			var userId = await _tokenGenerationService.ResolveUserIdFromTenantAppIdAsync(tenantId, applicationId, cancellationToken: cancellationToken);
			var userSecurityInfo = new UserSecurityInfo(tenantId, applicationId, userId, null);
			_logger.LogInformation("DEPLOY - mq route - trying to resolve user id - TenantId={TenantId} AppId={AppId} UserId={UserId}", tenantId, applicationId, userId);

			async Task<bool> InternalDeployRoutesAsync(List<MqRoutingRule>? routes)
			{
				var hasUpdates = false;
				if (routes is not null && routes.Count != 0)
				{
					foreach (var route in routes)
					{
						hasUpdates |= await InternalDeployAsync(tenantId, applicationId, route);
					}
				}
				else
				{
					_logger.LogInformation("DEPLOY - mq route - metadata has empty/no routes, so no op");
				}
				return hasUpdates;
			}


			foreach (var metadata in deploymentRequest.Metadata)
			{
				_logger.LogInformation("DEPLOY - mq route - Unpacking Metadata...={Metadata}", metadata);
				var layerResponse = Encoding.UTF8.GetString(Convert.FromBase64String(metadata))
					.ToObject<LayerResponse<List<Models.MqRoutingRule>?>>();
				_logger.LogInformation("DEPLOY - mq route - unpacked Metadata={Metadata}", metadata);

				var routes = layerResponse?.DataModel?.Instance;

				hasUpdates |= await InternalDeployRoutesAsync(routes);
			}

			var hasUpdatesDomainModel = false;

			try
			{
				_logger.LogInformation("DEPLOY - mq route - trying to lookup domain models - TenantId={TenantId} AppId={AppId}", tenantId, applicationId);
				if (deploymentRequest.Application.DomainModels is List<DomainModel> domainModels)
				{
					_logger.LogInformation("DEPLOY - mq route - domain models found - TenantId={TenantId} DomainModels#={DomainModels}", tenantId, domainModels.Count);
					foreach (var domainModel in domainModels)
					{
						try
						{
							if (string.IsNullOrEmpty(domainModel.AppInstId))
							{
								var deployMetadata = domainModel.DeployMetadata;
								var deploymentDomainModelsPath = $"{deployMetadata?.BucketName}/{deployMetadata?.Path}/{domainModel.Name}-mq-router";
								_logger.LogInformation("DEPLOY - mq route - deploying from domain models - TenantId={TenantId} DomainModel={DomainModel} DeploymentDomainModelsPath={DeploymentDomainModelsPath}",
									tenantId, domainModel, deploymentDomainModelsPath);

								if (!deploymentDomainModelsPath.IsNullOrEmpty())
								{
									_logger.LogInformation("DEPLOY - mq route - downloading metadata file - TenantId={TenantId} DeploymentDomainModelsPath={DeploymentDomainModelsPath}",
										tenantId, deploymentDomainModelsPath);

									var jsonModel = await _jiffyDriveRestService.GetFileAsTextAsync(deploymentDomainModelsPath, userSecurityInfo, cancellationToken);
									if (!jsonModel.IsNullOrEmpty())
									{
										_logger.LogInformation("DEPLOY - mq route - downloaded metadata file, deploying - TenantId={TenantId} DeploymentDomainModelsPath={DeploymentDomainModelsPath}",
											tenantId, deploymentDomainModelsPath);

										var routes = jsonModel.ToObject<LayerResponse<List<MqRoutingRule>?>>()?.DataModel?.Instance;
										hasUpdatesDomainModel |= await InternalDeployRoutesAsync(routes);
									}
								}
							}
							else
							{
								_logger.LogInformation("DEPLOY - mq route - domain model, using AppInstId - {AppInstId}",
									domainModel.AppInstId);
								var routes = await _mqRoutingRulesService.GetAllRoutingRulesAsync(tenantId, domainModel.AppInstId);
								_logger.LogInformation("DEPLOY - mq route - domain model AppInstId={AppInstId} Routes#={Routes}", domainModel.AppInstId, routes?.Count);
								hasUpdatesDomainModel |= await InternalDeployRoutesAsync(routes!);

							}
							
						}
						catch (Exception e)
						{
							_logger.LogError(e,"DEPLOY - ERROR - when deploying a domain model mq route - DeploymentRequest={DeploymentRequest} Error={Error}",deploymentRequest, e.Message);
						}
					}
				}
			}
			catch (Exception ex)
			{
				_logger.LogError(ex, "DEPLOY - ERROR - when deploying domain models mq route - DeploymentRequest={DeploymentRequest} Error={Error}",
					deploymentRequest, ex.Message);
			}

			if (hasUpdates || hasUpdatesDomainModel)
			{
				_logger.LogInformation("DEPLOY - Resetting cached subscribers - DeploymentRequest={DeploymentRequest}", deploymentRequest);
			}
			return new DeploymentResponse(deploymentRequest.Component.Id, DeploymentResponseStatus.completed, $"Mq routing rules for application {applicationId} created.");
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "DEPLOY - ERROR - mq route - DeploymentRequest={DeploymentRequest} Error={Error}", deploymentRequest, ex.Message);
			throw;
		}
	}

	/// <summary>
	/// Deploys the route to the application.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="applicationId">The application identifier.</param>
	/// <param name="userId"></param>
	/// <param name="routeId">The routing identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>public Task.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	public async Task<bool> DeployAsync(string tenantId, string applicationId, string userId, string routeId,
		CancellationToken cancellationToken = default)
	{
		_logger.LogInformation("DEPLOY - mq route - RouteId={RouteId}", routeId);
		try
		{
			var mqRule = await _appManagerService.GetComponentFromModelRepoAsync<EventSource>(tenantId, applicationId, userId, "eventSource", routeId, cancellationToken)
				?? throw new MediatorException($"unable to get component with id - Id={routeId}");

			// expand topic names...
			static string GetTopicName(EventSource.Types.TopicSegment topicSegment)
				=> topicSegment.ExtCase switch
				{
					EventSource.Types.TopicSegment.ExtOneofCase.Literal => topicSegment.Literal,
					EventSource.Types.TopicSegment.ExtOneofCase.SegmentAttribute => topicSegment.SegmentAttribute.Name,
					EventSource.Types.TopicSegment.ExtOneofCase.None => string.Empty,
					_ => throw new MediatorException($"unknown topic segment type - Type={topicSegment.ExtCase}")
				};

			static string GetTopicNames(RepeatedField<EventSource.Types.TopicSegment> topicSegments)
				=> topicSegments.Select(t => GetTopicName(t)).JoinWith('.') ?? string.Empty;

			// convert CEL to js
			async Task<string?> CelToJsAsync(string? expression, CancellationToken cancellationToken = default)
			{
				if (expression.IsNullOrEmpty())
				{
					return null;
				}

				//var userId = await _tokenGenerationService.ResolveUserIdFromTenantAppIdAsync(tenantId, applicationId, cancellationToken: cancellationToken);
				var userInfo = new UserSecurityInfo(tenantId, applicationId, userId, null);
				var js = await _celToJsService.CelToJsAsync(expression, userInfo, cancellationToken: cancellationToken);
				return js?.ToJSON();
			}

			var templateTasks = mqRule.DefaultTemplates
				.Select(t => _appManagerService.GetComponentFromModelRepoAsync<EmailTemplate>(tenantId, applicationId, userId,"emailTemplate", t.TargetId));
			var templates = await Task.WhenAll(templateTasks);

			var inAppTemplate = Array.Find(templates, t => t?.ChannelType == AlertChannelType.CtInApp);
			var emailTemplate = Array.Find(templates, t => t?.ChannelType == AlertChannelType.CtEmail);
			
			var outboxRefId = mqRule.EmailOutboxRef?.TargetId;
			string? providerName = null;
			if(outboxRefId is not null && emailTemplate is not null)
			{
				//outbox available
				_logger.LogInformation("DEPLOY - mq route - Email template found - RouteId={RouteId} OutboxRef={OutboxRef}, emailTemplate={emailTemplate}", routeId, outboxRefId, emailTemplate.Uuid);
				
				if(!outboxRefId.IsNullOrEmpty())
				{
					
				    var outbox = await _appManagerService.GetComponentFromModelRepoAsync<MediatedService>(tenantId, applicationId, userId, "mediated_service", outboxRefId, cancellationToken) ?? throw new MediatorException($"unable to get component with id - Id={outboxRefId}");
				    _logger.LogInformation("DEPLOY - mq route - Email outbox found - RouteId={RouteId} Outbox={OutboxRef}", routeId, outbox);
					providerName = outbox.Name;
				}
			}

			var rule = new Models.MqRoutingRule
			{
				tenant_id = tenantId,
				app_id = applicationId,
				id = mqRule.Uuid,
				name = mqRule.Name,
				type = GetTopicNames(mqRule.TopicName),
				description = mqRule.Description,
				category = mqRule.Category,
				tags = [.. mqRule.SubCategory],
				json_schema = !mqRule.Schema.IsNullOrEmpty() ? mqRule.Schema.ToJsonDocument() : null,
				sample = !mqRule.Sample.IsNullOrEmpty() ? mqRule.Sample.ToJsonDocument() : null,
				enable_condition = await CelToJsAsync(mqRule.EnableCondition?.EditorExpression?.Text, cancellationToken),
				is_enabled = mqRule.Enabled != BinaryOption.BoDisabled,
				can_opt_out = mqRule.UserOptOutAllowed == BinaryOption.BoEnabled,
				recipients_required = mqRule.RecipientsRequired == BinaryOption.BoEnabled,
				inappinfo = inAppTemplate is null ? null : new Models.InAppAlertInfo
				(
					inAppTemplate.Name,
					true,
					inAppTemplate.Uuid,
					inAppTemplate.Cc
				),
				emailinfo = emailTemplate is null ? null : new Models.EmailAlertInfo
				(
					emailTemplate.Name,
					true,
					providerName!,
					emailTemplate.Uuid
				),

				// navigation variables...
				nav_variables = mqRule.NavigationVariables?.Select(nv => new Models.NavigationVariableInfo(nv.Variable.Name, nv.VariableValueExpression))?.ToList(),
				nav_pageurlexpression = mqRule.PageUrlExpression,
			};
			return await InternalDeployAsync(tenantId, applicationId, rule);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "DEPLOY - ERROR - mq route - RouteId={RouteId} Error={Error}", routeId, ex.Message);
			throw;
		}
	}

	/// <summary>
	/// Helper to deploy the route to the application.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="applicationId">The application identifier.</param>
	/// <param name="route">The route.</param>
	/// <returns>
	///   <c>true</c> if XXXX, <c>false</c> otherwise.</returns>
	private async Task<bool> InternalDeployAsync(string tenantId, string applicationId, Models.MqRoutingRule route)
	{
		_logger.LogInformation("DEPLOY - deploying mq route - Route={Route}", route);

		var routeId = route.id;
		route.tenant_id = tenantId;
		route.app_id = applicationId;
		var updated = await _mqRoutingRulesService.UpsertRoutingRuleAsync(route) > 0;
		if (updated)
		{
			_logger.LogInformation("DEPLOY - mq route - deployed/inserted to target environment - RouteId={RouteId}", routeId);
		}
		return updated;
	}

	/// <summary>
	/// Undeploys the routes from the application.
	/// </summary>
	/// <param name="undeployRequest">The undeploy request.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>public Task.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	public async Task<DeploymentResponse> UndeployAsync(UndeployRequest undeployRequest, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(undeployRequest.Environment);
		ArgumentException.ThrowIfNullOrEmpty(undeployRequest.Environment.TenantId);
		ArgumentNullException.ThrowIfNull(undeployRequest.Application);
		ArgumentException.ThrowIfNullOrEmpty(undeployRequest.Application.Id);

		var tenantId = undeployRequest.Environment.TenantId;
		var applicationId = undeployRequest.Application.Id;
		// removing code that removes the mq route entries... will be introduced back, once the other fixes are in-place

		_logger.LogInformation("UNDEPLOY - UndeployRequest={UndeployRequest}", undeployRequest);
		return new DeploymentResponse(undeployRequest.Component.Id, DeploymentResponseStatus.completed, $"Mq routing rules for tenant {tenantId}, application {applicationId} undeployed.");
	}

	/// <summary>
	/// Undeploys the route.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="applicationId">The application identifier.</param>
	/// <param name="routeId">The route identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>public Task.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	public Task<bool> UndeployAsync(string tenantId, string applicationId, string routeId, CancellationToken cancellationToken = default)
	{
		return InternalDeleteMqRulesByIdAsync(tenantId, applicationId, routeId);
	}

	/// <summary>
	/// Destroys the app - deletes all routes in the application.
	/// </summary>
	/// <param name="destroyRequest">The destroy request.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>public Task.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	public async Task<DeploymentResponse> DestroyAsync(DestroyRequest destroyRequest, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(destroyRequest.Environment);
		ArgumentException.ThrowIfNullOrEmpty(destroyRequest.Environment.TenantId);
		ArgumentNullException.ThrowIfNull(destroyRequest.Application);
		ArgumentException.ThrowIfNullOrEmpty(destroyRequest.Application.Id);

		var tenantId = destroyRequest.Environment.TenantId;
		var applicationId = destroyRequest.Application.Id;
		await InternalDeleteMqRulesForAppAsync(tenantId, applicationId);

		_logger.LogInformation("DESTROY - DeploymentRequest={DeploymentRequest}", destroyRequest);
		return new DeploymentResponse(destroyRequest.Component.Id, DeploymentResponseStatus.completed, $"Mq routing rules for tenant {tenantId}, application {applicationId} destroyed.");
	}

	/// <summary>
	/// Delete mq rule by id for application.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="applicationId">The application identifier.</param>
	/// <param name="routeId">The route identifier.</param>
	/// <returns>A Task representing the asynchronous operation.</returns>
	private async Task<bool> InternalDeleteMqRulesByIdAsync(string tenantId, string applicationId, string routeId)
	{
		_logger.LogInformation("UNDEPLOY/DESTROY - mq route - deleting route - TenantId={TenantId} AppId={AppId} RouteId={RouteId}", tenantId, applicationId, routeId);
		var result = await _mqRoutingRulesService.DeleteRoutingRuleByIdAsync(tenantId, applicationId, routeId);
		_logger.LogInformation("UNDEPLOY/DESTROY - mq route - deleted route - TenantId={TenantId} AppId={AppId} Result={Result} RouteId={RouteId}",
			tenantId, applicationId, routeId, result);
		return result > 0;
	}

	/// <summary>
	/// Delete mq rules for application.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="applicationId">The application identifier.</param>
	/// <returns>A Task representing the asynchronous operation.</returns>
	private async Task<bool> InternalDeleteMqRulesForAppAsync(string tenantId, string applicationId)
	{
		_logger.LogInformation("UNDEPLOY/DESTROY - mq route - deleting all routes bound - TenantId={TenantId} AppId={AppId}", tenantId, applicationId);
		var result = await _mqRoutingRulesService.DeleteRoutingRuleInAppAsync(tenantId, applicationId);
		_logger.LogInformation("UNDEPLOY/DESTROY - mq route - deleted all routes bound - TenantId={TenantId} AppId={AppId} Result={Result}",
			tenantId, applicationId, result);
		return result > 0;
	}
}