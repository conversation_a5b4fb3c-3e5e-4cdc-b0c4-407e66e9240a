using MqRouting.Models;

namespace MqRouting.Services;

/// <summary>
/// Interface IMqRoutingRulesService
/// </summary>
public partial interface IMqRoutingRulesService
{
	/// <summary>
	/// Gets all routing rules.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <returns>Task&lt;List&lt;MqRoutingRule&gt;&gt;.</returns>
	Task<List<MqRoutingRule?>> GetAllRoutingRulesAsync(string tenantId, string appId);

	/// <summary>
	/// Gets the routing rule by identifier.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="id">The identifier.</param>
	/// <returns>Task&lt;MqRoutingRule&gt;.</returns>
	Task<MqRoutingRule?> GetRoutingRuleByIdAsync(string tenantId, string appId, string id);

	/// <summary>
	/// Gets the routing rules by type asynchronous.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="type">The type.</param>
	/// <returns>Task&lt;MqRoutingRule&gt;.</returns>
	Task<List<MqRoutingRule?>> GetRoutingRulesByTypeAsync(string tenantId, string appId, string type);

	/// <summary>
	/// Insert/Update the routing rule.
	/// </summary>
	/// <param name="instance">The instance.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	Task<int> UpsertRoutingRuleAsync(MqRoutingRule instance);

	/// <summary>
	/// Deletes the routing rule by identifier.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="id">The identifier.</param>
	/// <returns>Task&lt;System.Int32&gt;.</returns>
	Task<int> DeleteRoutingRuleByIdAsync(string tenantId, string appId, string id);

	/// <summary>
	/// Deletes the routing rule in application.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <returns>Task&lt;System.Int32&gt;.</returns>
	Task<int> DeleteRoutingRuleInAppAsync(string tenantId, string appId);

	/// <summary>
	/// Queries the routing rules.
	/// </summary>
	/// <param name="sql">The SQL.</param>
	/// <param name="param">The parameter.</param>
	/// <returns>Task&lt;List&lt;MqRoutingRule&gt;&gt;.</returns>
	Task<List<MqRoutingRule?>> QueryRoutingRulesAsync(string sql, object? param);
}