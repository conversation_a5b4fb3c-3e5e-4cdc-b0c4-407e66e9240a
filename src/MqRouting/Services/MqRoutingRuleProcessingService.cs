namespace MqRouting.Services;

/// <summary>
/// Class MqRoutingRuleProcessingService.
/// </summary>
/// <remarks>Initializes a new instance of the <see cref="MqRoutingRuleProcessingService" /> class.</remarks>
public sealed partial class MqRoutingRuleProcessingService(ILogger<MqRoutingRuleProcessingService> _logger,
	IMqRoutingRulesService _ruleService, IServiceProvider _serviceProvider) : IMqRoutingRuleProcessingService
{

}