using Common.Postgres;
using Dapper;
using MqRouting.Models;
using System.Text.Json;

namespace MqRouting.Services;

/// <summary>
/// Class MqRoutingRulesService.
/// </summary>
public sealed partial class MqRoutingRulesService(IPgRepository2 _pgRepository) : IMqRoutingRulesService
{
	/// <summary>
	/// Initializes static members of the <see cref="MqRoutingRulesService"/> class.
	/// </summary>
	static MqRoutingRulesService()
	{
		SqlMapper.AddTypeHandler(new JsonTypeHandler<JsonDocument>());
		SqlMapper.AddTypeHandler(new JsonTypeHandler<List<string>>());
		SqlMapper.AddTypeHandler(new JsonTypeHandler<InAppAlertInfo>());
		SqlMapper.AddTypeHandler(new JsonTypeHandler<EmailAlertInfo>());
		SqlMapper.AddTypeHandler(new JsonTypeHandler<SmsAlertInfo>());
		SqlMapper.AddTypeHandler(new JsonTypeHandler<WorkflowAlertInfo>());
		SqlMapper.AddTypeHandler(new JsonTypeHandler<WebhookAlertInfo>());
		SqlMapper.AddTypeHandler(new JsonTypeHandler<List<ConditionParameter>>());
		SqlMapper.AddTypeHandler(new JsonTypeHandler<List<NavigationVariableInfo>>());
	}
}
