using Apex.Models;
using Apex.Services;
using Prometheus;

namespace MqRouting.Services;

/// <summary>
/// Class MqRoutingRuleProcessingService.
/// </summary>
public sealed partial class MqRoutingRuleProcessingService
{
	/// <summary>
	/// The metrics no of messages processed
	/// </summary>
	private static readonly Counter METRICS_NOOFMESSAGESPROCESSED = Metrics
		.CreateCounter("message_routing_total_no_of_messages_processed", "Total number of messages processed.");

	/// <summary>
	/// The metrics no of messages processed - successfully
	/// </summary>
	private static readonly Counter METRICS_NOOFMESSAGESPROCESSED_SUCCESSFULLY = Metrics
		.CreateCounter("message_routing_total_no_of_messages_processed_success", "Total number of messages processed successfully.");

	/// <summary>
	/// The metrics no of messages failed processing
	/// </summary>
	private static readonly Counter METRICS_NOOFMESSAGESPROCESSED_FAILED = Metrics
		.CreateCounter("message_routing_total_no_of_messages_processed_fail", "Total number of messages failed processing.");

	/// <summary>
	/// The metrics for time taken to lookup rules matching message in MS
	/// </summary>
	private static readonly Histogram METRICS_RULESLOOKUP_DURATION_MS = Metrics
		.CreateHistogram("message_routing_ruleslookup_duration_ms", "Time taken to lookup alert defs matching message in milliseconds.");

	/// <summary>
	/// The metrics for time taken to process a message in MS
	/// </summary>
	private static readonly Histogram METRICS_PROCESSMESSAGE_DURATION_MS = Metrics
		.CreateHistogram("message_routing_processmessage_duration_ms", "Time taken to process a message in milliseconds.");

	/// <summary>
	/// Processes the message.
	/// </summary>
	/// <param name="topicName">Name of the topic.</param>
	/// <param name="message">The message.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	public async Task<(bool, MessageNack)> ProcessAlertAsync(string topicName, Message message, CancellationToken cancellationToken = default)
	{
		METRICS_NOOFMESSAGESPROCESSED.Inc();
		try
		{
			var auth = message.Auth;

			// collect all the rules matching type
			List<Models.MqRoutingRule?> matchingRules;
			using (var timer = METRICS_RULESLOOKUP_DURATION_MS.NewTimer())
			{
				matchingRules = await _ruleService.GetRoutingRulesByTypeAsync(auth.TenantId, auth.AppId, message.Type);
				_logger.LogDebug("Running match rules - Message={TopicName}/{Id} Rules={Rules}",
					topicName, message.Id, matchingRules.Select(m => m.name).JoinWith());
			}

			using (var timer = METRICS_PROCESSMESSAGE_DURATION_MS.NewTimer())
			{
				foreach (var matchingRule in matchingRules.Where(r => r is not null))
				{
					_logger.LogDebug("Running match rule - Message={TopicName}/{Id} Rule={Rule}/{RuleName}",
						topicName, message.Id, matchingRule!.id, matchingRule.name);

					Models.BaseAlertInfo?[] alerts = [matchingRule.inappinfo, matchingRule.emailinfo, matchingRule.smsinfo, matchingRule.webhookinfo, matchingRule.workflowinfo];
					foreach (var notification in alerts.Where(n => n is not null))
					{
						_logger.LogDebug("Running match rule - for channel - Message={TopicName}/{Id} Rule={Rule}/{RuleName} Channel={Channel}",
							topicName, message.Id, matchingRule.id, matchingRule.name, notification.Name);

						try
						{
							var ctHandler = _serviceProvider.GetRequiredKeyedService<IChannelTypeHandler>(notification.GetType().Name);
							await ctHandler.ProcessMessageAsync(message, matchingRule, cancellationToken);
						}
						catch (Exception exNotification)
						{
							_logger.LogError(exNotification, "ERROR Running match rule - for channel - Message={TopicName}/{Id} Rule={Rule}/{RuleName} Channel={Channel} Error={Error}",
								topicName, message.Id, matchingRule.id, matchingRule.name, notification.Name, exNotification.Message);
						}
					}
				}
			}

			METRICS_NOOFMESSAGESPROCESSED_SUCCESSFULLY.Inc();
			return MessageAcknowledgement.SUCCESS;
		}
		catch (Exception ex)
		{
			METRICS_NOOFMESSAGESPROCESSED_FAILED.Inc();
			_logger.LogError(ex, "ERROR processing message - TopicName={TopicName} Id={Id} Error={Error}", topicName, message.Id, ex.Message);
			return MessageAcknowledgement.FAIL_REQUEUE;
		}
	}

	/// <summary>
	/// Processes the message.
	/// </summary>
	/// <param name="topicName">Name of the topic.</param>
	/// <param name="message">The message.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	public async Task<(bool, MessageNack)> ProcessBroadcastAlertAsync(string topicName, BroadcastAlertAndDef message, CancellationToken cancellationToken = default)
	{
		METRICS_NOOFMESSAGESPROCESSED.Inc();
		var alert = message.Message;
		try
		{
			_logger.LogInformation("processing broadcast message - TopicName={TopicName} Id={Id}", topicName, alert.Id);

			METRICS_NOOFMESSAGESPROCESSED_SUCCESSFULLY.Inc();
			return MessageAcknowledgement.SUCCESS;
		}
		catch (Exception ex)
		{
			METRICS_NOOFMESSAGESPROCESSED_FAILED.Inc();
			_logger.LogError(ex, "ERROR processing message - TopicName={TopicName} Id={Id} Error={Error}", topicName, alert.Id, ex.Message);
			return MessageAcknowledgement.FAIL_REQUEUE;
		}
	}
}