using Apex.Models;
using Apex.Services;

namespace MqRouting.Services;

/// <summary>
/// class BaseChannelTypeHandler
/// </summary>
public abstract class BaseChannelTypeHandler(ILogger _logger, IMqSendService _mqSendService, string _queueName) : IChannelTypeHandler
{
	/// <summary>
	/// Processes the message.
	/// </summary>
	/// <param name="message">The message.</param>
	/// <param name="rule">The rule.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.ValueTuple&lt;System.Boolean, MessageNack&gt;&gt;.</returns>
	public async Task<(bool, MessageNack)> ProcessMessageAsync(Message message, Models.MqRoutingRule rule, CancellationToken cancellationToken = default)
	{
		_logger.LogInformation("Forwarding it to channel queue - Queue={Queue} MessageId={MessageId} Rule={RuleId}/{RuleName}", _queueName, message.Id, rule.id, rule.name);
		try
		{
			await _mqSendService.PostMessageAsync
			(
				_queueName,
				new Message
				(
					message.Id, message.Type, new AlertAndDef(message, rule).ToJsonDocument(),
					Auth: message.Auth,
					Metadata: message.Metadata
				),
				true
			);
			return MessageAcknowledgement.SUCCESS;
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Forwarding it to channel queue failed - Queue={Queue} MessageId={MessageId} Rule={RuleId}/{RuleName} Error={Error}",
				_queueName, message.Id, rule.id, rule.name, ex.Message);
			return MessageAcknowledgement.FAIL_REQUEUE;
		}
	}
}