using Apex.Options;
using Apex.Services;
using Common.Postgres;
using Common.Postgres.Options;
using MqRouting.Models;
using MqRouting.Options;
using MqRouting.Services;

namespace System;

/// <summary>
/// class ServiceExtensions
/// </summary>
public static class ServiceExtensions
{
	/// <summary>
	/// Adds the Mq Routing needed services.
	/// </summary>
	/// <param name="services">The services.</param>
	/// <param name="configuration">The configuration.</param>
	/// <returns>Task&lt;System.Nullable&lt;MqRoutingResponse&gt;&gt;.</returns>
	public static IServiceCollection AddMqRouting(this IServiceCollection services, IConfiguration configuration)
	{
		services.AddOptions<IamOptions>().Bind(configuration.GetRequiredSection("IAM"));
		services.AddOptions<SecureIamOptions>().Bind(configuration);
		services.AddOptions<DeploymentOptions>().Bind(configuration.GetRequiredSection("DeploymentOptions"));

		var mqRoutingOptions = configuration.GetRequiredSection("MqRoutingOptions");
		services.AddOptions<MqRoutingOptions>().Bind(mqRoutingOptions);
		services.AddOptions<SecurePgOptions>().Bind(configuration);

		services.AddOptions<ServiceOptions>().Bind(configuration.GetRequiredSection("ServiceOptions"));
		services.AddWebConnectionExtensions();
		services.AddSingleton<IJiffyDriveRestService, JiffyDriveRestService>();
		services.AddSingleton<IMessengerRestService, MessengerRestService>();
		services.AddSingleton<IWorkflowRestService, WorkflowRestService>();
		services.AddSingleton<ITokenGenerationService, PlatformTokenGenerationService>();
		services.AddSingleton<IAppManagerService, AppManagerService>();
		services.AddSingleton<ICelToJsService, CelToJsService>();

		services.AddSingleton<IPgRepository2, PgRepository2>();
		services.AddTransient<IMqRoutingRulesService, MqRoutingRulesService>();
		services.AddTransient<IMqRoutingMediatedService, MqRoutingMediatedService>();
		services.AddTransient<IMqRoutingRuleProcessingService, MqRoutingRuleProcessingService>();

		services.AddKeyedScoped<IChannelTypeHandler, InAppChannelTypeHandler>(typeof(InAppAlertInfo).Name);
		services.AddKeyedScoped<IChannelTypeHandler, EmailChannelTypeHandler>(typeof(EmailAlertInfo).Name);
		services.AddKeyedScoped<IChannelTypeHandler, SmsChannelTypeHandler>(typeof(SmsAlertInfo).Name);
		services.AddKeyedScoped<IChannelTypeHandler, WebHookChannelTypeHandler>(typeof(WebhookAlertInfo).Name);
		services.AddKeyedScoped<IChannelTypeHandler, WorkflowChannelTypeHandler>(typeof(WorkflowAlertInfo).Name);

		// ordering matters
		services.AddHostedService<SchemaCreatorBackgroundService>();

		// add default queues to listen for alerts
		foreach (var channel in CHANNELS)
		{
			services.AddSingleton<IHostedService>(sp => new AlertsQueueBackgroundService(sp.GetRequiredService<ILogger<AlertsQueueBackgroundService>>(), sp, channel.Key));
		}
		services.AddHostedService<BroadcastAlertsBackgroundService>();

		return services.AddMq(configuration);
	}

	/// <summary>
	/// The channels
	/// </summary>
	static readonly Dictionary<string, Type> CHANNELS = new()
	{
		{ "default", typeof(AlertsQueueBackgroundService) },
		{ "alerts", typeof(AlertsQueueBackgroundService) }
	};
}