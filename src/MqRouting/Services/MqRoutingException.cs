namespace MqRouting.Services;

using System.Runtime.Serialization;

/// <summary>
/// Class MqRoutingException.
/// Implements the <see cref="System.Exception" />
/// </summary>
/// <seealso cref="System.Exception" />
[Serializable]
public sealed partial class MqRoutingException : Exception
{
	/// <summary>
	/// Initializes a new instance of the <see cref="MqRoutingException" /> class.
	/// </summary>
	/// <param name="errorMessage">The error message.</param>
	/// <returns>InvalidStudentNameException.</returns>
	public MqRoutingException(string errorMessage)
		: base(errorMessage)
	{
	}

	/// <summary>
	/// Initializes a new instance of the <see cref="MqRoutingException" /> class.
	/// </summary>
	/// <param name="serializationInfo">The serialization information.</param>
	/// <param name="streamingContext">The streaming context.</param>
	private MqRoutingException(SerializationInfo serializationInfo, StreamingContext streamingContext)
		: base(serializationInfo, streamingContext)
	{
	}
}