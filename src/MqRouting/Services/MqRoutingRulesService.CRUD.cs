using Common.Postgres;
using MqRouting.Models;

namespace MqRouting.Services;

/// <summary>
/// Class MqRoutingRulesService.
/// </summary>
public sealed partial class MqRoutingRulesService
{
	/// <summary>
	/// Gets the routing rules.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <returns>Task&lt;IEnumerable&lt;MqRoutingRule&gt;&gt;.</returns>
	public Task<List<MqRoutingRule?>> GetAllRoutingRulesAsync(string tenantId, string appId)
		=> _pgRepository.GetAllAsync<MqRoutingRule>(new TenantAppScopedObject { tenant_id = tenantId, app_id = appId });

	/// <summary>
	/// Gets the routing rules by type asynchronous.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="type">The type.</param>
	/// <returns>Task&lt;MqRoutingRule&gt;.</returns>
	public Task<List<MqRoutingRule?>> GetRoutingRulesByTypeAsync(string tenantId, string appId, string type)
		=> _pgRepository.QueryAsync<MqRoutingRule>(
$$"""
   SELECT * FROM mq_routing_rules WHERE tenant_id=@tenantId AND app_id=@appId AND type=@type
""",
			param: new
			{
				tenantId,
				appId,
				type
			});

	/// <summary>
	/// Gets the routing rule by identifier.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="id">The identifier.</param>
	/// <returns>Task&lt;MqRoutingRule&gt;.</returns>
	public Task<MqRoutingRule?> GetRoutingRuleByIdAsync(string tenantId, string appId, string id)
		=> _pgRepository.GetByIdAsync<MqRoutingRule>(new TenantAppScopedObjectWithId { tenant_id = tenantId, app_id = appId, id = id });

	/// <summary>
	/// Updates the routing rule.
	/// </summary>
	/// <param name="instance">The routing rule.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	public Task<int> UpsertRoutingRuleAsync(MqRoutingRule instance)
		=> _pgRepository.ExecuteNonQueryAsync(UPSERT_MQROUTINGRULE, instance);

	const string UPSERT_MQROUTINGRULE =
	"""
	INSERT INTO "mq_routing_rules"
	(
		"tenant_id", "app_id", "id", "name", "type", "description", "category", "tags",
		"is_enabled", "enable_condition", "condition_parameters", "json_schema", "sample", "inappinfo", "emailinfo", "smsinfo", "workflowinfo", "webhookinfo",
		"recipient_roles", "recipient_orgs", "can_opt_out", "recipients_required", "nav_pageurlexpression", "nav_variables"
	)
	VALUES
	(
		@tenant_id, @app_id, @id, @name, @type, @description, @category, @tags,
		@is_enabled, @enable_condition, @condition_parameters, @json_schema, @sample, @inappinfo, @emailinfo, @smsinfo, @workflowinfo, @webhookinfo,
		@recipient_roles, @recipient_orgs, @can_opt_out, @recipients_required, @nav_pageurlexpression, @nav_variables
	)
	ON CONFLICT ("tenant_id", "app_id", "id")
	DO UPDATE SET
		"name"=EXCLUDED."name", "type"=EXCLUDED.type, "description"=EXCLUDED.description, "category"=EXCLUDED.category, "tags"=EXCLUDED.tags,
		"is_enabled"=EXCLUDED.is_enabled, "enable_condition"=EXCLUDED.enable_condition, "condition_parameters"=EXCLUDED.condition_parameters, "json_schema"=EXCLUDED.json_schema, "sample"=EXCLUDED.sample,
		"inappinfo"=EXCLUDED.inappinfo, "emailinfo"=EXCLUDED.emailinfo, "smsinfo"=EXCLUDED.smsinfo, "workflowinfo"=EXCLUDED.workflowinfo, "webhookinfo"=EXCLUDED.webhookinfo, 
		"recipient_roles"=EXCLUDED.recipient_roles, "recipient_orgs"=EXCLUDED.recipient_orgs, "can_opt_out"=EXCLUDED.can_opt_out, "recipients_required"=EXCLUDED.recipients_required,
		"nav_pageurlexpression"=EXCLUDED.nav_pageurlexpression, "nav_variables"=EXCLUDED.nav_variables
	""";

	/// <summary>
	/// Deletes the routing rule.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="id">The identifier.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	public Task<int> DeleteRoutingRuleByIdAsync(string tenantId, string appId, string id)
		=> _pgRepository.DeleteByIdAsync<MqRoutingRule>(new TenantAppScopedObjectWithId { tenant_id = tenantId, app_id = appId, id = id });

	/// <summary>
	/// Deletes the routing rules in application.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <returns>Task&lt;System.Int32&gt;.</returns>
	public Task<int> DeleteRoutingRuleInAppAsync(string tenantId, string appId)
		=> _pgRepository.DeleteAllAsync<MqRoutingRule>(new TenantAppScopedObject { tenant_id = tenantId, app_id = appId });

	/// <summary>
	/// Queries the routing rules.
	/// </summary>
	/// <param name="sql">The SQL.</param>
	/// <param name="param">The parameter.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	public Task<List<MqRoutingRule?>> QueryRoutingRulesAsync(string sql, object? param)
		=> _pgRepository.QueryAsync<MqRoutingRule>(sql, param);
}