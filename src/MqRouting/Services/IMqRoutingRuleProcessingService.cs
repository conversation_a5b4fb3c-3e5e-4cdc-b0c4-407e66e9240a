using Apex.Models;
using Apex.Services;

namespace MqRouting.Services;

/// <summary>
/// Interface IMqRoutingRuleProcessingService
/// </summary>
public partial interface IMqRoutingRuleProcessingService
{
	/// <summary>
	/// Processes the message.
	/// </summary>
	/// <param name="topicName">Name of the topic.</param>
	/// <param name="message">The message.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	Task<(bool, MessageNack)> ProcessAlertAsync(string topicName, Message message, CancellationToken cancellationToken = default);

	/// <summary>
	/// Processes the broadcast alert.
	/// </summary>
	/// <param name="topicName">Name of the topic.</param>
	/// <param name="message">The message.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.ValueTuple&lt;System.Boolean, MessageNack&gt;&gt;.</returns>
	Task<(bool, MessageNack)> ProcessBroadcastAlertAsync(string topicName, BroadcastAlertAndDef message, CancellationToken cancellationToken = default);
}