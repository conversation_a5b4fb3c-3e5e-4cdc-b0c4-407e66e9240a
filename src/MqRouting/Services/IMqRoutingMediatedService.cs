using Ai.Jiffy.Models.Mediator.In;
using Ai.Jiffy.Models.Mediator.Out;

namespace MqRouting.Services;

/// <summary>
/// Interface IMqRoutingMediatedService
/// </summary>
public interface IMqRoutingMediatedService
{
	/// <summary>
	/// Publishes the routes to the target.
	/// </summary>
	/// <param name="publishBody">The publish body.</param>
	/// <param name="memoryStream">The memory stream.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	Task PublishAsync(PublishBody publishBody, MemoryStream memoryStream, CancellationToken cancellationToken = default);

	/// <summary>
	/// Deploys the routes to the target app.
	/// </summary>
	/// <param name="deploymentRequest">The deployment request.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	Task<DeploymentResponse> DeployAsync(DeploymentRequest deploymentRequest, CancellationToken cancellationToken = default);

	/// <summary>
	/// Deploys the route to the target app.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="applicationId">The application identifier.</param>
	/// <param name="userId"></param>
	/// <param name="routeId">The routing identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	Task<bool> DeployAsync(string tenantId, string applicationId, string userId, string routeId, CancellationToken cancellationToken = default);

	/// <summary>
	/// Undeploys a route from the target app.
	/// </summary>
	/// <param name="undeployRequest">The undeploy request.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	Task<DeploymentResponse> UndeployAsync(UndeployRequest undeployRequest, CancellationToken cancellationToken = default);

	/// <summary>
	/// Undeploys routes from the target app.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="applicationId">The application identifier.</param>
	/// <param name="routeId">The routing identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	Task<bool> UndeployAsync(string tenantId, string applicationId, string routeId, CancellationToken cancellationToken = default);

	/// <summary>
	/// Destroys the routes in the target app.
	/// </summary>
	/// <param name="destroyRequest">The destroy request.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	Task<DeploymentResponse> DestroyAsync(DestroyRequest destroyRequest, CancellationToken cancellationToken = default);
}