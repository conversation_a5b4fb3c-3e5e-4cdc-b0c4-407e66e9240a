<Project Sdk="Microsoft.NET.Sdk.Web">
	<Import Project="../Build.Executable.properties" />

	<PropertyGroup>
		<AssemblyName>Net.Service.MqRouting</AssemblyName>
		<RootNamespace>MqRouting</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="RabbitMq.Client" Version="7.1.2" />
		<PackageReference Include="Asp.Versioning.Mvc" Version="8.1.0" />
		<PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
		<PackageReference Include="Google.Protobuf" Version="3.32.1" />
		<PackageReference Include="Grpc.Net.ClientFactory" Version="2.71.0" />
		<PackageReference Include="Grpc.Tools" Version="2.72.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Net.Common" Version="3.0.2" />
		<PackageReference Include="Net.Common.AppModels" Version="3.0.9" />
		<!--<ProjectReference Include="..\..\..\net-common-appmodels\src\AppModels\AppModels.csproj" />-->
		<PackageReference Include="Net.Common.Mq" Version="3.0.15" />
		<PackageReference Include="Net.Common.PgRepository2" Version="3.0.19" />
		<!--<ProjectReference Include="..\..\..\net-common-repository\src\Common.PgRepository2\Common.PgRepository2.csproj" />-->
		<PackageReference Include="Net.Service.Common" Version="3.0.50" />
		<!--<ProjectReference Include="..\..\..\net-service-common\src\Service.Common\Service.Common.csproj" />-->
	</ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="MqRoutingTest" />
	</ItemGroup>

	<ItemGroup>
		<Compile Update="Models\MqRoutingRule.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\IMqRoutingMediatedService.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\MqRoutingMediatedService.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\ChannelTypeHandler\BaseChannelTypeHandler.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\ChannelTypeHandler\SmsChannelTypeHandler.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\ChannelTypeHandler\WebHookChannelTypeHandler.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\ChannelTypeHandler\WorkflowChannelTypeHandler.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
	</ItemGroup>

	<ItemGroup>
		<Compile Update="Properties\Resources.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>Resources.resx</DependentUpon>
		</Compile>
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Update="Properties\Resources.resx">
			<Generator>ResXFileCodeGenerator</Generator>
			<LastGenOutput>Resources.Designer.cs</LastGenOutput>
		</EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
		<None Update="appsettings.yaml">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="appsettings.Development.yaml" Condition="$(Configuration) == 'Debug'">
			<DependentUpon>appsettings.yaml</DependentUpon>
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>
	<ItemGroup Condition="$(Configuration) == 'Debug'">
		<None Update="up.cmd">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>
	<ItemGroup>
		<Folder Include="Controllers\Examples\" />
	</ItemGroup>

	<Target Name="CopyToSecretsFolder" AfterTargets="AfterBuild" Condition="$(Configuration) == 'Debug'">
		<Message Importance="high" Text="copying db.properties =&gt; $(USERPROFILE)\secrets-store" />
		<Copy SourceFiles="db.properties" DestinationFolder="$(USERPROFILE)\secrets-store" SkipUnchangedFiles="true" ContinueOnError="false" />

		<Message Importance="high" Text="copying principal.properties =&gt; $(USERPROFILE)\secrets-store" />
		<Copy SourceFiles="principal.properties" DestinationFolder="$(USERPROFILE)\secrets-store" SkipUnchangedFiles="true" ContinueOnError="false" />

		<Message Importance="high" Text="copying extra.properties =&gt; $(USERPROFILE)\secrets-store" />
		<Copy SourceFiles="extra.properties" DestinationFolder="$(USERPROFILE)\secrets-store" SkipUnchangedFiles="true" ContinueOnError="false" />
	</Target>
</Project>