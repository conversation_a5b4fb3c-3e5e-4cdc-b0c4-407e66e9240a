using Apex;
using Asp.Versioning.ApiExplorer;

namespace MqRouting;

/// <summary>
/// Class Startup.
/// </summary>
public sealed partial class Startup : BaseServiceStartup<Startup>
{
	/// <summary>
	/// Initializes a new instance of the <see cref="Startup" /> class.
	/// </summary>
	/// <param name="configuration">The configuration.</param>
	/// <param name="webHostEnvironment">The web host environment.</param>
	public Startup(IConfiguration configuration, IHostEnvironment webHostEnvironment)
		: base(configuration, webHostEnvironment, new ServiceStartupInfo
		(
			"Mq Routing",
			"Mq Routing - Enables platform components to process incoming messages and route to other streams/queues using rules + optional condition",
			"*MqRouting.xml",
			"/mq/routing",
			TelemetryInfo: new OpenTelemetryInfo
			(
				ServiceName: "mqrouting"
			)
		))
	{
	}

	/// <summary>
	/// Configures the services.
	/// </summary>
	/// <param name="services">The services.</param>
	public void ConfigureServices(IServiceCollection services)
	{
		BaseConfigureServices(services);
		services.AddMqRouting(Configuration);
	}

	/// <summary>
	/// Configures the specified application.
	/// </summary>
	/// <param name="app">The application.</param>
	/// <param name="provider">The provider.</param>
	public void Configure(IApplicationBuilder app, IApiVersionDescriptionProvider provider)
	{
		BaseConfigure(app, provider);
	}
}