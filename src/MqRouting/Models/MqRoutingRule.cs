using Dapper.Contrib.Extensions;
using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace MqRouting.Models;

/// <summary>
/// Class AlertInfo.
/// </summary>
[JsonPolymorphic()]
[JsonDerivedType(typeof(WorkflowAlertInfo), "workflow")]
[JsonDerivedType(typeof(InAppAlertInfo), "inapp")]
[JsonDerivedType(typeof(SmsAlertInfo), "sms")]
[JsonDerivedType(typeof(EmailAlertInfo), "email")]
[JsonDerivedType(typeof(WebhookAlertInfo), "webhook")]
public abstract record BaseAlertInfo
(
	string Name,
	bool IsEnabled
);

/// <summary>
/// Class WorkflowAlertInfo.
/// </summary>
public sealed record WorkflowAlertInfo
(
	string Name,
	bool IsEnabled,
	string WorkflowDefId,
	string Variable
) : BaseAlertInfo(Name, IsEnabled);

/// <summary>
/// Class InAppAlertInfo.
/// </summary>
public sealed record InAppAlertInfo
(
	string Name,
	bool IsEnabled,
	string DefaultTemplateId,
	string? UsersExpression
) : BaseAlertInfo(Name, IsEnabled);

/// <summary>
/// Class SmsAlertInfo.
/// </summary>
public sealed record SmsAlertInfo
(
	string Name,
	bool IsEnabled,
	string Outbox,
	string DefaultTemplateId
) : BaseAlertInfo(Name, IsEnabled);

/// <summary>
/// Class EmailAlertInfo.
/// </summary>
public sealed record EmailAlertInfo
(
	string Name,
	bool IsEnabled,
	string Outbox,
	string DefaultTemplateId
) : BaseAlertInfo(Name, IsEnabled);

/// <summary>
/// Class WebhookAlertInfo.
/// </summary>
public sealed record WebhookAlertInfo
(
	string Name,
	bool IsEnabled,
	string Url,
	Dictionary<string, string> Headers
) : BaseAlertInfo(Name, IsEnabled);

/// <summary>
/// Class ConditionParameter. This class cannot be inherited.
/// </summary>
public sealed record ConditionParameter
(
	string Name,
	string? DefaultValue = null
);

/// <summary>
/// Class NavigationVariableInfo. This class cannot be inherited.
/// </summary>
public sealed record NavigationVariableInfo
(
	string Name,
	string? ValueExpression
);


/// <summary>
/// Class MqRoutingRule.
/// </summary>
[Table("mq_routing_rules")]
public sealed partial class MqRoutingRule : Common.Postgres.TenantAppScopedObjectWithId
{
	/// <summary>
	/// Gets the name.
	/// </summary>
	/// <value>The name.</value>
	public string name { get; init; }

	/// <summary>
	/// Gets the type.
	/// </summary>
	/// <value>The type.</value>
	public string type { get; init; }

	/// <summary>
	/// Gets the description.
	/// </summary>
	/// <value>The description.</value>
	public string? description { get; init; }

	/// <summary>
	/// Gets the category.
	/// </summary>
	/// <value>The category.</value>
	[Required]
	public string? category { get; init; }

	/// <summary>
	/// Gets the tags.
	/// </summary>
	/// <value>The tags.</value>
	public List<string>? tags { get; init; }

	/// <summary>
	/// Gets or sets a value indicating whether this instance is enabled.
	/// </summary>
	/// <value><c>true</c> if this instance is enabled; otherwise, <c>false</c>.</value>
	[JsonPropertyName("isEnabled")]
	public bool is_enabled { get; set; }

	/// <summary>
	/// Gets the enable condition.
	/// </summary>
	/// <value>The enable expression.</value>
	[JsonPropertyName("enableCondition")]
	public string? enable_condition { get; init; }

	/// <summary>
	/// Gets the condition parameters.
	/// </summary>
	/// <value>The condition parameters.</value>
	[JsonPropertyName("conditionParameters")]
	public List<ConditionParameter>? condition_parameters { get; init; }

	/// <summary>
	/// Gets the json schema.
	/// </summary>
	/// <value>The json schema.</value>
	[JsonPropertyName("jsonSchema")]
	public JsonDocument? json_schema { get; init; }

	/// <summary>
	/// Gets the sample JSON.
	/// </summary>
	/// <value>The sample.</value>
	public JsonDocument? sample { get; init; }

	/// <summary>
	/// Gets the inapp alert.
	/// </summary>
	/// <value>The inapp alert.</value>
	[JsonPropertyName("inAppInfo")]
	public InAppAlertInfo? inappinfo { get; init; }

	/// <summary>
	/// Gets the email alert.
	/// </summary>
	/// <value>The email alert.</value>
	[JsonPropertyName("emailInfo")]
	public EmailAlertInfo? emailinfo { get; init; }

	/// <summary>
	/// Gets the sms alert.
	/// </summary>
	/// <value>The SMS alert.</value>
	[JsonPropertyName("smsInfo")]
	public SmsAlertInfo? smsinfo { get; init; }

	/// <summary>
	/// Gets the workflow alert.
	/// </summary>
	/// <value>The workflow alert.</value>
	[JsonPropertyName("workflowInfo")]
	public WorkflowAlertInfo? workflowinfo { get; init; }

	/// <summary>
	/// Gets the webhook alert.
	/// </summary>
	/// <value>The webhook alert.</value>
	[JsonPropertyName("webhookInfo")]
	public WebhookAlertInfo? webhookinfo { get; init; }

	/// <summary>
	/// Gets the recipient roles.
	/// </summary>
	/// <value>The recipient roles.</value>
	[JsonPropertyName("recipientRoles")]
	public string? recipient_roles { get; init; }

	/// <summary>
	/// Gets the recipient orgs.
	/// </summary>
	/// <value>The recipient orgs.</value>
	[JsonPropertyName("recipientOrgs")]
	public string? recipient_orgs { get; init; }

	/// <summary>
	/// Gets a value indicating whether this instance can opt out.
	/// </summary>
	/// <value><c>null</c> if [can opt out] contains no value, <c>true</c> if [can opt out]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("canOptOut")]
	public bool? can_opt_out { get; init; }

	/// <summary>
	/// Gets a value indicating whether recipients are required.
	/// </summary>
	/// <value><c>null</c> if [recipients required] contains no value, <c>true</c> if [recipients required]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("recipientsRequired")]
	public bool? recipients_required { get; init; }

	/// <summary>
	/// Gets the navigation variables.
	/// </summary>
	/// <value>The navigation variables.</value>
	[JsonPropertyName("navVariables")]
	public List<NavigationVariableInfo>? nav_variables { get; init; }

	/// <summary>
	/// Gets the navigation page url expression.
	/// </summary>
	/// <value>The navigation pageurlexpression.</value>
	[JsonPropertyName("navPageUrlExpression")]
	public string? nav_pageurlexpression { get; init; }

	/// <summary>
	/// Performs an explicit conversion from <see cref="MqRoutingRule"/> to <see cref="Apex.Models.AlertDef"/>.
	/// </summary>
	/// <param name="source">The source.</param>
	/// <returns>The result of the conversion.</returns>
	public static implicit operator Apex.Models.AlertDef(MqRoutingRule source)
	{
		return new Apex.Models.AlertDef
		{
			Id = source.id,
			TenantId = source.tenant_id,
			AppId = source.app_id,
			Name = source.name,
			Type = source.type,
			Description = source.description,
			Category = source.category,
			Tags = source.tags,
			IsEnabled = source.is_enabled,
			EnableCondition = source.enable_condition,
			ConditionParameters = source.condition_parameters?.Select(p => new Apex.Models.ConditionParameter(p.Name, p.DefaultValue)).ToList(),
			CanOptOut = source.can_opt_out.GetValueOrDefault(false),
			JsonSchema = source.json_schema,
			Sample = source.sample,
			RecipientRoles = source.recipient_roles?.Split(','),
			NavigationPageUrlExpression = source.nav_pageurlexpression,
			NavigationVariables = source.nav_variables?.Select(v => new Apex.Models.NavigationVariableInfo(v.Name, v.ValueExpression)).ToList(),
			InAppInfo = source.inappinfo is InAppAlertInfo inapp ? new Apex.Models.InAppAlertInfo { Name = inapp.Name, IsEnabled = inapp.IsEnabled, DefaultTemplateId = inapp.DefaultTemplateId, UsersExpression = inapp.UsersExpression } : null,
			EmailInfo = source.emailinfo is EmailAlertInfo email ? new Apex.Models.EmailAlertInfo { Name = email.Name, IsEnabled = email.IsEnabled, Outbox = email.Outbox, DefaultTemplateId = email.DefaultTemplateId } : null,
			SmsInfo = source.smsinfo is SmsAlertInfo sms ? new Apex.Models.SmsAlertInfo { Name = sms.Name, IsEnabled = sms.IsEnabled, Outbox = sms.Outbox, DefaultTemplateId = sms.DefaultTemplateId } : null,
			WebhookInfo = source.webhookinfo is WebhookAlertInfo wh ? new Apex.Models.WebhookAlertInfo { Name = wh.Name, IsEnabled = wh.IsEnabled, Url = wh.Url, Headers = wh.Headers } : null,
			WorkflowInfo = source.workflowinfo is WorkflowAlertInfo wf ? new Apex.Models.WorkflowAlertInfo { Name = wf.Name, IsEnabled = wf.IsEnabled, WorkflowDefId = wf.WorkflowDefId, Variable = wf.Variable } : null,
		};
	}
}