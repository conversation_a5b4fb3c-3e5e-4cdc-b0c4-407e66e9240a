using Apex.Models;
using Apex.Services;

namespace MqRouting.Services;

/// <summary>
/// Class BroadcastAlertsBackgroundService.
/// </summary>
public sealed partial class BroadcastAlertsBackgroundService(ILogger<BroadcastAlertsBackgroundService> _logger, IServiceProvider _serviceProvider) : BackgroundService
{
	/// <summary>
	/// Execute.
	/// </summary>
	/// <param name="stoppingToken">Triggered when <see cref="M:Microsoft.Extensions.Hosting.IHostedService.StopAsync(System.Threading.CancellationToken)" /> is called.</param>
	/// <returns>A Task representing the asynchronous operation.</returns>
	/// <remarks>See <see href="https://docs.microsoft.com/dotnet/core/extensions/workers">Worker Services in .NET</see> for implementation guidelines.</remarks>
	protected override async Task ExecuteAsync(CancellationToken stoppingToken)
	{
		_logger.LogInformation("starting queue listen - Queue={Queue}", _QUEUENAME);

		// let other services take over and then this start...
		await Task.Yield();

		// create an scoped instance...
		using var scopedServicesCollection = _serviceProvider.CreateScope();
		var scopedServiceProvider = scopedServicesCollection.ServiceProvider;
		var _mqReceiveService = scopedServiceProvider.GetRequiredService<IMqReceiveService>();
		var _mqProcessingService = scopedServiceProvider.GetRequiredService<IMqRoutingRuleProcessingService>();

		await using var _queueConsumer = await _mqReceiveService.StartReceiveAsync<BroadcastAlertAndDef>(_QUEUENAME, true, async message =>
		{
			_logger.LogInformation("RECEIVED MESSAGE - Queue={Queue} Message={Message}", _QUEUENAME, message);
			return await _mqProcessingService.ProcessBroadcastAlertAsync(_QUEUENAME, message);
		});

#if DEBUG
		var tsDelay = TimeSpan.FromMinutes(5);
#else
		var tsDelay = TimeSpan.FromSeconds(5);
#endif
		while (!stoppingToken.IsCancellationRequested)
		{
			await Task.Delay(tsDelay, stoppingToken);
		}

		_logger.LogInformation("STOPPING queue listen - Queue={Queue}", _QUEUENAME);
	}

	/// <summary>
	/// The queue name
	/// </summary>
	const string _QUEUENAME = "broadcastalerts";
}