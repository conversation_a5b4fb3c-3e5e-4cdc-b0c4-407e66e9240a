using Microsoft.AspNetCore.Mvc;
using MqRouting.Models;
using Swashbuckle.AspNetCore.Filters;

namespace MqRouting.Controllers;

/// <summary>
/// Class MqRoutingRulesController.
/// </summary>
public sealed partial class MqRoutingRulesController
{
	/// <summary>
	/// Gets the alertdefs.
	/// </summary>
	/// <param name="type">The type.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("alertdefs")]
	[ProducesResponseType(200, Type = typeof(IEnumerable<MqRoutingRule>))]
	public async Task<IActionResult> GetMqRulesAsync([FromQuery] string? type = null)
	{
		var appId = this.GetCurrentAppId();
		var tenantId = this.GetCurrentTenantId();
		var results =
			type is null
			? await _mqRoutingRulesService.GetAllRoutingRulesAsync(tenantId, appId)
			: await _mqRoutingRulesService.GetRoutingRulesByTypeAsync(tenantId, appId, type);
		return Ok(results);
	}

	/// <summary>
	/// Gets the alertdef by id.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("alertdefs/{id}")]
	[ProducesResponseType(200, Type = typeof(IEnumerable<MqRoutingRule>))]
	public async Task<IActionResult> GetMqRuleByIdAsync(string id)
	{
		var appId = this.GetCurrentAppId();
		var tenantId = this.GetCurrentTenantId();
		return Ok(await _mqRoutingRulesService.GetRoutingRuleByIdAsync(tenantId, appId, id));
	}

	/// <summary>
	/// Insert/Update an alertdef.
	/// </summary>
	/// <param name="alertdef">The alertdef.</param>
	/// <returns>IActionResult.</returns>
	[HttpPost("alertdefs")]
	[HttpPut("alertdefs")]
	[ProducesResponseType(200, Type = typeof(bool))]
	[SwaggerRequestExample(typeof(MqRoutingRule), typeof(CreateExamples))]
	public async Task<IActionResult> UpsertMqRuleAsync([FromBody] MqRoutingRule alertdef)
	{
		_logger.LogDebug("InsertMqRuleAsync - RoutingRule={RoutingRule}", alertdef);

		var instance = new MqRoutingRule
		{
			tenant_id = this.GetCurrentTenantId(),
			app_id = this.GetCurrentAppId(),
			id = alertdef.id,
			name = alertdef.name,
			type = alertdef.type,
			description = alertdef.description,
			category = alertdef.category,
			tags = alertdef.tags,
			enable_condition = alertdef.enable_condition,
			condition_parameters = alertdef.condition_parameters,
			is_enabled = alertdef.is_enabled,
			json_schema = alertdef.json_schema,
			sample = alertdef.sample,
			inappinfo = alertdef.inappinfo,
			emailinfo = alertdef.emailinfo,
			smsinfo = alertdef.smsinfo,
			workflowinfo = alertdef.workflowinfo,
			webhookinfo = alertdef.webhookinfo,
			can_opt_out = alertdef.can_opt_out,
			recipients_required = alertdef.recipients_required,
			recipient_roles = alertdef.recipient_roles,
		};
		return Ok(await _mqRoutingRulesService.UpsertRoutingRuleAsync(instance));
	}

	/// <summary>
	/// Delete alertdef by id.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <returns>IActionResult.</returns>
	[HttpDelete("alertdefs/{id}")]
	[ProducesResponseType(200, Type = typeof(bool))]
	public async Task<IActionResult> DeleteMqRuleByIdAsync([FromRoute] string id)
	{
		_logger.LogDebug("DeleteMqRuleById - Id={Id}", id);
		var appId = this.GetCurrentAppId();
		var tenantId = this.GetCurrentTenantId();
		return Ok(await _mqRoutingRulesService.DeleteRoutingRuleByIdAsync(tenantId, appId, id));
	}
}