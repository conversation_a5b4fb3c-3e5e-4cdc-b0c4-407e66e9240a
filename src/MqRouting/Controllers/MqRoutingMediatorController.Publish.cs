namespace MqRouting.Controllers;

using Ai.Jiffy.Models.Mediator.In;
using Microsoft.AspNetCore.Mvc;
using MqRouting.Models;
using System.IO;

/// <summary>
/// Class MqRoutingMediatorController.
/// </summary>
public sealed partial class MqRoutingMediatorController
{
	/// <summary>
	/// Publish the routes.
	/// </summary>
	/// <param name="publishBody">The publish body.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpPost("publish")]
	public async Task<IActionResult> PublishAsync([FromBody] PublishBody publishBody, CancellationToken cancellationToken)
	{
		var ms = new MemoryStream();
		await _mediatedService.PublishAsync(publishBody, ms, cancellationToken);

		ms.Position = 0;
		return File(ms, "application/zip", $"mqroute_publish_{DateTime.UtcNow.Ticks}.zip");
	}

	/// <summary>
	/// Deploy the routes.
	/// </summary>
	/// <param name="deploymentRequest">The deployment request.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IActionResult&gt; representing the asynchronous operation.</returns>
	[HttpPost("deploy")]
	public async Task<IActionResult> DeployAsync([FromBody] DeploymentRequest deploymentRequest, CancellationToken cancellationToken)
	{
		return Ok(await _mediatedService.DeployAsync(deploymentRequest, cancellationToken));
	}

	/// <summary>
	/// Deploy a route.
	/// </summary>
	/// <param name="route">The route info.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IActionResult&gt; representing the asynchronous operation.</returns>
	[HttpPost("deployroute")]
	public async Task<IActionResult> DeployRouteAsync([FromBody] PublishMqRoutingRule route, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(route.TenantId);
		ArgumentNullException.ThrowIfNull(route.ApplicationId);
		ArgumentNullException.ThrowIfNull(route.RouteId);
		var userId = this.GetCurrentUserId();
		return Ok(await _mediatedService.DeployAsync(route.TenantId, route.ApplicationId, userId, route.RouteId, cancellationToken));
	}

	/// <summary>
	/// Undeploys the routes.
	/// </summary>
	/// <param name="undeployRequest">The undeploy request.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IActionResult&gt; representing the asynchronous operation.</returns>
	[HttpPost("undeploy")]
	public async Task<IActionResult> UndeployAsync([FromBody] UndeployRequest undeployRequest, CancellationToken cancellationToken)
	{
		return Ok(await _mediatedService.UndeployAsync(undeployRequest, cancellationToken));
	}

	/// <summary>
	/// Undeploys the route.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="applicationId">The application identifier.</param>
	/// <param name="routeId">The route identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IActionResult&gt; representing the asynchronous operation.</returns>
	[HttpPost("undeployroute/{tenantId}/{applicationId}/{routeId}")]
	public async Task<IActionResult> UndeployAsync([FromRoute] string tenantId, [FromRoute] string applicationId, [FromRoute] string routeId, CancellationToken cancellationToken)
	{
		return Ok(await _mediatedService.UndeployAsync(tenantId, applicationId, routeId, cancellationToken));
	}

	/// <summary>
	/// Destroys/undeploys the routes.
	/// </summary>
	/// <param name="destroyRequest">The destroy request.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IActionResult&gt; representing the asynchronous operation.</returns>
	[HttpPost("destroy")]
	public async Task<IActionResult> DestroyAsync([FromBody] DestroyRequest destroyRequest, CancellationToken cancellationToken)
	{
		return Ok(await _mediatedService.DestroyAsync(destroyRequest, cancellationToken));
	}
}