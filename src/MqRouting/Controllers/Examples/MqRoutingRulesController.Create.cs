using MqRouting.Models;
using Swashbuckle.AspNetCore.Filters;
using System.Diagnostics.CodeAnalysis;

namespace MqRouting.Controllers;

/// <summary>
/// Class MqRoutingRulesController.
/// </summary>
public sealed partial class MqRoutingRulesController
{
	/// <summary>
	/// Examples
	/// </summary>
	[ExcludeFromCodeCoverage]
	internal sealed class CreateExamples : IMultipleExamplesProvider<MqRoutingRule>
	{
		/// <summary>
		/// Gets the examples.
		/// </summary>
		public IEnumerable<SwaggerExample<MqRoutingRule>> GetExamples()
		{
			var EXAMPLES = new[]
			{
				(Name: "invoice1_inapp", Value: new MqRoutingRule
				{
					id = "invoice1_inapp",
					tenant_id = "1",
					app_id = "1",
					name = "invoice created",
					type = "invoice.created",
					category = "System",
					description = "lorem ipsum",
					enable_condition = null,
					is_enabled = true,
					json_schema = null,
					sample = null,
					inappinfo = new InAppAlertInfo("inapp1", true, "tinapp1", null),
				}),
				(Name: "invoice1_email", Value: new MqRoutingRule
				{
					id = "invoice1_email",
					tenant_id = "1",
					app_id = "1",
					name = "invoice created",
					type = "invoice.created",
					category = "System",
					description = "lorem ipsum",
					enable_condition = null,
					is_enabled = true,
					json_schema = null,
					sample = null,
					emailinfo = new EmailAlertInfo("email1", true, "outbox1", "temail1"),
				}),
				(Name: "invoice1_wh", Value: new MqRoutingRule
				{
					id = "invoice1_wh",
					tenant_id = "1",
					app_id = "1",
					name = "invoice created",
					type = "invoice.created",
					category = "System",
					description = "lorem ipsum",
					enable_condition = null,
					is_enabled = true,
					json_schema = null,
					sample = null,
					webhookinfo = new WebhookAlertInfo("wh1", true, "https://public.requestbin.com/r/en2xzedujtgn",
						new()
						{
							["clientId"] = "c1111xxxxxxxxxxxxxxxxxxxxxx",
							["clientSecret"] = "c1111secretyyyyyyyyyyyyyyyyyyyyyy",
						})
				}),
				(Name: "invoice1_sms", Value: new MqRoutingRule
				{
					id = "invoice1_sms",
					tenant_id = "1",
					app_id = "1",
					name = "invoice created",
					type = "invoice.created",
					category = "System",
					description = "lorem ipsum",
					enable_condition = null,
					is_enabled = true,
					json_schema = null,
					sample = null,
					smsinfo = new SmsAlertInfo("sms1", true, "smsoutbox1", "tsms1"),
				}),
				(Name: "invoice1_all", Value: new MqRoutingRule
				{
					id = "invoice1_all",
					tenant_id = "1",
					app_id = "1",
					name = "invoice created",
					type = "invoice.created",
					category = "System",
					tags = ["Invoice Management"],
					description = "lorem ipsum",
					enable_condition = null,
					is_enabled = true,
					json_schema = null,
					sample = null,
					emailinfo = new EmailAlertInfo("email1", true, "outbox1", "temail1"),
					smsinfo = new SmsAlertInfo("sms1", true, "smsoutbox1", "tsms1"),
					inappinfo = new InAppAlertInfo("inapp1", true, "tinapp1", null),
					webhookinfo = new WebhookAlertInfo("wh1", true, "https://public.requestbin.com/r/en2xzedujtgn",
						new()
						{
							["clientId"] = "c1111xxxxxxxxxxxxxxxxxxxxxx",
							["clientSecret"] = "c1111secretyyyyyyyyyyyyyyyyyyyyyy",
						}),
				})
			};

			return EXAMPLES.Select(ex => new SwaggerExample<MqRoutingRule>
			{
				Name = ex.Name,
				Value = ex.Value
			});
		}
	}
}