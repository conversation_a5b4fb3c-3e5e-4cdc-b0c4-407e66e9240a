using Apex.Service.AuthHandlers.Scheme;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MqRouting.Services;

namespace MqRouting.Controllers;

/// <summary>
/// Class MqRoutingRulesController.
/// </summary>
/// <remarks>Initializes a new instance of the <see cref="MqRoutingRulesController" /> class.</remarks>
[Authorize(AuthenticationSchemes = JiffyAuthSchemeConstants.JiffyAuthScheme)]
[ApiController]
[Route("v{version:apiVersion}")]
[Produces("application/json")]
[ProducesResponseType(typeof(ProblemDetails), 500)]
[ApiExplorerSettings(GroupName = "Mq Alert Definitions")]
public sealed partial class MqRoutingRulesController(ILogger<MqRoutingRulesController> _logger, IMqRoutingRulesService _mqRoutingRulesService) : ControllerBase
{
}