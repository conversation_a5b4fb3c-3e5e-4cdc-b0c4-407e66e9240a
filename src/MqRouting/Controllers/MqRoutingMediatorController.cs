namespace MqRouting.Controllers;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MqRouting.Services;

/// <summary>
/// Class MqRoutingMediatorController.
/// </summary>
/// <param name="_mediatedService">The sender mediated service.</param>
/// <remarks>Initializes a new instance of the <see cref="MqRoutingMediatorController" /> class.</remarks>
//[Authorize(AuthenticationSchemes = JiffyAuthSchemeConstants.JiffyAuthScheme)]
[AllowAnonymous]
[ApiController]
[Route("mediator")]
[Produces("application/json")]
[ProducesResponseType(typeof(ProblemDetails), 500)]
[ApiExplorerSettings(GroupName = "Mq Routing Mediator", IgnoreApi = true)]
public sealed partial class MqRoutingMediatorController(IMqRoutingMediatedService _mediatedService)
	: ControllerBase
{
}