---
Serilog:
  MinimumLevel:
    Default: Warning
    Override:
      Microsoft: Warning
      System: Warning
      Microsoft.AspNetCore.HttpLogging.HttpLoggingMiddleware: Information
      MqRouting: Debug
      Apex.Services: Debug
AllowedHosts: "*"
MqRoutingOptions:
  StreamProcessingUri: http://messenger:9095
ServiceOptions:
  MessengerUrl: http://messenger:5000
IAM:
  retryPolicyClientName: defaultWithLargeRetry
NATS:
  ModelRepoUrl: http://localhost
  Server: localhost:4222
  Subject: event-router-nats-subject
  Queue: event-router-nats-subject-queue