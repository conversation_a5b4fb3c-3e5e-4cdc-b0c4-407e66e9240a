namespace Apex.Models;

using Apex.Service.AuthHandlers.Scheme;
using Microsoft.Extensions.Primitives;
using System.Net.Http.Headers;

/// <summary>
/// Class UserSecurityInfo. This class cannot be inherited.
/// </summary>
public sealed record UserSecurityInfo(string TenantId, string AppId, string? UserId, string? AccessToken)
{
	/// <summary>
	/// Gets or sets the additional header values.
	/// </summary>
	/// <value>The additional header values.</value>
	public IDictionary<string, StringValues>? AdditionalHeaderValues { get; set; }

	/// <summary>
	/// Froms the web headers.
	/// </summary>
	/// <param name="dictionary">The dictionary.</param>
	/// <returns>UserInfo.</returns>
	/// <exception cref="System.ArgumentException">unable to find valid header - {JiffyAuthSchemeConstants.HEADER_X_Jiffy_Tenant_ID}</exception>
	/// <exception cref="System.ArgumentException">unable to find valid header - {JiffyAuthSchemeConstants.HEADER_X_Jiffy_App_ID}</exception>
	/// <exception cref="System.ArgumentException">unable to find valid header - {JiffyAuthSchemeConstants.HEADER_X_Jiffy_User_ID}</exception>
	public static UserSecurityInfo FromWebHeaders(IDictionary<string, StringValues> dictionary)
	{
		ArgumentNullException.ThrowIfNull(dictionary);

		if (!dictionary.TryGetValue(JiffyAuthSchemeConstants.HEADER_X_Jiffy_Tenant_ID, out var tenantIdValue))
		{
			throw new ArgumentException($"unable to find valid header - {JiffyAuthSchemeConstants.HEADER_X_Jiffy_Tenant_ID}");
		}

		if (!dictionary.TryGetValue(JiffyAuthSchemeConstants.HEADER_X_Jiffy_App_ID, out var appIdValue))
		{
			throw new ArgumentException($"unable to find valid header - {JiffyAuthSchemeConstants.HEADER_X_Jiffy_App_ID}");
		}

		if (!dictionary.TryGetValue(JiffyAuthSchemeConstants.HEADER_X_Jiffy_User_ID, out var userIdValue))
		{
			throw new ArgumentException($"unable to find valid header - {JiffyAuthSchemeConstants.HEADER_X_Jiffy_User_ID}");
		}

		string? accessToken = null;
		if (dictionary.TryGetValue("Authorization", out var authorizationValue))
		{
			accessToken = AuthenticationHeaderValue.Parse(authorizationValue).Parameter;
		}

		// OTEL headers
		var values = new Dictionary<string, StringValues>(StringComparer.OrdinalIgnoreCase);
		foreach (var header in ISTIO_OTEL_HEADERS)
		{
			if (dictionary.TryGetValue(header, out var svalue))
			{
				values.Add(header, svalue);
			}
		}
		if (values.Count == 0)
		{
			values = null;
		}

		return new UserSecurityInfo(tenantIdValue!, appIdValue!, userIdValue!, accessToken) { AdditionalHeaderValues = values };
	}

	/// <summary>
	/// Updates the web headers.
	/// </summary>
	/// <param name="targetHeaders">The target headers.</param>
	/// <returns>HttpHeaders.</returns>
	public HttpHeaders UpdateWebHeaders(HttpHeaders targetHeaders)
	{
		targetHeaders.TryAddWithoutValidation(JiffyAuthSchemeConstants.HEADER_X_Jiffy_Tenant_ID, TenantId);
		targetHeaders.TryAddWithoutValidation(JiffyAuthSchemeConstants.HEADER_X_Jiffy_App_ID, AppId);
		targetHeaders.TryAddWithoutValidation(JiffyAuthSchemeConstants.HEADER_X_Jiffy_User_ID, UserId);

		// OTEL and misc headers
		var values = AdditionalHeaderValues;
		if (values is not null && values.Count > 0)
		{
			foreach (var value in values)
			{
				targetHeaders.TryAddWithoutValidation(value.Key, (string?)value.Value);
			}
		}
		return targetHeaders;
	}

	/// <summary>
	/// The ISTIO OTEL headers
	/// </summary>
	public static readonly string[] ISTIO_OTEL_HEADERS = new[]
{
		"x-request-id",
		"x-b3-traceid",
		"x-b3-spanid",
		"x-b3-parentspanid",
		"x-b3-sampled",
		"x-b3-flags",
		"x-ot-span-context"
	};
}