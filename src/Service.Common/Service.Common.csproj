<Project Sdk="Microsoft.NET.Sdk.Web">
	<Import Project="../Build.Common.properties" />

	<PropertyGroup>
		<AssemblyName>Net.Service.Common</AssemblyName>
		<RootNamespace>Apex</RootNamespace>
		<NoDefaultLaunchSettingsFile>true</NoDefaultLaunchSettingsFile>
	</PropertyGroup>

	<PropertyGroup>
		<IsPackable>true</IsPackable>
		<GeneratePackageOnBuild>True</GeneratePackageOnBuild>
		<Description>library for bootstraping and helpers for services/microservices</Description>
		<Copyright>Jiffy.AI</Copyright>
		<RepositoryUrl>https://bitbucket.org/jiffy_bb_admin/net-service-common</RepositoryUrl>
		<RepositoryType>git</RepositoryType>
	</PropertyGroup>

	<ItemGroup>
		<!--<PackageReference Include="HttpClientToCurl" Version="2.0.6" />-->
		<PackageReference Include="Microsoft.Extensions.Caching.Hybrid" Version="9.9.0" />
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.9" />
		<PackageReference Include="Microsoft.Extensions.Http.Resilience" Version="9.9.0" />
		<PackageReference Include="Microsoft.Identity.Client" Version="4.77.1" />
		<PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.9" />
		<PackageReference Include="Polly" Version="8.6.4" />
		<PackageReference Include="Polly.Contrib.WaitAndRetry" Version="1.1.1" />
		<PackageReference Include="Serilog.Expressions" Version="5.0.0" />
		<PackageReference Include="Serilog.Extensions.Hosting" Version="9.0.0" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="9.0.2" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Asp.Versioning.Mvc" Version="8.1.0" />
		<PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
		<PackageReference Include="AspNetCore.HealthChecks.System" Version="9.0.0" />
		<PackageReference Include="System.Text.Json" Version="9.0.9" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.14.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.5" />
		<PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="9.0.0" />
		<PackageReference Include="prometheus-net.DotNetRuntime" Version="4.4.1" />
		<PackageReference Include="prometheus-net.AspNetCore" Version="8.2.1" />
		<PackageReference Include="prometheus-net.AspNetCore.HealthChecks" Version="8.2.1" />
		<PackageReference Include="NetEscapades.Configuration.Yaml" Version="3.1.0" />
		<PackageReference Include="Hellang.Middleware.ProblemDetails" Version="6.5.1" />
		<PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0" />
		<PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
		<PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
		<PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
		<PackageReference Include="Google.Protobuf" Version="3.32.1" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Net.Common" Version="3.0.2" />
		<!--Debugging - Comment Above, Uncomment Below for debugging -->
		<!--<ProjectReference Include="..\..\..\..\Apex2\net-common\src\Common\Common.csproj" />-->
	</ItemGroup>

	<ItemGroup>
		<Compile Update="Globals.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="BaseProgram.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="BaseServiceStartup.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\DataService.ImportExport.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\AppManagerService.Apps.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\AppManagerService.Component.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\AppManagerService.Service.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\DataService.Data.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\IOrgManagementService.Org.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\IOrgManagementService.Preference.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\OrgManagementService.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\OrgManagementService.Preference.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\OrgManagementService.Roles.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\OrgManagementServiceException.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Options\SecureRedisOptions.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\CacheExtensions.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\JiffyDriveRestService.BinaryMode.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
	</ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="Service.CommonTest" />
	</ItemGroup>
</Project>
