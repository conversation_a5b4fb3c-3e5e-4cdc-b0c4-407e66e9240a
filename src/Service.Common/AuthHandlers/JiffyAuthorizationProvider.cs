using Apex.Service.AuthHandlers.Scheme;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace Apex.Service.AuthHandlers.Scheme
{
	/// <summary>
	/// Class JiffyAuthSchemeConstants.
	/// </summary>
	public static partial class JiffyAuthSchemeConstants
	{
		/// <summary>
		/// The jiffy authentication scheme
		/// </summary>
		public const string JiffyAuthScheme = "bearer";

		/// <summary>
		/// The claim x jiffy tenant identifier
		/// </summary>
		public const string CLAIM_Jiffy_Tenant_ID = "JIFFY_TENANT_ID";

		/// <summary>
		/// The claim x jiffy user identifier
		/// </summary>
		public const string CLAIM_Jiffy_User_ID = ClaimTypes.NameIdentifier;

		/// <summary>
		/// The claim x jiffy application identifier
		/// </summary>
		public const string CLAIM_Jiffy_App_ID = "JIFFY_APP_ID";

		/// <summary>
		/// The claim x jiffy target application identifier
		/// </summary>
		public const string CLAIM_Jiffy_Target_App_ID = "JIFFY_TARGET_APP_ID";

		/// <summary>
		/// The header x jiffy tenant identifier
		/// </summary>
		public const string HEADER_X_Jiffy_Tenant_ID = "X-Jiffy-Tenant-ID";

		/// <summary>
		/// The header x jiffy user identifier
		/// </summary>
		public const string HEADER_X_Jiffy_User_ID = "X-Jiffy-User-ID";

		/// <summary>
		/// The header x jiffy application identifier
		/// </summary>
		public const string HEADER_X_Jiffy_App_ID = "X-Jiffy-App-ID";

		/// <summary>
		/// The header x jiffy target application identifier
		/// </summary>
		public const string HEADER_X_Jiffy_Target_App_ID = "X-Jiffy-Target-App-Id";
	}

	/// <summary>
	/// Class JiffyAuthSchemeOptions.
	/// </summary>
	public sealed partial class JiffyAuthSchemeOptions : AuthenticationSchemeOptions
	{
	}
}

namespace Apex.Service.AuthHandlers
{
	/// <summary>
	/// Class JiffyAuthHandler.
	/// </summary>
	/// <remarks>
	/// Initializes a new instance of the <see cref="JiffyAuthHandler" /> class.
	/// </remarks>
	/// <param name="options">The monitor for the options instance.</param>
	/// <param name="logger">The <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory" />.</param>
	/// <param name="encoder">The <see cref="T:System.Text.Encodings.Web.UrlEncoder" />.</param>
	public sealed partial class JiffyAuthHandler(
		IOptionsMonitor<Scheme.JiffyAuthSchemeOptions> options,
		ILoggerFactory logger,
		UrlEncoder encoder
		) : AuthenticationHandler<Scheme.JiffyAuthSchemeOptions>(options, logger, encoder)
	{

		/// <summary>
		/// Allows derived types to handle authentication.
		/// </summary>
		/// <returns>The <see cref="T:Microsoft.AspNetCore.Authentication.AuthenticateResult" />.</returns>
		protected override Task<AuthenticateResult> HandleAuthenticateAsync()
		{
			var headers = Request.Headers;
			if (!headers.TryGetValue(JiffyAuthSchemeConstants.HEADER_X_Jiffy_Tenant_ID, out Microsoft.Extensions.Primitives.StringValues tidValue))
			{
				return Task.FromResult(AuthenticateResult.Fail($"required header not found - Header={JiffyAuthSchemeConstants.HEADER_X_Jiffy_Tenant_ID}"));
			}
			if (!headers.TryGetValue(JiffyAuthSchemeConstants.HEADER_X_Jiffy_User_ID, out Microsoft.Extensions.Primitives.StringValues userIdValue))
			{
				return Task.FromResult(AuthenticateResult.Fail($"required header not found - Header={JiffyAuthSchemeConstants.HEADER_X_Jiffy_User_ID}"));
			}
			if (!headers.TryGetValue(JiffyAuthSchemeConstants.HEADER_X_Jiffy_App_ID, out Microsoft.Extensions.Primitives.StringValues appIdValue))
			{
				return Task.FromResult(AuthenticateResult.Fail($"required header not found - Header={JiffyAuthSchemeConstants.HEADER_X_Jiffy_App_ID}"));
			}

			IEnumerable<Claim> claims =
			[
				new Claim(JiffyAuthSchemeConstants.CLAIM_Jiffy_User_ID, userIdValue!),
				new Claim(JiffyAuthSchemeConstants.CLAIM_Jiffy_Tenant_ID, tidValue!),
				new Claim(JiffyAuthSchemeConstants.CLAIM_Jiffy_App_ID, appIdValue!),
			];

			// optional target app id header
			if (headers.TryGetValue(JiffyAuthSchemeConstants.HEADER_X_Jiffy_Target_App_ID, out Microsoft.Extensions.Primitives.StringValues targetAppIdValue))
			{
				claims = claims.Append(new Claim(JiffyAuthSchemeConstants.CLAIM_Jiffy_Target_App_ID, targetAppIdValue!));
			}


			var claimsIdentity = new ClaimsIdentity(claims, nameof(JiffyAuthHandler));
			var ticket = new AuthenticationTicket(new ClaimsPrincipal(claimsIdentity), Scheme.Name);
			return Task.FromResult(AuthenticateResult.Success(ticket));
		}
	}
}