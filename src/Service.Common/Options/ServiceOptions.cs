namespace Apex.Options;

/// <summary>
/// Class ServiceOptions. This class cannot be inherited.
/// </summary>
public sealed partial class ServiceOptions
{
	/// <summary>
	/// Gets or sets the jiffy drive URL.
	/// </summary>
	/// <value>The jiffy drive URL.</value>
	public string? JiffyDriveUrl { get; set; }

	/// <summary>
	/// Gets or sets the template processor URL.
	/// </summary>
	/// <value>The template processor URL.</value>
	public string? TemplateProcessorUrl { get; set; }

	/// <summary>
	/// Gets or sets the messenger URL.
	/// </summary>
	/// <value>The messenger URL.</value>
	public string? MessengerUrl { get; set; }

	/// <summary>
	/// Gets or sets the workflow URL.
	/// </summary>
	/// <value>The workflow URL.</value>
	public string? WorkflowUrl { get; set; }

	/// <summary>
	/// Gets or sets the vault URL.
	/// </summary>
	/// <value>The vault URL.</value>
	public string? VaultUrl { get; set; }

	/// <summary>
	/// Gets or sets the pam URL.
	/// </summary>
	/// <value>The pam URL.</value>
	public string? PamUrl { get; set; }

	/// <summary>
	/// Gets or sets the model repo URL.
	/// </summary>
	/// <value>The model repo URL.</value>
	public string? ModelRepoUrl { get; set; }

	/// <summary>
	/// Gets or sets the CLS URL.
	/// </summary>
	/// <value>The CLS URL.</value>
	public string? ClsUrl { get; set; }

	/// <summary>
	/// Gets or sets the configuration manager URL.
	/// </summary>
	/// <value>The configuration manager URL.</value>
	public string? ConfigManagerUrl { get; set; }

	/// <summary>
	/// Gets or sets the data service URL.
	/// </summary>
	/// <value>The data service URL.</value>
	public string? DataServiceUrl { get; set; }

	/// <summary>
	/// Gets or sets the DVT service URL.
	/// </summary>
	/// <value>The DVT service URL.</value>
	public string? DvtServiceUrl { get; set; }

	/// <summary>
	/// Gets or sets the DVT service URL.
	/// </summary>
	/// <value>The DVT service URL.</value>
	public string? EsBundleServiceUrl { get; set; }

	/// <summary>
	/// Gets or sets the OMS URL.
	/// </summary>
	/// <value>The vault URL.</value>
	public string? OmsUrl { get; set; }

	/// <summary>
	/// Gets or sets the iam URL.
	/// </summary>
	/// <value>The iam URL.</value>
	public string? IamUrl { get; set; }
}