namespace Apex.Options;

/// <summary>
/// Class IamOptions. This class cannot be inherited.
/// </summary>
public sealed partial class IamOptions
{
	/// <summary>
	/// Gets the name of the tenant.
	/// </summary>
	/// <value>The name of the tenant.</value>
	public string? TenantName { get; set; }

	/// <summary>
	/// Gets or sets the tenant identifier.
	/// </summary>
	/// <value>The tenant identifier.</value>
	public string? TenantId { get; set; }

	/// <summary>
	/// Gets the name of the tenant.
	/// </summary>
	/// <value>The name of the tenant.</value>
	public string? AppName { get; set; }

	/// <summary>
	/// Gets or sets the tenant identifier.
	/// </summary>
	/// <value>The tenant identifier.</value>
	public string? AppInstanceId { get; set; }

	/// <summary>
	/// Gets or sets the application service principal.
	/// </summary>
	/// <value>The application service principal.</value>
	public string? AppServicePrincipal { get; set; }

	/// <summary>
	/// Gets or sets the client - TEMPORARY.
	/// </summary>
	/// <value>The client.</value>
	public string? Client { get; set; }

	/// <summary>
	/// Gets or sets the secret - TEMPORARY.
	/// </summary>
	/// <value>The secret.</value>
	public string? Secret { get; set; }

	/// <summary>
	/// Gets the grace time for token expire in seconds.
	/// </summary>
	/// <value>The grace time for token expire in seconds.</value>
	public int GraceTimeForTokenExpireInSeconds { get; set; }

	/// <summary>
	/// Gets or sets the URL.
	/// </summary>
	/// <value>The URL.</value>
	public string? Url { get; set; }

	/// <summary>
	/// Gets or sets the tenant application user identifier cache expiry in minutes.
	/// </summary>
	/// <value>The tenant application user identifier cache expiry in minutes.</value>
	public int TenantAppUserIdCacheExpiryInMinutes { get; set; } = 10;

	/// <summary>
	/// Gets or sets the name of the retry policy client.
	/// </summary>
	/// <value>The client.</value>
	public string? RetryPolicyClientName { get; set; }

}