namespace Apex.Options;

/// <summary>
/// Class SecureRedisOptions. This class cannot be inherited.
/// </summary>
public sealed class SecureRedisOptions
{
	/// <summary>
	/// Gets or sets the URL.
	/// </summary>
	/// <value>The URL.</value>
	[ConfigurationKeyName("REDIS_URL")]
	public string Url { get; set; }

	/// <summary>
	/// Gets or sets the username.
	/// </summary>
	/// <value>The username.</value>
	[ConfigurationK<PERSON><PERSON>ame("REDIS_USERNAME")]
	public string Username { get; set; }

	/// <summary>
	/// Gets or sets the password.
	/// </summary>
	/// <value>The password.</value>
	[ConfigurationK<PERSON>Name("REDIS_PASSWORD")]
	public string? Password { get; set; }

	/// <summary>
	/// Gets or sets the port.
	/// </summary>
	/// <value>The port.</value>
	[ConfigurationKeyName("REDIS_PORT")]
	public string Port { get; set; }
}