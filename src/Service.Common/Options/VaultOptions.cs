namespace Apex.Options;

/// <summary>
/// Class VaultOptions. This class cannot be inherited.
/// </summary>
public sealed class VaultOptions
{
	/// <summary>
	/// Gets or sets the vault secret path.
	/// </summary>
	/// <value>
	/// The vault secret path.
	/// </value>
	public string? VaultSecretPath { get; set; }

	/// <summary>
	/// Gets or sets the vault mount path.
	/// </summary>
	/// <value>
	/// The vault mount path.
	/// </value>
	public string? VaultMountPath { get; set; } = "pfx";

	/// <summary>
	/// Gets the grace time for token expire in seconds.
	/// </summary>
	/// <value>
	/// The grace time for token expire in seconds.
	/// </value>
	public int GraceTimeForTokenExpireInSeconds { get; set; }
}