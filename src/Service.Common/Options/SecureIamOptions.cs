namespace Apex.Options;

/// <summary>
/// Class SecureIamOptions. This class cannot be inherited.
/// </summary>
public sealed class SecureIamOptions
{
	/// <summary>
	/// Gets or sets the client identifier.
	/// </summary>
	/// <value>The client identifier.</value>
	[ConfigurationKeyName("SERVICEACCOUNT_NAME")]
	public string? ClientId { get; set; }

	/// <summary>
	/// Gets or sets the client secret.
	/// </summary>
	/// <value>The client secret.</value>
	[ConfigurationKeyName("SERVICEACCOUNT_PASSWORD")]
	public string? ClientSecret { get; set; }
}