namespace Apex.Options;

/// <summary>
/// Class SecureVaultOptions. This class cannot be inherited.
/// </summary>
public sealed class SecureVaultOptions
{
	/// <summary>
	/// Gets or sets the role identifier.
	/// </summary>
	/// <value>The role identifier.</value>
	[ConfigurationKeyName("VAULT_ROLE")]
	public string? RoleId { get; set; }

	/// <summary>
	/// Gets or sets the vault pki role.
	/// </summary>
	/// <value>The vault pki role.</value>
	[ConfigurationKeyName("VAULT_PKI_INT_ROLE")]
	public string? VaultPkiIntRole { get; set; }
}