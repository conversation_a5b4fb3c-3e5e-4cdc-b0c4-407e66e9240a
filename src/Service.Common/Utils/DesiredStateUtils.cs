namespace System;

/// <summary>
/// Class DesiredStateUtils.
/// </summary>
public sealed partial class DesiredStateUtils<T>
{
	/// <summary>
	/// The object lock
	/// </summary>
	private readonly object _objectLock = new();

	/// <summary>
	/// The initial state
	/// </summary>
	private List<T> _state;

	/// <summary>
	/// The comparer
	/// </summary>
	private readonly IEqualityComparer<T>? _comparer;

	/// <summary>
	/// The new item callback
	/// </summary>
	private readonly Action<T> _newItemCallback;

	/// <summary>
	/// The deleted item callback
	/// </summary>
	private readonly Action<T> _deletedItemCallback;

	/// <summary>
	/// Initializes a new instance of the <see cref="DesiredStateUtils{T}" /> class.
	/// </summary>
	/// <param name="initialState">The initial state.</param>
	/// <param name="newItemCallback">The new item callback.</param>
	/// <param name="deletedItemCallback">The deleted item callback.</param>
	/// <param name="comparer">The comparer.</param>
	public DesiredStateUtils(List<T> initialState, Action<T> newItemCallback, Action<T> deletedItemCallback, IEqualityComparer<T>? comparer = null)
	{
		_state = initialState;
		_comparer = comparer;
		_newItemCallback = newItemCallback;
		_deletedItemCallback = deletedItemCallback;
	}

	/// <summary>
	/// Merges the specified new state.
	/// </summary>
	/// <param name="newState">The new state.</param>
	public void Merge(List<T> newState)
	{
		lock (_objectLock)
		{
			var currentState = new List<T>(_state);
			var deletedItems = currentState.Except(newState, _comparer).ToList();
			var newItems = newState.Except(currentState, _comparer).ToList();

			// for each removed, remove from current state - this has to occur before add, as an item can be modified
			// which means removal needs to happen first before add
			// NOTE: do not change the order.
			foreach (var deletedItem in deletedItems)
			{
				_deletedItemCallback.Invoke(deletedItem);
				currentState.Remove(deletedItem);
			}

			// for each added, add to current state
			foreach (var newItem in newItems)
			{
				_newItemCallback.Invoke(newItem);
				currentState.Add(newItem);
			}

			_state = currentState;
		}
	}
}