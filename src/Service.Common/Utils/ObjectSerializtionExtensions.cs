using System.Text;

namespace System;

/// <summary>
/// Class ObjectSerializtionExtensions.
/// </summary>
public static partial class ObjectSerializtionExtensions
{
	/// <summary>
	/// Converts the given instance into key value pairs.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="source">The source.</param>
	/// <returns>IEnumerable&lt;T&gt;.</returns>
	public static IList<KeyValuePair<string, string?>> ToKeyValuePairs<T>(this T? source)
		=> source is not null
			? new ConfigurationBuilder()
				.AddJsonStream(new MemoryStream(Encoding.UTF8.GetBytes(source.ToJSON(false)!)))
				.Build()
				.AsEnumerable()
				.Where(kv => !kv.Value.IsNullOrEmpty())
				.ToList()
			: [];

	/// <summary>
	/// Converts the given key value pairs into an instance.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="configuration">The configuration.</param>
	/// <returns>T.</returns>
	public static T? ToObject<T>(this IEnumerable<KeyValuePair<string, string?>>? configuration)
		=> configuration is not null
			? new ConfigurationBuilder()
				.AddInMemoryCollection(configuration)
				.Build()
				.Get<T>()
			: default;

	/// <summary>
	/// Converts the given key value pairs into an instance.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="configuration">The configuration.</param>
	/// <param name="instance">The instance.</param>
	/// <returns>T.</returns>
	public static bool MergeWithObject<T>(this IEnumerable<KeyValuePair<string, string?>>? configuration, T instance)
	{
		if (configuration is not null)
		{
			new ConfigurationBuilder()
				.AddInMemoryCollection(configuration)
				.Build()
				.Bind(instance, (options) =>
				{
					options.BindNonPublicProperties = true;
					options.ErrorOnUnknownConfiguration = true;
				});
			return true;
		}
		return false;
	}
}