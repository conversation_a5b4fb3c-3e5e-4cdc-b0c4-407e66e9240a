using Polly.Contrib.WaitAndRetry;

namespace System;

/// <summary>
/// Class BackoffUtils.
/// </summary>
public static partial class BackoffUtils
{
	/// <summary>
	/// Aws decorrelated jitter backoff.
	/// </summary>
	/// <param name="minDelay">The minimum delay.</param>
	/// <param name="maxDelay">The maximum delay.</param>
	/// <param name="retryCount">The retry count.</param>
	/// <param name="seed">The seed.</param>
	/// <param name="fastFirst">if set to <c>true</c> [fast first].</param>
	/// <returns>IEnumerable&lt;TimeSpan&gt;.</returns>
	public static IEnumerable<TimeSpan> AwsDecorrelatedJitterBackoff(TimeSpan minDelay, TimeSpan maxDelay, int retryCount, int? seed = null, bool fastFirst = false)
		=> Backoff.AwsDecorrelatedJitterBackoff(minDelay, maxDelay, retryCount, seed, fastFirst);

	/// <summary>
	/// Constant backoff.
	/// </summary>
	/// <param name="delay">The delay.</param>
	/// <param name="retryCount">The retry count.</param>
	/// <param name="fastFirst">if set to <c>true</c> [fast first].</param>
	/// <returns>IEnumerable&lt;TimeSpan&gt;.</returns>
	public static IEnumerable<TimeSpan> ConstantBackoff(TimeSpan delay, int retryCount, bool fastFirst = false)
		=> Backoff.ConstantBackoff(delay, retryCount, fastFirst);

	/// <summary>
	/// Decorrelated jitter backoff v2.
	/// </summary>
	/// <param name="medianFirstRetryDelay">The median first retry delay.</param>
	/// <param name="retryCount">The retry count.</param>
	/// <param name="seed">The seed.</param>
	/// <param name="fastFirst">if set to <c>true</c> [fast first].</param>
	/// <returns>IEnumerable&lt;TimeSpan&gt;.</returns>
	public static IEnumerable<TimeSpan> DecorrelatedJitterBackoffV2(TimeSpan medianFirstRetryDelay, int retryCount, int? seed = null, bool fastFirst = false)
		=> Backoff.DecorrelatedJitterBackoffV2(medianFirstRetryDelay, retryCount, seed, fastFirst);

	/// <summary>
	/// Exponential backoff.
	/// </summary>
	/// <param name="initialDelay">The initial delay.</param>
	/// <param name="retryCount">The retry count.</param>
	/// <param name="factor">The factor.</param>
	/// <param name="fastFirst">if set to <c>true</c> [fast first].</param>
	/// <returns>IEnumerable&lt;TimeSpan&gt;.</returns>
	public static IEnumerable<TimeSpan> ExponentialBackoff(TimeSpan initialDelay, int retryCount, double factor = 2, bool fastFirst = false)
		=> Backoff.ExponentialBackoff(initialDelay, retryCount, factor, fastFirst);

	/// <summary>
	/// Linear backoff.
	/// </summary>
	/// <param name="initialDelay">The initial delay.</param>
	/// <param name="retryCount">The retry count.</param>
	/// <param name="factor">The factor.</param>
	/// <param name="fastFirst">The fast first.</param>
	/// <returns>System.Collections.Generic.IEnumerable&lt;System.TimeSpan&gt;.</returns>
	public static IEnumerable<TimeSpan> LinearBackoff(TimeSpan initialDelay, int retryCount, double factor = 1, bool fastFirst = false)
		=> Backoff.LinearBackoff(initialDelay, retryCount, factor, fastFirst);

	/// <summary>
	/// Backoff helper, to begin with exponential and then to linear
	/// </summary>
	/// <param name="initialDelay">The initial delay.</param>
	/// <param name="exponentialDelayMax">The exponential delay maximum.</param>
	/// <param name="maxRetries">The maximum retries.</param>
	/// <returns></returns>
	public static IEnumerable<TimeSpan> ExponentialThenLinearBackoff(TimeSpan initialDelay, TimeSpan? exponentialDelayMax = null, int maxRetries = int.MaxValue)
	{
		var exponentialDelay = exponentialDelayMax.GetValueOrDefault(TimeSpan.FromMinutes(30));// cap exponential to 30 minutes

		var retries = 0;
		var lastKnownDelay = initialDelay;
		foreach (var delay in Backoff.ExponentialBackoff(initialDelay, retryCount: maxRetries, fastFirst: true))
		{
			retries++;

			// if threshold exceeds exponential, step out...
			if (delay > exponentialDelay)
			{
				break;
			}

			lastKnownDelay = delay;
			yield return delay;
		}

		foreach (var delay in Backoff.ConstantBackoff(lastKnownDelay, retryCount: maxRetries - retries))
		{
			retries++;
			yield return delay;
		}
	}
}