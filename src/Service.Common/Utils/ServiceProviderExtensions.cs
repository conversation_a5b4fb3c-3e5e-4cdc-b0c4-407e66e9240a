using Apex;

namespace System;

/// <summary>
/// Class ServiceProviderExtensions.
/// </summary>
public static partial class ServiceProviderExtensions
{
	/// <summary>
	/// Gets the required hosted service.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="sp">The sp.</param>
	/// <returns>System.Nullable&lt;T&gt;.</returns>
	/// <exception cref="Apex.ServiceException">Unable to resolve service of type - {typeof(T)}</exception>
	public static T GetRequiredHostedService<T>(this IServiceProvider sp)
		=> sp.GetHostedService<T>()
			?? throw new ServiceException($"Unable to resolve service of type - {typeof(T)}");

	/// <summary>
	/// Gets the hosted service.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="sp">The sp.</param>
	/// <returns>System.Nullable&lt;T&gt;.</returns>
	public static T? GetHostedService<T>(this IServiceProvider sp)
		=> sp.GetServices<IHostedService>().OfType<T>().FirstOrDefault();
}