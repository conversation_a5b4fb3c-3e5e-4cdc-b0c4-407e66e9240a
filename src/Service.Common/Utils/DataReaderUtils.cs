using System.Data;

namespace System;

/// <summary>
/// Class DataReaderUtils.
/// </summary>
public static partial class DataReaderUtils
{
	/// <summary>
	/// Converts to wrappeddatareader.
	/// </summary>
	/// <param name="reader">The reader.</param>
	/// <returns>IDataReader.</returns>
	public static WrappedDataReader ToWrappedDataReader(this IDataReader reader)
		=> new WrappedDataReader(reader);

	/// <summary>
	/// Class WrappedDataReader. This class cannot be inherited.
	/// Implements the <see cref="IDataReader" />
	/// </summary>
	/// <seealso cref="IDataReader" />
	public sealed class WrappedDataReader(IDataReader inner) : IDataReader
	{
		/// <summary>
		/// The inner
		/// </summary>
		private readonly IDataReader _inner = inner;

		/// <summary>
		/// Gets the <see cref="System.Object"/> with the specified i.
		/// </summary>
		/// <param name="i">The i.</param>
		/// <returns>System.Object.</returns>
		object IDataRecord.this[int i] => _inner[i];

		/// <summary>
		/// Gets the <see cref="System.Object"/> with the specified name.
		/// </summary>
		/// <param name="name">The name.</param>
		/// <returns>System.Object.</returns>
		object IDataRecord.this[string name] => _inner[name];

		/// <summary>
		/// Gets a value indicating the depth of nesting for the current row.
		/// </summary>
		/// <value>The depth.</value>
		int IDataReader.Depth => _inner.Depth;

		/// <summary>
		/// Gets a value indicating whether the data reader is closed.
		/// </summary>
		/// <value><c>true</c> if this instance is closed; otherwise, <c>false</c>.</value>
		bool IDataReader.IsClosed => _inner.IsClosed;

		/// <summary>
		/// Gets the number of rows changed, inserted, or deleted by execution of the SQL statement.
		/// </summary>
		/// <value>The records affected.</value>
		int IDataReader.RecordsAffected => _inner.RecordsAffected;

		/// <summary>
		/// Gets the number of columns in the current row.
		/// </summary>
		/// <value>The field count.</value>
		int IDataRecord.FieldCount => _inner.FieldCount;

		/// <summary>
		/// Closes the <see cref="T:System.Data.IDataReader" /> Object.
		/// </summary>
		void IDataReader.Close() => _inner.Close();

		/// <summary>
		/// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
		/// </summary>
		void IDisposable.Dispose() => _inner.Dispose();

		/// <summary>
		/// Gets the value of the specified column as a Boolean.
		/// </summary>
		/// <param name="i">The zero-based column ordinal.</param>
		/// <returns>The value of the column.</returns>
		bool IDataRecord.GetBoolean(int i) => _inner.GetBoolean(i);

		/// <summary>
		/// Gets the 8-bit unsigned integer value of the specified column.
		/// </summary>
		/// <param name="i">The zero-based column ordinal.</param>
		/// <returns>The 8-bit unsigned integer value of the specified column.</returns>
		byte IDataRecord.GetByte(int i) => _inner.GetByte(i);

		/// <summary>
		/// Reads a stream of bytes from the specified column offset into the buffer as an array, starting at the given buffer offset.
		/// </summary>
		/// <param name="i">The zero-based column ordinal.</param>
		/// <param name="fieldOffset">The index within the field from which to start the read operation.</param>
		/// <param name="buffer">The buffer into which to read the stream of bytes.</param>
		/// <param name="bufferoffset">The index for <paramref name="buffer" /> to start the read operation.</param>
		/// <param name="length">The number of bytes to read.</param>
		/// <returns>The actual number of bytes read.</returns>
		long IDataRecord.GetBytes(int i, long fieldOffset, byte[]? buffer, int bufferoffset, int length) => _inner.GetBytes(i, fieldOffset, buffer, bufferoffset, length);

		/// <summary>
		/// Gets the character value of the specified column.
		/// </summary>
		/// <param name="i">The zero-based column ordinal.</param>
		/// <returns>The character value of the specified column.</returns>
		char IDataRecord.GetChar(int i) => _inner.GetChar(i);

		/// <summary>
		/// Reads a stream of characters from the specified column offset into the buffer as an array, starting at the given buffer offset.
		/// </summary>
		/// <param name="i">The zero-based column ordinal.</param>
		/// <param name="fieldoffset">The index within the row from which to start the read operation.</param>
		/// <param name="buffer">The buffer into which to read the stream of bytes.</param>
		/// <param name="bufferoffset">The index for <paramref name="buffer" /> to start the read operation.</param>
		/// <param name="length">The number of bytes to read.</param>
		/// <returns>The actual number of characters read.</returns>
		long IDataRecord.GetChars(int i, long fieldoffset, char[]? buffer, int bufferoffset, int length) => _inner.GetChars(i, fieldoffset, buffer, bufferoffset, length);

		/// <summary>
		/// Returns an <see cref="T:System.Data.IDataReader" /> for the specified column ordinal.
		/// </summary>
		/// <param name="i">The index of the field to find.</param>
		/// <returns>The <see cref="T:System.Data.IDataReader" /> for the specified column ordinal.</returns>
		IDataReader IDataRecord.GetData(int i) => _inner.GetData(i);

		/// <summary>
		/// Gets the data type information for the specified field.
		/// </summary>
		/// <param name="i">The index of the field to find.</param>
		/// <returns>The data type information for the specified field.</returns>
		string IDataRecord.GetDataTypeName(int i) => _inner.GetDataTypeName(i);

		/// <summary>
		/// Gets the date and time data value of the specified field.
		/// </summary>
		/// <param name="i">The index of the field to find.</param>
		/// <returns>The date and time data value of the specified field.</returns>
		DateTime IDataRecord.GetDateTime(int i) => _inner.GetDateTime(i);

		/// <summary>
		/// Gets the fixed-position numeric value of the specified field.
		/// </summary>
		/// <param name="i">The index of the field to find.</param>
		/// <returns>The fixed-position numeric value of the specified field.</returns>
		decimal IDataRecord.GetDecimal(int i) => _inner.GetDecimal(i);

		/// <summary>
		/// Gets the double-precision floating point number of the specified field.
		/// </summary>
		/// <param name="i">The index of the field to find.</param>
		/// <returns>The double-precision floating point number of the specified field.</returns>
		double IDataRecord.GetDouble(int i) => _inner.GetDouble(i);

		/// <summary>
		/// Gets the <see cref="T:System.Type" /> information corresponding to the type of <see cref="T:System.Object" /> that would be returned from <see cref="M:System.Data.IDataRecord.GetValue(System.Int32)" />.
		/// </summary>
		/// <param name="i">The index of the field to find.</param>
		/// <returns>The <see cref="T:System.Type" /> information corresponding to the type of <see cref="T:System.Object" /> that would be returned from <see cref="M:System.Data.IDataRecord.GetValue(System.Int32)" />.</returns>
		Type IDataRecord.GetFieldType(int i) => _inner.GetFieldType(i);

		/// <summary>
		/// Gets the single-precision floating point number of the specified field.
		/// </summary>
		/// <param name="i">The index of the field to find.</param>
		/// <returns>The single-precision floating point number of the specified field.</returns>
		float IDataRecord.GetFloat(int i) => _inner.GetFloat(i);

		/// <summary>
		/// Returns the GUID value of the specified field.
		/// </summary>
		/// <param name="i">The index of the field to find.</param>
		/// <returns>The GUID value of the specified field.</returns>
		Guid IDataRecord.GetGuid(int i) => _inner.GetGuid(i);

		/// <summary>
		/// Gets the 16-bit signed integer value of the specified field.
		/// </summary>
		/// <param name="i">The index of the field to find.</param>
		/// <returns>The 16-bit signed integer value of the specified field.</returns>
		short IDataRecord.GetInt16(int i) => _inner.GetInt16(i);

		/// <summary>
		/// Gets the 32-bit signed integer value of the specified field.
		/// </summary>
		/// <param name="i">The index of the field to find.</param>
		/// <returns>The 32-bit signed integer value of the specified field.</returns>
		int IDataRecord.GetInt32(int i) => _inner.GetInt32(i);

		/// <summary>
		/// Gets the 64-bit signed integer value of the specified field.
		/// </summary>
		/// <param name="i">The index of the field to find.</param>
		/// <returns>The 64-bit signed integer value of the specified field.</returns>
		long IDataRecord.GetInt64(int i) => _inner.GetInt64(i);

		/// <summary>
		/// Gets the name for the field to find.
		/// </summary>
		/// <param name="i">The index of the field to find.</param>
		/// <returns>The name of the field or the empty string (""), if there is no value to return.</returns>
		string IDataRecord.GetName(int i) => _inner.GetName(i);

		/// <summary>
		/// Return the index of the named field.
		/// </summary>
		/// <param name="name">The name of the field to find.</param>
		/// <returns>The index of the named field.</returns>
		int IDataRecord.GetOrdinal(string name) => _inner.GetOrdinal(name);

		/// <summary>
		/// Returns a <see cref="T:System.Data.DataTable" /> that describes the column metadata of the <see cref="T:System.Data.IDataReader" />.
		/// Returns <see langword="null" /> if the executed command returned no resultset, or after <see cref="M:System.Data.IDataReader.NextResult" /> returns <see langword="false" />.
		/// </summary>
		/// <returns>A <see cref="T:System.Data.DataTable" /> that describes the column metadata.</returns>
		DataTable? IDataReader.GetSchemaTable() => _inner.GetSchemaTable();

		/// <summary>
		/// Gets the string value of the specified field.
		/// </summary>
		/// <param name="i">The index of the field to find.</param>
		/// <returns>The string value of the specified field.</returns>
		string IDataRecord.GetString(int i) => _inner.GetString(i);

		/// <summary>
		/// Return the value of the specified field.
		/// </summary>
		/// <param name="i">The index of the field to find.</param>
		/// <returns>The <see cref="T:System.Object" /> which will contain the field value upon return.</returns>
		object IDataRecord.GetValue(int i)
			=> _inner.IsDBNull(i)
				? DBNull.Value
				: _inner.GetDataTypeName(i) switch
				{
					"DATE" when _inner.GetValue(i) is string s && s.IsNullOrEmpty() => s,
					"DATE" => _inner.GetDateTime(i).ToString("yyyy-MM-dd"),

					"DATETIME" when _inner.GetValue(i) is string s && s.IsNullOrEmpty() => s,
					"DATETIME" => _inner.GetDateTime(i).ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
					_ => _inner.GetValue(i)
				};

		/// <summary>
		/// Populates an array of objects with the column values of the current record.
		/// </summary>
		/// <param name="values">An array of <see cref="T:System.Object" /> to copy the attribute fields into.</param>
		/// <returns>The number of instances of <see cref="T:System.Object" /> in the array.</returns>
		int IDataRecord.GetValues(object[] values) => _inner.GetValues(values);

		/// <summary>
		/// Return whether the specified field is set to null.
		/// </summary>
		/// <param name="i">The index of the field to find.</param>
		/// <returns><see langword="true" /> if the specified field is set to null; otherwise, <see langword="false" />.</returns>
		bool IDataRecord.IsDBNull(int i) => _inner.IsDBNull(i);

		/// <summary>
		/// Advances the data reader to the next result, when reading the results of batch SQL statements.
		/// </summary>
		/// <returns><see langword="true" /> if there are more rows; otherwise, <see langword="false" />.</returns>
		bool IDataReader.NextResult() => _inner.NextResult();

		/// <summary>
		/// Advances the <see cref="T:System.Data.IDataReader" /> to the next record.
		/// </summary>
		/// <returns><see langword="true" /> if there are more rows; otherwise, <see langword="false" />.</returns>
		bool IDataReader.Read()
		{
			var read = _inner.Read();
			if (read)
			{
				_recordsRead++;
			}
			return read;
		}

		/// <summary>
		/// Gets the records read.
		/// </summary>
		/// <value>The records read.</value>
		public int RecordsRead => _recordsRead;

		/// <summary>
		/// The records read
		/// </summary>
		private int _recordsRead = 0;
	}
}