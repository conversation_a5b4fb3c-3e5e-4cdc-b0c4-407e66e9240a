namespace System;

/// <summary>
/// Class FileUtils.
/// </summary>
public static partial class FileUtils
{
	/// <summary>
	/// returns a disposable object - that deletes the underlying file on dispose
	/// </summary>
	/// <param name="filePath">The file path.</param>
	/// <param name="deleteOnDispose">if set to <c>true</c> [delete on dispose].</param>
	/// <returns>System.Nullable&lt;IDisposable&gt;.</returns>
	public static DisposableFile? DisposableTempFile(this string? filePath, bool deleteOnDispose = true)
		=> filePath is not null ? new DisposableFile(filePath, deleteOnDispose) : null;
}

/// <summary>
/// Class DisposableFile. This class cannot be inherited.
/// </summary>
public sealed class DisposableFile(string _filePath, bool _deleteOnDispose = true) : IDisposable
{
	/// <summary>
	/// Gets the file path.
	/// </summary>
	/// <value>The file path.</value>
	public string FilePath => _filePath;

	/// <summary>
	/// Gets a value indicating whether to delete on dispose.
	/// </summary>
	/// <value><c>true</c> if [delete on dispose]; otherwise, <c>false</c>.</value>
	public bool DeleteOnDispose => _deleteOnDispose;

	/// <summary>
	/// Releases unmanaged and - optionally - managed resources.
	/// </summary>
	/// <param name="disposing"><c>true</c> to release both managed and unmanaged resources; <c>false</c> to release only unmanaged resources.</param>
	private void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			if (disposing && _deleteOnDispose && File.Exists(FilePath))
			{
				File.Delete(FilePath);
			}

			_disposedValue = true;
		}
	}

	/// <summary>
	/// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
	/// </summary>
	public void Dispose()
	{
		Dispose(disposing: true);
		GC.SuppressFinalize(this);
	}

	/// <summary>
	/// The disposed value
	/// </summary>
	private bool _disposedValue;
}