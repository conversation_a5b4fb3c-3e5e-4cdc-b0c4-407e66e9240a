using System.Globalization;

namespace System;

/// <summary>
/// Class GenericExtensions.
/// </summary>
public static partial class GenericExtensions
{
	/// <summary>
	/// Trims the string to the specified length
	/// </summary>
	/// <param name="source">The source.</param>
	/// <param name="length">The length.</param>
	/// <returns>IEnumerable&lt;T&gt;.</returns>
	public static string? TrimToLength(this string? source, int length)
	{
		if (source is null)
		{
			return null;
		}

		return source.Length > length ? source[..length] : source;
	}


	/// <summary>
	/// gets a not null collection.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="source">The source.</param>
	/// <returns>IEnumerable&lt;T&gt;.</returns>
	public static IEnumerable<T> NotNull<T>(this IEnumerable<T?> source) where T : class
		=> source.Where(x => x is not null).Select(x => x!);

	/// <summary>
	/// Converts string to title case.
	/// </summary>
	/// <param name="s">The s.</param>
	/// <returns>System.String.</returns>
	public static string? ToTitleCase(this string? s)
		=> s is not null ? CultureInfo.InvariantCulture.TextInfo.ToTitleCase(s.ToLower()) : null;
}