namespace System;

/// <summary>
/// Class EnumerableExtensions.
/// </summary>
public static partial class EnumerableExtensions
{
	/// <summary>
	/// Fors the each.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="this">The this.</param>
	/// <param name="action">The action.</param>
	public static void ForEach<T>(this IEnumerable<T> @this, Action<T> action)
	{
		foreach (T item in @this)
		{
			action(item);
		}
	}
}