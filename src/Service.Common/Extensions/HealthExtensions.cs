namespace System;

using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.Extensions.DependencyInjection;

/// <summary>
/// Class HealthExtensions.
/// </summary>
public static partial class HealthExtensions
{
	/// <summary>
	/// Adds the health checks and returns the builder.
	/// </summary>
	/// <param name="services">The services.</param>
	/// <returns>IServiceCollection.</returns>
	public static IHealthChecksBuilder AddDefaultHealthChecks(this IServiceCollection services)
	{
		return services.AddHealthChecks()
			.AddDiskStorageHealthCheck(options => options.CheckAllDrives = true);
	}

	/// <summary>
	/// Uses the health checks.
	/// </summary>
	/// <param name="endpoints">The endpoints.</param>
	/// <returns>IApplicationBuilder.</returns>
	public static IEndpointRouteBuilder UseDefaultHealthChecks(this IEndpointRouteBuilder endpoints)
	{
		endpoints.MapHealthChecks("/health", new HealthCheckOptions
		{
			Predicate = (check) => check.Tags.Contains("all")
		});
		endpoints.MapHealthChecks("/health/startup", new HealthCheckOptions
		{
			Predicate = (check) => check.Tags.Contains("startup")
		});
		endpoints.MapHealthChecks("/health/liveness", new HealthCheckOptions
		{
			Predicate = (check) => check.Tags.Contains("liveness")
		});
		endpoints.MapHealthChecks("/health/readiness", new HealthCheckOptions
		{
			Predicate = (check) => check.Tags.Contains("readiness")
		});
		return endpoints;
	}
}