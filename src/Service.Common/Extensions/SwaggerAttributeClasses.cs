namespace System;

using Apex.Service.AuthHandlers.Scheme;
using Microsoft.AspNetCore.Authorization;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

/// <summary>
/// Class SwaggerOperationFilter.
/// Implements the <see cref="Swashbuckle.AspNetCore.SwaggerGen.IOperationFilter" />
/// </summary>
/// <seealso cref="Swashbuckle.AspNetCore.SwaggerGen.IOperationFilter" />
public sealed partial class SwaggerOperationFilter : IOperationFilter
{
	/// <summary>
	/// Applies the specified operation.
	/// </summary>
	/// <param name="operation">The operation.</param>
	/// <param name="context">The context.</param>
	public void Apply(OpenApiOperation operation, OperationFilterContext context)
	{
		var versionParameter = operation.Parameters.SingleOrDefault(p => p.Name == "version");
		if (versionParameter is not null)
		{
			operation.Parameters.Remove(versionParameter);
		}

		var customAttributes = context.MethodInfo.DeclaringType!.GetCustomAttributes(typeof(AuthorizeAttribute), true);
		var directCustomAttributes = context.MethodInfo.GetCustomAttributes(typeof(AuthorizeAttribute), true);
		if (customAttributes is not null && directCustomAttributes is not null)
		{
			var attributes = customAttributes
				.Union(directCustomAttributes)
				.OfType<AuthorizeAttribute>();

			if (attributes.Any())
			{
				var attr = attributes.ToList()[0];

				// Add what should be show inside the security section
				var securityInfos = new List<string>
				{
					$"{nameof(AuthorizeAttribute.Policy)}:{attr.Policy}",
					$"{nameof(AuthorizeAttribute.Roles)}:{attr.Roles}",
					$"{nameof(AuthorizeAttribute.AuthenticationSchemes)}:{attr.AuthenticationSchemes}"
				};

				switch (attr.AuthenticationSchemes)
				{
					case var p when p == "bearer":
					default:
						{
							operation.Responses.Add("401", new OpenApiResponse { Description = "Unauthorized" });
							operation.Security = new List<OpenApiSecurityRequirement>()
							{
								new OpenApiSecurityRequirement()
								{
									{
										new OpenApiSecurityScheme
										{
											Reference = new OpenApiReference
											{
												Id = "bearer", // Must fit the defined Id of SecurityDefinition in global configuration
												Type = ReferenceType.SecurityScheme
											}
										},
										securityInfos
									}
								}
							};

							var parameters = operation.Parameters;
							parameters.Add(new OpenApiParameter
							{
								Name = JiffyAuthSchemeConstants.HEADER_X_Jiffy_Tenant_ID,
								Description = "TenantId value",
								In = ParameterLocation.Header,
								Required = false,
							});
							parameters.Add(new OpenApiParameter
							{
								Name = JiffyAuthSchemeConstants.HEADER_X_Jiffy_App_ID,
								Description = "AppId value",
								In = ParameterLocation.Header,
								Required = false,
							});
							parameters.Add(new OpenApiParameter
							{
								Name = JiffyAuthSchemeConstants.HEADER_X_Jiffy_User_ID,
								Description = "UserId value",
								In = ParameterLocation.Header,
								Required = false,
							});
							break;
						}
				}
			}
		}
	}
}

/// <summary>
/// Class SwaggerReplaceVersionWithExactValueInPathDocumentFilter.
/// Implements the <see cref="Swashbuckle.AspNetCore.SwaggerGen.IDocumentFilter" />
/// </summary>
/// <seealso cref="Swashbuckle.AspNetCore.SwaggerGen.IDocumentFilter" />
public sealed partial class SwaggerReplaceVersionWithExactValueInPathDocumentFilter : IDocumentFilter
{
	/// <summary>
	/// Applies the specified swagger document.
	/// </summary>
	/// <param name="swaggerDoc">The swagger document.</param>
	/// <param name="context">The context.</param>
	public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
	{
		var paths = new OpenApiPaths();
		foreach (var path in swaggerDoc.Paths)
		{
			paths.Add(path.Key.Replace("v{version}", swaggerDoc.Info.Version), path.Value);
		}
		swaggerDoc.Paths = paths;
	}
}