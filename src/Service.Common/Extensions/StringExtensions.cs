namespace System;

/// <summary>
/// Class StringExtensions.
/// </summary>
public static partial class StringExtensions
{
	/// <summary>
	/// Limits the string to the specified length and adds ellipsis if possible
	/// </summary>
	/// <param name="source">The source.</param>
	/// <param name="length">The limit.</param>
	/// <returns>System.Nullable&lt;System.String&gt;.</returns>
	public static string? Limit(this string? source, int length = 10)
		=> source is null || source.Length <= length
			? source
			: string.Concat(source.AsSpan(0, length), "...");

	/// <summary>
	/// Returns not null first value
	/// </summary>
	/// <param name="list">The list.</param>
	/// <returns>System.Nullable&lt;System.String&gt;.</returns>
	public static string? Coalesce(this string?[] list)
		=> list.First(s => !s.IsNullOrEmpty());
}