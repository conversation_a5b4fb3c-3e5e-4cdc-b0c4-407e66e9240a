using Apex.Models;
using Apex.Service.AuthHandlers.Scheme;
using System.Security.Claims;

namespace Microsoft.AspNetCore.Mvc;

/// <summary>
///
/// </summary>
public static partial class ControllerExtensions
{
	/// <summary>
	/// Gets the user security information.
	/// </summary>
	/// <param name="controller">The controller.</param>
	/// <returns>IActionResult.</returns>
	public static UserSecurityInfo GetUserSecurityInfo(this ControllerBase controller)
		=> new UserSecurityInfo(controller.GetCurrentTenantId(), controller.GetCurrentAppId(), controller.GetCurrentUserId(), null);

	/// <summary>
	/// returns Ok(instance) if instance is valid else BadRequest(error).
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="controller">The controller.</param>
	/// <param name="instance">The instance.</param>
	/// <param name="error">The error.</param>
	/// <returns>IActionResult.</returns>
	public static IActionResult OkIf<T>(this ControllerBase controller, T instance, object? error = null)
	{
		return instance is not null
			? controller.Ok(instance)
			: controller.BadRequest(error);
	}

	/// <summary>
	/// Gets the current tenant identifier.
	/// </summary>
	/// <param name="controller">The controller.</param>
	/// <returns>System.String.</returns>
	public static string GetCurrentTenantId(this ControllerBase controller)
	{
		return controller.User.GetCurrentTenantId();
	}

	/// <summary>
	/// Gets the current tenant identifier.
	/// </summary>
	/// <param name="user">The user.</param>
	/// <returns></returns>
	public static string GetCurrentTenantId(this ClaimsPrincipal user)
	{
		return user.Claims.First(c => c.Type == JiffyAuthSchemeConstants.CLAIM_Jiffy_Tenant_ID).Value;
	}

	/// <summary>
	/// Gets the current user identifier.
	/// </summary>
	/// <param name="controller">The controller.</param>
	/// <returns>System.String.</returns>
	public static string GetCurrentUserId(this ControllerBase controller)
	{
		return controller.User.GetCurrentUserId();
	}

	/// <summary>
	/// Gets the current user identifier.
	/// </summary>
	/// <param name="user">The user.</param>
	/// <returns></returns>
	public static string GetCurrentUserId(this ClaimsPrincipal user)
	{
		return user.Claims.First(c => c.Type == JiffyAuthSchemeConstants.CLAIM_Jiffy_User_ID).Value;
	}

	/// <summary>
	/// Gets the current app identifier.
	/// </summary>
	/// <param name="controller">The controller.</param>
	/// <returns>System.String.</returns>
	public static string GetCurrentAppId(this ControllerBase controller)
	{
		return controller.User.GetCurrentAppId();
	}

	/// <summary>
	/// Gets the current app identifier.
	/// </summary>
	/// <param name="user">The user.</param>
	/// <returns></returns>
	public static string GetCurrentAppId(this ClaimsPrincipal user)
	{
		return user.Claims.First(c => c.Type == JiffyAuthSchemeConstants.CLAIM_Jiffy_App_ID).Value;
	}

	/// <summary>
	/// Gets the current app identifier.
	/// </summary>
	/// <param name="controller">The controller.</param>
	/// <returns>System.String.</returns>
	public static string? GetCurrentTargetAppId(this ControllerBase controller)
	{
		return controller.User.GetCurrentTargetAppId();
	}

	/// <summary>
	/// Gets the current app target identifier.
	/// </summary>
	/// <param name="user">The user.</param>
	/// <returns></returns>
	public static string? GetCurrentTargetAppId(this ClaimsPrincipal user)
	{
		return user.Claims.FirstOrDefault(c => c.Type == JiffyAuthSchemeConstants.CLAIM_Jiffy_Target_App_ID)?.Value;
	}

	/// <summary>
	/// Gets the current app target identifier.
	/// </summary>
	/// <param name="controller">The controller.</param>
	/// <returns>System.String.</returns>
	public static string GetCurrentTargetAppIdOrAppId(this ControllerBase controller)
	{
		return controller.User.GetCurrentTargetAppIdOrAppId();
	}

	/// <summary>
	/// Gets the current app target identifier.
	/// </summary>
	/// <param name="user">The user.</param>
	/// <returns></returns>
	public static string GetCurrentTargetAppIdOrAppId(this ClaimsPrincipal user)
	{
		return user.GetCurrentTargetAppId() ?? user.GetCurrentAppId();
	}

	/// <summary>
	/// Gets the current connector component identifier.
	/// </summary>
	/// <param name="controller">The controller.</param>
	/// <returns>System.String.</returns>
	public static string? GetCurrentConnectorComponentId(this ControllerBase controller)
	{
		controller.Request.Headers.TryGetValue("x-jiffy-component-id", out var stringValues);
		return stringValues;
	}

	/// <summary>
	/// Gets the current connector authentication component identifier.
	/// </summary>
	/// <param name="controller">The controller.</param>
	/// <returns>System.String.</returns>
	public static string? GetCurrentConnectorAuthenticationComponentId(this ControllerBase controller)
	{
		controller.Request.Headers.TryGetValue("x-jiffy-connector-auth-id", out var stringValues);
		return stringValues;
	}

	/// <summary>
	/// Registers the file stream dispose.
	/// </summary>
	/// <param name="fileStream">The file stream.</param>
	/// <param name="context">The context.</param>
	/// <returns>IDisposable.</returns>
	public static void RegisterForFileStreamDispose(this FileStream fileStream, HttpContext context)
	{
		context.Response.RegisterForDispose(new FileStreamDeleter(fileStream));
	}

	/// <summary>
	/// Registers the file path for delete and dispose.
	/// </summary>
	/// <param name="filePath">The file path.</param>
	/// <param name="context">The context.</param>
	/// <returns>IDisposable.</returns>
	public static void RegisterFileDispose(this string filePath, HttpContext context)
	{
		context.Response.RegisterForDispose(new FileStreamDeleter(filePath));
	}

	/// <summary>
	/// internal class to track file stream deletions on didpose
	/// </summary>
	internal sealed class FileStreamDeleter : IDisposable
	{
		/// <summary>
		/// The file stream
		/// </summary>
		private FileStream? _fileStream;

		/// <summary>
		/// The file path
		/// </summary>
		private string? _filePath;

		/// <summary>
		/// Initializes a new instance of the <see cref="FileStreamDeleter" /> class.
		/// </summary>
		/// <param name="filePath">The file path.</param>
		public FileStreamDeleter(string filePath)
		{
			_filePath = filePath;
		}

		/// <summary>
		/// Initializes a new instance of the <see cref="FileStreamDeleter"/> class.
		/// </summary>
		/// <param name="fs">The fs.</param>
		public FileStreamDeleter(FileStream fs)
		{
			_fileStream = fs;
			_filePath = fs.Name;
		}

		/// <summary>
		/// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
		/// </summary>
		public void Dispose()
		{
			if (_fileStream is not null)
			{
				var tempfs = _fileStream;
				_fileStream = null;

				tempfs.Dispose();
			}

			if (_filePath is not null)
			{
				File.Delete(_filePath);
				_filePath = null;
			}
		}
	}
}