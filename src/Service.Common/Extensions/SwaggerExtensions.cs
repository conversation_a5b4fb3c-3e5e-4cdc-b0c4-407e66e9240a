namespace System;

using Apex;
using Asp.Versioning;
using Asp.Versioning.ApiExplorer;
using Microsoft.OpenApi.Models;

/// <summary>
/// Class SwaggerExtensions.
/// </summary>
public static partial class SwaggerExtensions
{
	/// <summary>
	/// Adds the swagger.
	/// </summary>
	/// <param name="services">The services.</param>
	/// <param name="info">The information.</param>
	/// <returns>IServiceCollection.</returns>
	public static IServiceCollection AddDefaultSwagger(this IServiceCollection services, ServiceStartupInfo info)
	{
		services.AddApiVersioning(options =>
		{
			options.DefaultApiVersion = new ApiVersion(1, 0);
			options.AssumeDefaultVersionWhenUnspecified = true;
			options.ReportApiVersions = true;
			options.ApiVersionReader = null!;
		})
			.AddApiExplorer(options =>
			{
				options.DefaultApiVersion = new ApiVersion(1, 0);
				options.AddApiVersionParametersWhenVersionNeutral = true;
				options.AssumeDefaultVersionWhenUnspecified = true;
				options.GroupNameFormat = "'v'VVV";
				options.SubstituteApiVersionInUrl = true;
			});

		services.AddEndpointsApiExplorer();

		services.AddSwaggerGen(options =>
		{
			options.SwaggerDoc("v1", new OpenApiInfo { Title = info.SwaggerPageTitle, Version = "v1", Description = info.SwaggerPageDescription });

			options.AddSecurityDefinition("bearer", new OpenApiSecurityScheme
			{
				Description = @"JWT Authorization header using the Bearer scheme. Example: '12345abcdef'",
				Name = "Authorization",
				In = ParameterLocation.Header,
				Type = SecuritySchemeType.Http,
				Scheme = "Bearer",
				BearerFormat = "JWT"
			});

			options.TagActionsBy(api => [api.GroupName]);
			options.CustomSchemaIds(t => t.FullName!.Replace('.', '_').ToLowerInvariant());
			options.OperationFilter<SwaggerOperationFilter>();
			options.DocumentFilter<SwaggerReplaceVersionWithExactValueInPathDocumentFilter>();
			options.DocInclusionPredicate((name, api) => true);

			foreach (var filePath in Directory.EnumerateFiles(AppContext.BaseDirectory, info.SwaggerCommentsFilePattern))
			{
				options.IncludeXmlComments(filePath);
			}
		});

		return services;
	}

	/// <summary>
	/// Uses the swagger.
	/// </summary>
	/// <param name="app">The application.</param>
	/// <param name="provider">The provider.</param>
	/// <returns>IApplicationBuilder.</returns>
	public static IApplicationBuilder UseDefaultSwagger(this IApplicationBuilder app, IApiVersionDescriptionProvider provider)
	{
		// API documentation
		app.UseSwagger(options =>
		{
			options.RouteTemplate = "swagger-ui/{documentName}/swagger.json";
		});

		app.UseSwaggerUI(options =>
		{
			options.RoutePrefix = "swagger-ui";
			var apiVersionDescriptions = provider.ApiVersionDescriptions;
			if (apiVersionDescriptions is not null)
			{
				foreach (var majorVersion in apiVersionDescriptions.Select(v => v.ApiVersion.MajorVersion))
				{
					options.SwaggerEndpoint($"v{majorVersion}/swagger.json", $"v{majorVersion}");
				}
			}

			options.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.List);
			//options.DefaultModelsExpandDepth(-1); // hide models
			options.DisplayOperationId();
			options.EnableFilter();
			options.EnableTryItOutByDefault();
			options.EnableValidator(null);
			options.EnableDeepLinking();
		});

		return app;
	}
}