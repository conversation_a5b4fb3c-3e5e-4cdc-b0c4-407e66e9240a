namespace Apex;

using Apex.Service.AuthHandlers;
using Apex.Service.AuthHandlers.Scheme;
using Asp.Versioning.ApiExplorer;
using Hellang.Middleware.ProblemDetails;
using Microsoft.AspNetCore.HttpLogging;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.AspNetCore.Mvc.Formatters;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using Prometheus;
using Serilog;
using Swashbuckle.AspNetCore.Filters;
using System;
using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using MSL = Microsoft.Extensions.Logging;
using SLL = Serilog.Extensions.Logging;

/// <summary>
/// Class OpenTelemetryInfo.
/// </summary>
public sealed record OpenTelemetryInfo
(
	string ServiceName,
	Action<TracerProviderBuilder>? TraceBuilderSetup = null,
	Action<MeterProviderBuilder>? MeterBuilderSetup = null,
	Func<HttpContext, bool>? RequestFilter = null
);

/// <summary>
/// Diagnostics config...
/// </summary>
public sealed class DiagnosticsConfig
{
	/// <summary>
	/// Initializes the instance.
	/// </summary>
	/// <param name="serviceName">Name of the service.</param>
	public static DiagnosticsConfig InitInstance(string serviceName)
	{
		return _instance = new DiagnosticsConfig
		{
			ServiceName = serviceName,
			ActivitySource = new(serviceName)
		};
	}

	/// <summary>
	/// The instance
	/// </summary>
	static DiagnosticsConfig? _instance;

	/// <summary>
	/// Gets the instance.
	/// </summary>
	/// <value>The instance.</value>
	public static DiagnosticsConfig Instance => _instance!;

	/// <summary>
	/// Gets the name of the service.
	/// </summary>
	/// <value>The name of the service.</value>
	public required string ServiceName { get; init; }

	/// <summary>
	/// Gets the activity source.
	/// </summary>
	/// <value>The activity source.</value>
	public required ActivitySource ActivitySource { get; init; }
}

/// <summary>
/// Class ServiceStartupInfo.
/// </summary>
public sealed record ServiceStartupInfo
(
	string SwaggerPageTitle,
	string SwaggerPageDescription,
	string SwaggerCommentsFilePattern,
	string BasePath,
	OpenTelemetryInfo? TelemetryInfo = null
);

/// <summary>
/// Class BaseServiceStartup - cannot be tested with a host.
/// </summary>
/// <typeparam name="T"></typeparam>
[ExcludeFromCodeCoverage]
public abstract partial class BaseServiceStartup<T>
{
	/// <summary>
	/// Initializes a new instance of the <see cref="BaseServiceStartup{T}" /> class.
	/// </summary>
	/// <param name="configuration">The configuration.</param>
	/// <param name="webHostEnvironment">The web host environment.</param>
	/// <param name="info">The information.</param>
	protected BaseServiceStartup(IConfiguration configuration, IHostEnvironment webHostEnvironment, ServiceStartupInfo info)
	{
		Configuration = configuration;
		WebHostEnvironment = webHostEnvironment;
		StartupInfo = info;

		var logger = Log.Logger = BaseProgram<T>.ConfigureLogging(new LoggerConfiguration(), configuration) // Sensitive
			.CreateLogger();
		_logger = new SLL.SerilogLoggerProvider(logger).CreateLogger(typeof(T).FullName!);
		_logger.LogInformation("INIT - StartupType={StartupType}", this.GetType().AssemblyQualifiedName);
	}

	/// <summary>
	/// The logger
	/// </summary>
	protected readonly MSL.ILogger _logger;

	/// <summary>
	/// Gets the startup information.
	/// </summary>
	/// <value>The startup information.</value>
	public ServiceStartupInfo StartupInfo { get; init; }

	/// <summary>
	/// Gets the web host environment.
	/// </summary>
	/// <value>The web host environment.</value>
	public IHostEnvironment WebHostEnvironment { get; }

	/// <summary>
	/// Gets the configuration.
	/// </summary>
	/// <value>The configuration.</value>
	public IConfiguration Configuration { get; }

	/// <summary>
	/// Configures the services.
	/// </summary>
	/// <param name="services">The services.</param>
	/// <param name="applicationParts">The application parts.</param>
	/// <exception cref="ServiceException">$"configuration DeploymentOptions:ReleaseNamespace could not be found, this is needed for enabling tracing</exception>
	/// <exception cref="ServiceException">$"configuration DeploymentOptions:OtlpExporterUrl could not be found, this is needed for enabling tracing</exception>
	public void BaseConfigureServices(IServiceCollection services, IEnumerable<Assembly>? applicationParts = null)
	{
		var configuration = Configuration;
		var webHostEnvironment = WebHostEnvironment;

		services.AddSingleton(configuration);
		services.AddSingleton(configuration)
			.AddOptions();

		// used for tid/appid => userid, among other things
		services.AddMemoryCache();

		// Remove no needed mapped json
		static void ResetMediaTypeCollection(MediaTypeCollection mt)
		{
			mt.Remove("text/json");

#if IGNORING_BELOW
			// below causing all authorization using application/problem+json content type to return 406
			mt.Remove("application/*+json");
#endif
		}

		services.AddProblemDetails(opts =>
		{
			opts.IncludeExceptionDetails = (ctx, ex) => webHostEnvironment.IsDevelopment() || webHostEnvironment.IsStaging();

			opts.Map<Exception>((ctx, ex) =>
			{
				var factory = ctx.RequestServices.GetRequiredService<ProblemDetailsFactory>();
				return factory.CreateProblemDetails(ctx, title: ex.Message);
			});

			ResetMediaTypeCollection(opts.ContentTypes);
		});

		services.AddAuthentication(options => options.DefaultScheme = JiffyAuthSchemeConstants.JiffyAuthScheme)
			.AddScheme<JiffyAuthSchemeOptions, JiffyAuthHandler>(JiffyAuthSchemeConstants.JiffyAuthScheme, options => { });

		var mvcBuilder = services
			.AddRouting(options => options.LowercaseUrls = true)
			.AddControllers()
			.AddMvcOptions(c =>
			{
				c.InputFormatters.OfType<SystemTextJsonInputFormatter>().ForEach(f => ResetMediaTypeCollection(f.SupportedMediaTypes));
				c.OutputFormatters.OfType<SystemTextJsonOutputFormatter>().ForEach(f => ResetMediaTypeCollection(f.SupportedMediaTypes));
			})
			.AddJsonOptions(options =>
			{
				options.JsonSerializerOptions.ConfigureDefaults();
			});

		if (applicationParts is not null)
		{
			var parts = mvcBuilder.PartManager.ApplicationParts;
			foreach (var a in applicationParts)
			{
				parts.Add(new AssemblyPart(a));
			}
		}

		var info = StartupInfo;
		services.AddDefaultSwagger(info);
		services.AddSwaggerGen(options => options.ExampleFilters());
		services.AddSwaggerExamplesFromAssemblyOf<T>();

		services.AddCors(o => o.AddPolicy("AllowAll", builder =>
		{
			builder
				.SetIsOriginAllowed(_ => true)
				.AllowAnyMethod()
				.AllowAnyHeader()
				;
		}));

		services.AddHttpLogging(logging =>
		{
			logging.LoggingFields = HttpLoggingFields.All | HttpLoggingFields.RequestQuery;
			var rh = logging.RequestHeaders;

			// REF: https://jiffy-ai.atlassian.net/wiki/spaces/ET/pages/**********/Headers+for+Authentication+related+Information
			var KNOWNHEADERS = new string[]
			{
				JiffyAuthSchemeConstants.HEADER_X_Jiffy_Tenant_ID,
				JiffyAuthSchemeConstants.HEADER_X_Jiffy_App_ID,
				JiffyAuthSchemeConstants.HEADER_X_Jiffy_User_ID,
				"x-jiffy-component-id",
				"x-jiffy-connector-auth-id",
				"X-Jiffy-Filter-Ids",
				"X-Jiffy-Acl",
				"x-b3-traceid",
				"x-b3-spanid",
				"x-b3-parentspanid",
				"x-b3-sampled",
				"x-request-id",
				"x-envoy-attempt-count",
				"x-envoy-external-address",
				"x-forwarded-for",
				"x-forwarded-proto",
				"x-forwarded-port",
				"x-forwarded-client-cert",
				"X-Jiffy-Env-Name",
				"X-Jiffy-Filter-Ids",
				"X-Jiffy-Acl",
				"X-Jiffy-User-RootOrg",
				"X-Jiffy-User-OrgFilter",
				"X-Jiffy-Auth-Info", // REF for JSON structure https://jiffy-ai.atlassian.net/wiki/spaces/ET/pages/**********/Headers+for+Authentication+related+Information#:~:text=49c7%2Daa6a%2Dd06b00cabd85-,X%2DJiffy%2DAuth%2DInfo,-Contains%20authentication%20context
			};
			foreach (var header in KNOWNHEADERS)
			{
				rh.Add(header);
			}
		});

		services.AddDefaultHealthChecks()
			.ForwardToPrometheus();

		// open telemetry
		if (StartupInfo.TelemetryInfo is OpenTelemetryInfo otelInfo && !otelInfo.ServiceName.IsNullOrEmpty())
		{
			// we will need release/environment information
			var releaseName = configuration["DeploymentOptions:ReleaseNamespace"]!;
			if (releaseName.IsNullOrEmpty())
			{
				throw new ServiceException($"configuration DeploymentOptions:ReleaseNamespace could not be found, this is needed for enabling tracing");
			}
			var otlpExporterUrl = configuration["DeploymentOptions:OtlpExporterUrl"]!;
			if (otlpExporterUrl.IsNullOrEmpty())
			{
				throw new ServiceException($"configuration DeploymentOptions:OtlpExporterUrl could not be found, this is needed for enabling tracing");
			}

			var serviceName = otelInfo.ServiceName;
			var serviceNameWithEnvironment = $"{serviceName}.{releaseName}";
			_logger.LogInformation("OTEL - ServiceName={ServiceName} OtlpExporterUrl={OtlpExporterUrl} CurrentDirectory={CurrentDirectory}",
				serviceNameWithEnvironment, otlpExporterUrl, Environment.CurrentDirectory);

			// init global diagnostics source
			DiagnosticsConfig.InitInstance(serviceNameWithEnvironment);

			// tracing
			var otelBuilder = services.AddOpenTelemetry();
			otelBuilder.WithTracing(tracerProviderBuilder =>
			{
				tracerProviderBuilder
					.AddSource(serviceNameWithEnvironment!) // keeping same as service name
					.ConfigureResource(resource => resource.AddService(serviceNameWithEnvironment))
					.AddAspNetCoreInstrumentation(options =>
					{
						bool MustTrace(HttpContext request)
						{
							var path = request.Request.Path;
							var enableTrace =
							!(
								path.StartsWithSegments("/metrics")
								|| path.StartsWithSegments("/health")
								|| path.StartsWithSegments("/swagger-ui")
							);

							_logger.LogTrace("MUSTTRACE_INCOMING: Path={Path} EnableTrace={EnableTrace}", path, enableTrace);
							return enableTrace;
						}

						// filter - true to collect, false to ignore trace
						options.Filter = MustTrace;
					})
					.AddHttpClientInstrumentation(options =>
					{
						bool MustTrace(HttpRequestMessage request)
						{
							var uri = request.RequestUri;
							_logger.LogTrace("MUSTTRACE_OUTGOING: Uri={Uri}", uri);
							return true;
						}

						// filter - true to collect, false to ignore trace
						options.FilterHttpRequestMessage = MustTrace;
						options.RecordException = true;
					})
					.AddOtlpExporter(opts =>
					{
						opts.Endpoint = new Uri(otlpExporterUrl);
						opts.ExportProcessorType = OpenTelemetry.ExportProcessorType.Simple;
					})
					.SetSampler(new AlwaysOnSampler());
			});

			// metrics
			otelBuilder.WithMetrics(metricsProviderBuilder =>
			{
				metricsProviderBuilder
					.AddAspNetCoreInstrumentation()
					.AddHttpClientInstrumentation()
					.AddOtlpExporter(opts =>
					{
						opts.Endpoint = new Uri(otlpExporterUrl);
						opts.ExportProcessorType = OpenTelemetry.ExportProcessorType.Simple;
					});
			});
		}
	}

	/// <summary>
	/// Configures the specified application.
	/// </summary>
	/// <param name="app">The application.</param>
	/// <param name="provider">The provider.</param>
	/// <param name="routeBuilder">The route builder.</param>
	public void BaseConfigure(IApplicationBuilder app, IApiVersionDescriptionProvider provider, Action<IEndpointRouteBuilder>? routeBuilder = null)
	{
		app.UseProblemDetails();
		app.UseCors("AllowAll");

		var info = StartupInfo;
		app.UsePathBase(new PathString(info.BasePath));

		app.UseDefaultSwagger(provider);

		// HTTP logging
		var bp = StartupInfo.BasePath;
		var EXCLUSIONPATHS = new PathString[] { "/swagger-ui", "/metrics", "/health", $"{bp}/swagger-ui", $"{bp}/metrics", $"{bp}/health" };
		app.UseWhen(ctx =>
		{
			var path = ctx.Request.Path;
			return !EXCLUSIONPATHS.Any(pattern => path.StartsWithSegments(pattern));
		}, appBuilder => appBuilder.UseHttpLogging());
		app.UseRouting();

		var configuration = Configuration;
		var prometheusPort = configuration.GetValue<int>("PROMETHEUS_PORT");
		app.UseMetricServer(prometheusPort); // prometheus

		app.UseHttpMetrics();
		app.UseAuthorization();

		var healthPort = configuration.GetValue<int>("HEALTH_PORT");
		app.UseHealthChecks("/health", healthPort); // health
		app.UseEndpoints(endpoints =>
		{
			routeBuilder?.Invoke(endpoints);

			endpoints.MapControllers();
			endpoints.UseDefaultHealthChecks();
		});
		app.UseMetricServer(9012);
	}
}