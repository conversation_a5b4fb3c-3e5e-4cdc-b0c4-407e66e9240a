namespace Apex;

using Prometheus.DotNetRuntime;
using Serilog;
using Serilog.Templates;
using System.Diagnostics.CodeAnalysis;

/// <summary>
/// Class BaseProgramStartupInfo. This class cannot be inherited.
/// </summary>
public sealed partial class BaseProgramStartupInfo
{
	/// <summary>
	/// Gets the arguments.
	/// </summary>
	/// <value>The arguments.</value>
	public string[]? Args { get; init; }

	/// <summary>
	/// Gets the listen port.
	/// </summary>
	/// <value>The listen port.</value>
	public int ListenPort { get; init; } = DefaultPorts.LISTEN_PORT;

	/// <summary>
	/// Gets the health port.
	/// </summary>
	/// <value>The health port.</value>
	public int HealthPort { get; init; } = DefaultPorts.HEALTH_PORT;

	/// <summary>
	/// Gets the prometheus port.
	/// </summary>
	/// <value>The prometheus port.</value>
	public int PrometheusPort { get; init; } = DefaultPorts.PROMETHEUS_PORT;

	/// <summary>
	/// Gets or sets the application configuration callback.
	/// </summary>
	/// <value>The application configuration callback.</value>
	public Func<IConfigurationBuilder, IConfigurationBuilder>? AppConfigurationCallback { get; init; }
}

/// <summary>
/// Class ProgramMetricsInfo.
/// </summary>
internal static class ProgramMetricsInfo
{
	/// <summary>
	/// The lock
	/// </summary>
	public static readonly object Lock = new();

	/// <summary>
	/// Gets or sets the collector.
	/// </summary>
	/// <value>The collector.</value>
	public static IDisposable? Collector { get; set; }
}

/// <summary>
/// Class BaseProgram - cannot be tested with a host.
/// </summary>
/// <typeparam name="TStartup">The type of the t startup.</typeparam>
[ExcludeFromCodeCoverage]
public static partial class BaseProgram<TStartup>
{
	/// <summary>
	/// Defines the entry point of the application.
	/// </summary>
	/// <param name="startupInfo">The startup information.</param>
	/// <returns>IHost.</returns>
	public static IHost Main(BaseProgramStartupInfo startupInfo)
	{
		var args = startupInfo.Args;

		var host = Host.CreateDefaultBuilder(args)
			.ConfigureAppConfiguration((context, configure) =>
			{
				var cb = configure
					.AddYamlFile("appsettings.yaml", optional: false, reloadOnChange: false)
					.AddYamlFile(Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), "conf", "appsettings.yaml"), optional: true, reloadOnChange: false)
					.AddYamlFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.yaml", optional: true, reloadOnChange: false);

				var appConfigurationCallback = startupInfo.AppConfigurationCallback;
				if (appConfigurationCallback is not null)
				{
					cb = appConfigurationCallback.Invoke(cb!);
				}

				cb.AddEnvironmentVariables();

				if (args is not null)
				{
					cb.AddCommandLine(args);
				}
			})

			// clear current and add serilog for structured logging
			.ConfigureLogging((context, logging) =>
			{
				logging.ClearProviders();
			})
			.UseSerilog((context, logging) => ConfigureLogging(logging, context.Configuration))
			.ConfigureWebHostDefaults(webBuilder =>
			{
				var listenPort = startupInfo.ListenPort;
				var healthPort = startupInfo.HealthPort;
				var prometheusPort = startupInfo.PrometheusPort;

				webBuilder
					.CaptureStartupErrors(true)

					// use alternate ports if required
					.UseSetting("HEALTH_PORT", healthPort.ToString())
					.UseSetting("PROMETHEUS_PORT", prometheusPort.ToString())

					.UseStartup(typeof(TStartup))
					.UseKestrel(options =>
					{
						options.ListenAnyIP(listenPort); // http
						options.ListenAnyIP(healthPort); // health endpoint
						options.ListenAnyIP(prometheusPort); // prometheus
					});
			})
			.Build();

		return SetupMetricsCollector(host);
	}

	/// <summary>
	/// Configures the logging.
	/// </summary>
	/// <param name="logging">The logging.</param>
	/// <param name="configuration">The configuration.</param>
	/// <returns></returns>
	internal static LoggerConfiguration ConfigureLogging(LoggerConfiguration logging, IConfiguration configuration)
		=> logging
			.ReadFrom.Configuration(configuration)

			// exclude all health/metrics logs
			.Filter.ByExcluding(c => c.Properties.TryGetValue("RequestPath", out var pathValue)
				&& (pathValue.ToString() is string pathStrinValue)
				&& (pathStrinValue.StartsWith("\"/metrics") || pathStrinValue.StartsWith("\"/health"))
			)

			.Enrich.FromLogContext()
				//Needed levels: DEBUG, INFO, WARN, ERROR, FATAL
				.WriteTo.Console(new ExpressionTemplate("{ {timestamp: ToString(UtcDateTime(@t),'o')," +
					"level: if @l='Trace' then 'TRACE'" +
					" else if @l='Debug' then 'DEBUG'" +
					" else if @l='Information' then 'INFO'" +
					" else if @l='Warning' then 'WARN'" +
					" else if @l='Error' then 'ERROR'" +
					" else if @l='Fatal' then 'FATAL'" +
					" else @l, message: @mt, properties: @p, exception: @x} }\n"));

	/// <summary>
	/// Setups the metrics collector.
	/// </summary>
	/// <param name="host">The host.</param>
	/// <returns>Microsoft.Extensions.Hosting.IHost.</returns>
	private static IHost SetupMetricsCollector(IHost host)
	{
		// metrics collector...
		if (ProgramMetricsInfo.Collector is null)
		{
			lock (ProgramMetricsInfo.Lock)
			{
				if (ProgramMetricsInfo.Collector is null)
				{
					ProgramMetricsInfo.Collector = DotNetRuntimeStatsBuilder.Customize()
						.WithJitStats(CaptureLevel.Verbose)
						.WithContentionStats(CaptureLevel.Informational)
						.WithGcStats(CaptureLevel.Verbose)
						.WithExceptionStats(CaptureLevel.Errors)
						.WithThreadPoolStats(CaptureLevel.Informational)
						.StartCollecting();
					var lifeTime = host.Services.GetRequiredService<Microsoft.Extensions.Hosting.IHostApplicationLifetime>();
					lifeTime.ApplicationStopping.Register(() =>
					{
						ProgramMetricsInfo.Collector.Dispose();
					});
				}
			}
		}
		return host;
	}
}