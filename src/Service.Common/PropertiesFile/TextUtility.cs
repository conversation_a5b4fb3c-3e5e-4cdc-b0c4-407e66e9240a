namespace PropertiesReader.Providers;

/// <summary>
/// Text utility.
/// </summary>
internal static class TextUtility
{
	/// <summary>
	/// Convert a character which represents a hex digit
	/// (<c>0-9</c>, <c>A-F</c>, <c>a-f</c>) to an integer
	/// (from 0 to 15). If the given character is not a hex
	/// digit, <c>null</c> is returned.
	/// </summary>
	///
	/// <param name="c">
	/// A hex digit (<c>0-9</c>, <c>A-F</c>, <c>a-f</c>).
	/// </param>
	public static int? HexToInt(char c)
	{
		// 0-9
		if ('0' <= c && c <= '9')
		{
			return (c - '0');
		}

		// A-F
		if ('A' <= c && c <= 'F')
		{
			return (c - 'A' + 10);
		}

		// a-f
		if ('a' <= c && c <= 'f')
		{
			return (c - 'a' + 10);
		}

		// Invalid character.
		return null;
	}
}