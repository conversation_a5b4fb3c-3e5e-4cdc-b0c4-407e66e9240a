namespace PropertiesReader.Providers;

/// <summary>
/// Class PropertiesFileConfigurationSource.
/// Implements the <see cref="IConfigurationSource" />
/// </summary>
/// <seealso cref="IConfigurationSource" />
public sealed class PropertiesFileConfigurationSource : IConfigurationSource
{
	/// <summary>
	/// The file path
	/// </summary>
	private readonly string _filePath;

	/// <summary>
	/// The isoptional flag to indicate if the configuration file is required
	/// </summary>
	private readonly bool _isOptional;

	/// <summary>
	/// The comparer
	/// </summary>
	private readonly StringComparer? _comparer;

	/// <summary>
	/// Initializes a new instance of the <see cref="PropertiesFileConfigurationSource" /> class.
	/// </summary>
	/// <param name="filePath">The file path.</param>
	/// <param name="isOptional">if set to <c>true</c> [is optional].</param>
	/// <param name="comparer">The comparer.</param>
	public PropertiesFileConfigurationSource(string filePath, bool isOptional = false, StringComparer? comparer = null) =>
		(_filePath, _isOptional, _comparer) = (filePath, isOptional, comparer);

	/// <summary>
	/// Builds the <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider" /> for this source.
	/// </summary>
	/// <param name="builder">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder" />.</param>
	/// <returns>An <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider" /></returns>
	public IConfigurationProvider Build(IConfigurationBuilder builder) =>
		new EntityConfigurationProvider(_filePath, _isOptional, _comparer);
}