using Apex;

namespace PropertiesReader.Providers;

/// <summary>
/// Class EntityConfigurationProvider.
/// Implements the <see cref="ConfigurationProvider" />
/// </summary>
/// <seealso cref="ConfigurationProvider" />
public sealed class EntityConfigurationProvider : ConfigurationProvider
{
	/// <summary>
	/// The file path
	/// </summary>
	private readonly string _filePath;

	/// <summary>
	/// The isoptional flag to determine if the configuration file is required
	/// </summary>
	private readonly bool _isOptional;

	/// <summary>
	/// The comparer
	/// </summary>
	private readonly StringComparer? _comparer;

	/// <summary>
	/// Initializes a new instance of the <see cref="EntityConfigurationProvider" /> class.
	/// </summary>
	/// <param name="filePath">The file path.</param>
	/// <param name="isOptional">if set to <c>true</c> [is optional].</param>
	/// <param name="comparer">The comparer.</param>
	public EntityConfigurationProvider(string filePath, bool isOptional = false, StringComparer? comparer = null) =>
		(_filePath, _isOptional, _comparer) = (filePath, isOptional, comparer);

	/// <summary>
	/// Loads (or reloads) the data for this provider.
	/// </summary>
	public override void Load()
	{
		if (File.Exists(_filePath))
		{
			using var reader = File.OpenText(_filePath);
			Data = PropertiesLoader.Load(reader, _comparer);
		}
		else
		{
			if (!_isOptional)
			{
				throw new ServiceException($"Unable to find the required configuration file - {_filePath}");
			}

			Data = new Dictionary<string, string?>();
		}
	}
}