using PropertiesReader.Providers;

namespace Microsoft.Extensions.Configuration;

/// <summary>
/// Class ConfigurationBuilderExtensions.
/// </summary>
public static class ConfigurationBuilderExtensions
{
	/// <summary>
	/// Adds the properties file.
	/// </summary>
	/// <param name="builder">The builder.</param>
	/// <param name="filePath">The file path.</param>
	/// <param name="isOptional">if set to <c>true</c> [is optional].</param>
	/// <param name="comparer">The comparer.</param>
	/// <returns>IConfigurationBuilder.</returns>
	public static IConfigurationBuilder AddPropertiesFile(
		this IConfigurationBuilder builder, string filePath, bool isOptional = false, StringComparer? comparer = null)
	{
		return builder.Add(new PropertiesFileConfigurationSource(filePath, isOptional, comparer));
	}
}