using Apex.Models;
using System.Net.Http.Headers;

namespace Apex.Services;

/// <summary>
/// Class CelToJsService.
/// </summary>
public sealed partial class CelToJsService : ICelToJsService
{
	/// <summary>
	/// Cel to js.
	/// </summary>
	/// <param name="expression">The expression.</param>
	/// <param name="userSecurityInfo"></param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	public async Task<BundledExpressionOutput> CelToJsAsync(string expression, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken)
	{
		ArgumentException.ThrowIfNullOrEmpty(expression);
		return await InternalCelToJsAsync(expression, userSecurityInfo, cancellationToken);
	}

	/// <summary>
	/// Cel to js multiline.
	/// </summary>
	/// <param name="expression">The expression.</param>
	/// <param name="userSecurityInfo"></param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	public Task<BundledExpressionOutput> CelToJsMultiAsync(string expression, UserSecurityInfo userSecurityInfo,
		CancellationToken cancellationToken)
	{
		throw new NotImplementedException();
	}

	/// <summary>
	/// Helper method to get request message.
	/// </summary>
	/// <param name="requestUrl">The template URI.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>HttpRequestMessage.</returns>
	private async Task<HttpRequestMessage> InternalGetRequestMessageAsync(string requestUrl, UserSecurityInfo? userInfo, CancellationToken cancellationToken)
	{
		var requestMessage = new HttpRequestMessage
		{
			RequestUri = new Uri(requestUrl),
			Method = HttpMethod.Get,
		};

		var targetHeaders = requestMessage.Headers;
		userInfo?.UpdateWebHeaders(targetHeaders);

		var newAccessToken =
		   await _tokenGenerationService.CheckTokenExpiryAndGenerateTokenAsync(cancellationToken: cancellationToken);
		if (newAccessToken is not null)
		{
			targetHeaders.Authorization = new AuthenticationHeaderValue("Bearer", newAccessToken);
		}

		return requestMessage;
	}

	/// <summary>
	/// cel to js helper.
	/// </summary>
	/// <param name="expression">The expression.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;BundledExpressionOutput&gt; representing the asynchronous operation.</returns>
	private async Task<BundledExpressionOutput> InternalCelToJsAsync(string expression, UserSecurityInfo? userInfo,
		CancellationToken cancellationToken = default)
	{
		var translateResponse = await InternalTranslateAsync(expression, userInfo, cancellationToken);
		var bundledJs = await InternalBundleAsync(translateResponse.JsCode!, userInfo, cancellationToken);

		return new BundledExpressionOutput
		{
			Js = bundledJs,
			InputVariables = translateResponse.InputVariables
		};
	}

	/// <summary>
	/// helper to translate expression to js.
	/// </summary>
	/// <param name="expression">The expression.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;Apex.Services.ExpressionTranslatedOutput&gt;.</returns>
	private async Task<ExpressionTranslatedOutput> InternalTranslateAsync(string expression, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default)
	{
		var url = $"{_options.Value.DvtServiceUrl?.TrimEnd('/')}/v1/translator/expression";
		var requestMessage = await InternalGetRequestMessageAsync(url, userInfo, cancellationToken);
		requestMessage.Method = HttpMethod.Post;
		var body = new { expression };
		requestMessage.Content = JsonContent.Create(body);

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			_logger.LogError("Expression translation failed for expression - {expression},  Non success code while invoking dvt ng  - {errorCode}", expression, responseMessage.StatusCode);
			throw new ExternalRequestException($"unable to invoke/trigger external url - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}");
		}

		return (await responseMessage.Content.ReadFromJsonAsync<ExpressionTranslatedOutput>(SerializationExtensions._SERIALIZEROPTIONS, cancellationToken))!;
	}

	/// <summary>
	/// helper to bundle.
	/// </summary>
	/// <param name="code">The code.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;System.String&gt;.</returns>
	private async Task<string> InternalBundleAsync(string code, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default)
	{
		var url = $"{_options.Value.EsBundleServiceUrl?.TrimEnd('/')}/es-bundle/bundle/";
		var requestMessage = await InternalGetRequestMessageAsync(url, userInfo, cancellationToken);
		requestMessage.Method = HttpMethod.Post;
		var body = new { code = code };
		requestMessage.Content = JsonContent.Create(body);

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			_logger.LogError("Expression bundling failed for code - {code},  Non success code while invoking es bundler  - {errorCode}", code, responseMessage.StatusCode);
			throw new ExternalRequestException($"unable to invoke/trigger external url - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}");
		}

		return (await responseMessage.Content.ReadAsStringAsync(cancellationToken))!;
	}
}