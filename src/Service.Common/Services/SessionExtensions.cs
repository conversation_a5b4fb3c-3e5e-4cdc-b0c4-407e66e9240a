using <PERSON>;
using Polly.Retry;
using System.Net;

namespace Apex.Services;

/// <summary>
/// Class SessionExtensions.
/// </summary>
public static partial class SessionExtensions
{
	/// <summary>
	/// Adds the web connection extensions.
	/// </summary>
	/// <param name="services">The services.</param>
	/// <returns>IServiceCollection.</returns>
	public static IServiceCollection AddWebConnectionExtensions(this IServiceCollection services)
	{
		static HttpClientHandler SetDefaults(HttpClientHandler clientHandler)
		{
			clientHandler.AllowAutoRedirect = true;
			clientHandler.AutomaticDecompression = DecompressionMethods.All;
			clientHandler.ServerCertificateCustomValidationCallback = HttpClientHandler.DangerousAcceptAnyServerCertificateValidator!;
			return clientHandler;
		}

		static void RegisterHttpClientBuilder(IServiceCollection services, IHttpClientBuilder clientBuilder, string key, TimeSpan? clientTimeout = null)
		{
			clientBuilder
				.ConfigurePrimaryHttpMessageHandler(_ => SetDefaults(new()))
				.ConfigureHttpClient(client => client.Timeout = clientTimeout ?? TimeSpan.FromMinutes(3));
			services.AddKeyedTransient(key, (sp, key2) => sp.GetRequiredService<IHttpClientFactory>().CreateClient((string?)key2!));
		}

		var defaultBuilder = services.AddHttpClient("default")
			.ConfigurePrimaryHttpMessageHandler(_ => SetDefaults(new()));
		RegisterHttpClientBuilder(services, defaultBuilder, "default");

		var _10secTimeoutBuilder = services.AddHttpClient("10SecondsTimeout")
			.ConfigurePrimaryHttpMessageHandler(_ => SetDefaults(new()));
		RegisterHttpClientBuilder(services, _10secTimeoutBuilder, "10SecondsTimeout", TimeSpan.FromSeconds(10));

		var defaultLongTimeoutBuilder = services.AddHttpClient("default2HoursTimeout")
			.ConfigurePrimaryHttpMessageHandler(_ => SetDefaults(new()));
		RegisterHttpClientBuilder(services, defaultLongTimeoutBuilder, "default2HoursTimeout", TimeSpan.FromMinutes(120));

		// with max retries
		static void RegisterRetryHttpClient(IServiceCollection services, string key, int retries = 10, TimeSpan? clientTimeout = null)
		{
			var cb = services.AddHttpClient(key)
				.AddPolicyHandler((sp, message) => GetRetryPolicy(sp, message, retries));
			RegisterHttpClientBuilder(services, cb, key, clientTimeout);
		}
		RegisterRetryHttpClient(services, "defaultWithRetry", 10, TimeSpan.FromMinutes(60));
		RegisterRetryHttpClient(services, "defaultWithLargeRetry", 1000, TimeSpan.FromMinutes(120));

		// with forever retries
		static void RegisterForeverRetryHttpClient(IServiceCollection services, string key)
		{
			var cb = services.AddHttpClient(key)
				.AddPolicyHandler(GetForeverRetryPolicy);
			RegisterHttpClientBuilder(services, cb, key);
		}
		RegisterForeverRetryHttpClient(services, "defaultWithInfiniteRetry");

		// default webclient
		services.AddSingleton<IWebConnectionService, WebConnectionService>();

		// default keyed services with retry policies
		static WebConnectionService GetWebConnectionService(IServiceProvider sp, object key)
			=> new(sp.GetRequiredService<IHttpClientFactory>(), (string?)key);
		services.AddKeyedSingleton<IWebConnectionService, WebConnectionService>("10SecondsTimeout", GetWebConnectionService);
		services.AddKeyedSingleton<IWebConnectionService, WebConnectionService>("default2HoursTimeout", GetWebConnectionService);
		services.AddKeyedSingleton<IWebConnectionService, WebConnectionService>("defaultWithRetry", GetWebConnectionService);
		services.AddKeyedSingleton<IWebConnectionService, WebConnectionService>("defaultWithLargeRetry", GetWebConnectionService);
		services.AddKeyedSingleton<IWebConnectionService, WebConnectionService>("defaultWithInfiniteRetry", GetWebConnectionService);
		return services;
	}

	/// <summary>
	/// Determines whether [is retryable status code] [the specified m].
	/// </summary>
	/// <param name="m">The m.</param>
	/// <returns><c>true</c> if [is retryable status code] [the specified m]; otherwise, <c>false</c>.</returns>
	private static bool IsRetryableStatusCode(HttpResponseMessage m)
		=> m.StatusCode switch
		{
			HttpStatusCode.NotFound or HttpStatusCode.BadGateway or HttpStatusCode.TooManyRequests or HttpStatusCode.ServiceUnavailable => true,
			_ => false
		};

	/// <summary>
	/// Sleeps provider for exponential backoff followed by constant provider.
	/// </summary>
	/// <param name="retryAttempt">The retry attempt.</param>
	/// <param name="constantDelay">The constant delay.</param>
	/// <returns>TimeSpan.</returns>
	private static TimeSpan SleepDurationProvider(int retryAttempt, TimeSpan? constantDelay = null)
		=> retryAttempt switch
		{
			<= 7 => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
			_ => constantDelay.GetValueOrDefault(TimeSpan.FromMinutes(3))
		};

	/// <summary>
	/// Gets the retry policy.
	/// </summary>
	/// <param name="services">The services.</param>
	/// <param name="request">The request.</param>
	/// <param name="retries">The retries.</param>
	/// <returns>IAsyncPolicy&lt;HttpResponseMessage&gt;.</returns>
	private static AsyncRetryPolicy<HttpResponseMessage> GetRetryPolicy(IServiceProvider services, HttpRequestMessage request, int retries = 10)
		=> Policy<HttpResponseMessage>
			.HandleResult(IsRetryableStatusCode)
			.WaitAndRetryAsync(retries,
			(retryAttempt) => SleepDurationProvider(retryAttempt),
			onRetry: (outcome, timespan, retryAttempt, context) =>
			{
				var logger = services.GetRequiredService<ILogger<IWebConnectionService>>();
				logger.LogWarning("Retrying request to Url={Url} in Delay={Delay} seconds. Retry attempt Retry={Retry}.",
					request.RequestUri, timespan.TotalSeconds, retryAttempt);
			});

	/// <summary>
	/// Gets the retry policy to retry forever.
	/// </summary>
	/// <param name="services">The services.</param>
	/// <param name="request">The request.</param>
	/// <returns>IAsyncPolicy&lt;HttpResponseMessage&gt;.</returns>
	private static AsyncRetryPolicy<HttpResponseMessage> GetForeverRetryPolicy(IServiceProvider services, HttpRequestMessage request)
		=> Policy<HttpResponseMessage>
			.HandleResult(IsRetryableStatusCode)
			.WaitAndRetryForeverAsync((retryAttempt) => SleepDurationProvider(retryAttempt),
			onRetry: (outcome, retryAttempt, timespan) =>
			{
				var logger = services.GetRequiredService<ILogger<IWebConnectionService>>();
				logger.LogWarning("Retrying request to Url={Url} in Delay={Delay} seconds. Retry attempt Retry={Retry}.",
					request.RequestUri, timespan.TotalSeconds, retryAttempt);
			});
}