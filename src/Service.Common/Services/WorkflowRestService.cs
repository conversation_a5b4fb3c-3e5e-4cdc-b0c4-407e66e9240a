namespace Apex.Services;

using Apex.Options;
using Microsoft.Extensions.Options;

/// <summary>
/// Class WorkflowRestService. This class cannot be inherited.
/// </summary>
/// <param name="_serviceOptions">The service options.</param>
/// <param name="_connection">The connection.</param>
/// <param name="_tokenGeneration">The token generation.</param>
/// <remarks>Initializes a new instance of the <see cref="WorkflowRestService" /> class.</remarks>
public sealed partial class WorkflowRestService(IOptions<ServiceOptions> _serviceOptions, IWebConnectionService _connection, ITokenGenerationService _tokenGeneration)
	: IWorkflowRestService
{

}