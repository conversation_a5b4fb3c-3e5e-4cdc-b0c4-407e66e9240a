using System.Text.Json;
using System.Text.Json.Serialization;

namespace Apex.Services;

/// <summary>
/// Class CustomCertificateRequest. This class cannot be inherited.
/// </summary>
public sealed record CustomCertificateRequest
{
	/// <summary>
	/// Gets or sets the name of the common.
	/// </summary>
	/// <value>
	/// The name of the common.
	/// </value>
	[JsonPropertyName("common_name")]
	public required string CommonName { get; set; }

	/// <summary>
	/// Gets or sets the alt names.
	/// </summary>
	/// <value>
	/// The alt names.
	/// </value>
	[JsonPropertyName("alt_names")]
	public required string AltNames { get; set; }

	/// <summary>
	/// Gets or sets the TTL.
	/// </summary>
	/// <value>
	/// The TTL.
	/// </value>
	[JsonPropertyName("ttl")]
	public required string Ttl { get; set; }
}

/// <summary>
/// Class CustomCertificate. This class cannot be inherited.
/// </summary>
public sealed record CustomCertificate
{
	/// <summary>
	/// Gets or sets the request identifier.
	/// </summary>
	/// <value>The request identifier.</value>
	[JsonPropertyName("request_id")]
	public required string RequestId { get; set; }

	/// <summary>
	/// Gets or sets the lease identifier.
	/// </summary>
	/// <value>The lease identifier.</value>
	[JsonPropertyName("lease_id")]
	public required string LeaseId { get; set; }

	/// <summary>
	/// Gets or sets a value indicating whether this <see cref="CustomCertificate" /> is renewable.
	/// </summary>
	/// <value><c>null</c> if [renewable] contains no value, <c>true</c> if [renewable]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("renewable")]
	public bool Renewable { get; set; }

	/// <summary>
	/// Gets or sets the duration of the lease.
	/// </summary>
	/// <value>The duration of the lease.</value>
	[JsonPropertyName("lease_duration")]
	public int LeaseDuration { get; set; }

	/// <summary>
	/// Gets or sets the data.
	/// </summary>
	/// <value>The data.</value>
	[JsonPropertyName("data")]
	public required CustomCertificateData Data { get; set; }

	/// <summary>
	/// Gets or sets the wrap information.
	/// </summary>
	/// <value>The wrap information.</value>
	[JsonPropertyName("wrap_info")]
	public JsonDocument? WrapInfo { get; set; }

	/// <summary>
	/// Gets or sets the warnings.
	/// </summary>
	/// <value>The warnings.</value>
	[JsonPropertyName("warnings")]
	public JsonDocument? Warnings { get; set; }

	/// <summary>
	/// Gets or sets the authentication.
	/// </summary>
	/// <value>The authentication.</value>
	[JsonPropertyName("auth")]
	public JsonDocument? Auth { get; set; }
}

/// <summary>
/// Class Data. This class cannot be inherited.
/// </summary>
public sealed record CustomCertificateData
{
	/// <summary>
	/// Gets or sets the ca chain.
	/// </summary>
	/// <value>The ca chain.</value>
	[JsonPropertyName("ca_chain")]
	public List<string>? CaChain { get; set; }

	/// <summary>
	/// Gets or sets the certificate.
	/// </summary>
	/// <value>The certificate.</value>
	[JsonPropertyName("certificate")]
	public required string Certificate { get; set; }

	/// <summary>
	/// Gets or sets the expiration.
	/// </summary>
	/// <value>The expiration.</value>
	[JsonPropertyName("expiration")]
	public int? Expiration { get; set; }

	/// <summary>
	/// Gets or sets the issuing ca.
	/// </summary>
	/// <value>The issuing ca.</value>
	[JsonPropertyName("issuing_ca")]
	public string? IssuingCa { get; set; }

	/// <summary>
	/// Gets or sets the private key.
	/// </summary>
	/// <value>The private key.</value>
	[JsonPropertyName("private_key")]
	public string? PrivateKey { get; set; }

	/// <summary>
	/// Gets or sets the type of the private key.
	/// </summary>
	/// <value>The type of the private key.</value>
	[JsonPropertyName("private_key_type")]
	public string? PrivateKeyType { get; set; }

	/// <summary>
	/// Gets or sets the serial number.
	/// </summary>
	/// <value>The serial number.</value>
	[JsonPropertyName("serial_number")]
	public string? SerialNumber { get; set; }
}

/// <summary>
/// Class GetCertificatesResponse. This class cannot be inherited.
/// </summary>
public sealed record GetCertificatesResponse
{
	/// <summary>
	/// Gets or sets the request idd.
	/// </summary>
	/// <value>The request idd.</value>
	[JsonPropertyName("request_id")]
	public string? RequestId { get; set; }

	/// <summary>
	/// Gets or sets the lease identifier.
	/// </summary>
	/// <value>The lease identifier.</value>
	[JsonPropertyName("lease_id")]
	public string? LeaseId { get; set; }

	/// <summary>
	/// Gets or sets the renewable.
	/// </summary>
	/// <value>The renewable.</value>
	[JsonPropertyName("renewable")]
	public bool Renewable { get; set; }

	/// <summary>
	/// Gets or sets the duration of the lease.
	/// </summary>
	/// <value>The duration of the lease.</value>
	[JsonPropertyName("lease_duration")]
	public int LeaseDuration { get; set; }

	/// <summary>
	/// Gets or sets the data.
	/// </summary>
	/// <value>The data.</value>
	[JsonPropertyName("data")]
	public GetCertificatesResponseData Data { get; set; }

	/// <summary>
	/// Gets or sets the wrap information.
	/// </summary>
	/// <value>The wrap information.</value>
	[JsonPropertyName("wrap_info")]
	public JsonDocument? WrapInfo { get; set; }

	/// <summary>
	/// Gets or sets the warnings.
	/// </summary>
	/// <value>The warnings.</value>
	[JsonPropertyName("warnings")]
	public JsonDocument? Warnings { get; set; }

	/// <summary>
	/// Gets or sets the authentication.
	/// </summary>
	/// <value>The authentication.</value>
	[JsonPropertyName("auth")]
	public JsonDocument? Auth { get; set; }
}

/// <summary>
/// Class GetCertificatesResponseData. This class cannot be inherited.
/// </summary>
public sealed record GetCertificatesResponseData
{
	/// <summary>
	/// Gets or sets the keys.
	/// </summary>
	/// <value>The keys.</value>
	[JsonPropertyName("keys")]
	public string[]? Keys { get; set; }
}

/// <summary>
/// Class IVaultService.
/// </summary>
public partial interface IVaultService
{
	/// <summary>
	/// Gets the certificates.
	/// </summary>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	Task<GetCertificatesResponse?> GetCertificatesAsync(CancellationToken cancellationToken = default);

	/// <summary>
	/// Generates the certificate.
	/// </summary>
	/// <param name="request">The request.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	Task<CustomCertificate?> GenerateCertificateAsync(CustomCertificateRequest request, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the certificate.
	/// </summary>
	/// <param name="serialNumber">The serial number.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	Task<CustomCertificate?> GetCertificateAsync(string serialNumber, CancellationToken cancellationToken = default);

	/// <summary>
	/// Revokes the certificate.
	/// </summary>
	/// <param name="serialNumber">The serial number.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	Task<bool> RevokeCertificateAsync(string serialNumber, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the secret value.
	/// </summary>
	/// <param name="key">The key.</param>
	/// <param name="cancellationToken">The cancellation token.</param>
	/// <returns></returns>
	Task<T?> GetSecretValueAsync<T>(string key, CancellationToken cancellationToken = default)
		where T : class;

	/// <summary>
	/// Creates the secret value.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="key">The key.</param>
	/// <param name="value">The value.</param>
	/// <param name="cancellationToken">The cancellation token.</param>
	/// <returns></returns>

	Task<bool> CreateSecretValueAsync<T>(string key, T value, CancellationToken cancellationToken = default)
		where T : class;

	/// <summary>
	/// Deletes the secret value.
	/// </summary>
	/// <param name="key">The key.</param>
	/// <param name="cancellationToken">The cancellation token.</param>
	/// <returns></returns>
	Task DeleteSecretValueAsync(string key, CancellationToken cancellationToken = default);
}