using System.Runtime.Serialization;

namespace Apex.Services;

/// <summary>
/// Class JiffyDriveException.
/// Implements the <see cref="System.Exception" />
/// </summary>
/// <seealso cref="System.Exception" />
[Serializable]
public sealed partial class JiffyDriveException : Exception
{
	/// <summary>
	/// Initializes a new instance of the <see cref="JiffyDriveException" /> class.
	/// </summary>
	/// <param name="errorMessage">The error message.</param>
	/// <returns>InvalidStudentNameException.</returns>
	public JiffyDriveException(string errorMessage)
		: base(errorMessage)
	{
	}

	/// <summary>
	/// Initializes a new instance of the <see cref="JiffyDriveException" /> class.
	/// </summary>
	/// <param name="serializationInfo">The serialization information.</param>
	/// <param name="streamingContext">The streaming context.</param>
	private JiffyDriveException(SerializationInfo serializationInfo, StreamingContext streamingContext)
		: base(serializationInfo, streamingContext)
	{
	}
}