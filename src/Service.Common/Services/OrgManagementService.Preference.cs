using Apex.Models;
using System.Net.Http.Headers;

namespace Apex.Services;

/// <summary>
/// Class OrgManagementService.
/// </summary>
public sealed partial class OrgManagementService
{
	/// <summary>
	/// Gets the user orgs.
	/// </summary>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	/// <exception cref="ArgumentNullException"></exception>
	public async Task<Org?> GetTenantRootOrgAsync(UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.OmsUrl);

		var url = $"{_serviceOptions.Value.OmsUrl!.TrimEnd('/')}/api/v2/org/search?limit=10&offset=0&type=root";
		_logger.LogDebug("GetTenantRootOrgAsync - url - Url={Url}", url);

		var orgList = await InternalGetRequestAsync<List<Org>>(url, userSecurityInfo, cancellationToken: cancellationToken);
		if (orgList is not null && orgList.Count > 0)
		{
			return orgList.First();
		}

		return null;
	}

	/// <summary>
	/// Gets the user orgs.
	/// </summary>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="pageIndex">The page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	/// <exception cref="ArgumentNullException"></exception>
	public async Task<IList<Org>> GetTenantOrgsAsync(UserSecurityInfo userSecurityInfo, int pageIndex = 0, int pageSize = 100, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.OmsUrl);

		var url = $"{_serviceOptions.Value.OmsUrl!.TrimEnd('/')}/api/v1/org?page={pageIndex}&size={pageSize}";
		_logger.LogDebug("GetTenantOrgsAsync - url - Url={Url}", url);

		var wrapped = await InternalGetRequestAsync<WrappedData<Org>>(url, userSecurityInfo, cancellationToken: cancellationToken);
		return wrapped?.Data ?? [];
	}

	/// <summary>
	/// Gets the user orgs.
	/// </summary>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	/// <exception cref="ArgumentNullException"></exception>
	public async Task<IList<Org>> GetUserOrgsAsync(string iamUserId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.OmsUrl);

		var url = $"{_serviceOptions.Value.OmsUrl!.TrimEnd('/')}/api/v1/org/user/{iamUserId}";
		_logger.LogDebug("GetUserOrgsAsync - url - Url={Url}", url);

		var wrapped = await InternalGetRequestAsync<WrappedData<Org>>(url, userSecurityInfo, cancellationToken: cancellationToken);
		return wrapped?.Data ?? [];
	}

	/// <summary>
	/// Gets the user/org preference.
	/// </summary>
	/// <param name="scope">The scope.</param>
	/// <param name="preferenceGroupName">Name of the preference group.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	public Task<Preference?> GetPreferenceAsync(PreferenceScope scope, string preferenceGroupName, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.OmsUrl);
		scope.Validate();

		var url = $"{_serviceOptions.Value.OmsUrl!.TrimEnd('/')}/api/v1/preferences/by-name/{preferenceGroupName}?{GetQueryStringScope(scope)}";
		_logger.LogDebug("GetPreferenceAsync - url - Url={Url}", url);

		return InternalGetRequestAsync<Preference>(url, userSecurityInfo, throwError: false, cancellationToken);
	}

	/// <summary>
	/// Inserts/Updates the user/org preference.
	/// </summary>
	/// <param name="scope">The scope.</param>
	/// <param name="preference">The preference.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	public async Task UpsertPreferenceAsync(PreferenceScope scope, Preference preference, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.OmsUrl);
		scope.Validate();

		try
		{
			var updateUrl = $"{_serviceOptions.Value.OmsUrl!.TrimEnd('/')}/api/v1/preferences/by-name/{preference.Name}?{GetQueryStringScope(scope)}";
			_logger.LogDebug("UpsertPreferenceAsync - update url - Url={Url}", updateUrl);
			await InternalPostRequestAsync(updateUrl, preference, userSecurityInfo, method: HttpMethod.Put, cancellationToken);
		}
		catch
		{
			var insertUrl = $"{_serviceOptions.Value.OmsUrl!.TrimEnd('/')}/api/v1/preferences?{GetQueryStringScope(scope)}";
			_logger.LogDebug("UpsertPreferenceAsync - insert url - Url={Url}", insertUrl);
			await InternalPostRequestAsync(insertUrl, preference, userSecurityInfo, method: HttpMethod.Post, cancellationToken);
		}
	}

	/// <summary>
	/// Deletes the user/org preference.
	/// </summary>
	/// <param name="scope">The scope.</param>
	/// <param name="preferenceName">Name of the preference.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	public Task DeletePreferenceAsync(PreferenceScope scope, string preferenceName, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.OmsUrl);
		scope.Validate();

		var url = $"{_serviceOptions.Value.OmsUrl!.TrimEnd('/')}/api/v1/preferences/by-name/{preferenceName}?{GetQueryStringScope(scope)}";
		_logger.LogDebug("DeletePreferenceAsync - url - Url={Url}", url);

		return InternalPostRequestAsync<object>(url, null, userSecurityInfo, method: HttpMethod.Delete, cancellationToken);
	}

	/// <summary>
	/// Gets the user merged preference (with org).
	/// </summary>
	/// <param name="scope">The scope.</param>
	/// <param name="preferenceGroupName">Name of the preference group.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IList&lt;User&gt;&gt;.</returns>
	/// <exception cref="ArgumentNullException"></exception>
	public Task<IList<PreferenceKeyValue>?> GetMergedPreferenceKeyValuesAsync(PreferenceScope scope, string preferenceGroupName, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.OmsUrl);
		scope.Validate();

		var url = $"{_serviceOptions.Value.OmsUrl!.TrimEnd('/')}/api/v1/preferences/merged?groupName={preferenceGroupName}&{GetQueryStringScope(scope)}";
		_logger.LogDebug("GetMergedPreferenceKeyValuesAsync - url - Url={Url}", url);

		return InternalGetRequestAsync<IList<PreferenceKeyValue>>(url, userSecurityInfo, throwError: false, cancellationToken: cancellationToken);
	}

	/// <summary>
	/// helper to get request.
	/// </summary>
	/// <typeparam name="TIn">The type of the t in.</typeparam>
	/// <param name="requestUrl">The request URL.</param>
	/// <param name="model">The model.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="method">The method.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;string?&gt; representing the asynchronous operation.</returns>
	/// <exception cref="TokenGenerationException">$"error on request Url={requestUrl} Error={await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	private async Task InternalPostRequestAsync<TIn>(string requestUrl, TIn? model, UserSecurityInfo userSecurityInfo, HttpMethod? method = null, CancellationToken cancellationToken = default)
	{
		var request = new HttpRequestMessage(method ?? HttpMethod.Post, requestUrl);
		if (model is not null)
		{
			request.Content = JsonContent.Create(model);
		}

		var targetHeaders = request.Headers;
		userSecurityInfo?.UpdateWebHeaders(targetHeaders);

		var newAccessToken = await _tokenGenerationService.CheckTokenExpiryAndGenerateTokenAsync(cancellationToken: cancellationToken);
		if (newAccessToken is not null)
		{
			targetHeaders.Authorization = new AuthenticationHeaderValue("Bearer", newAccessToken);
		}

		var client = _connection.GetClient();
		var response = await client.SendAsync(request, cancellationToken);
		if (!response.IsSuccessStatusCode)
		{
			throw new OrgManagementServiceException($"error on request Url={requestUrl} Error={await response.Content.ReadAsStringAsync(cancellationToken)}");
		}
	}

	/// <summary>
	/// Gets the query string scope.
	/// </summary>
	/// <param name="scope">The scope.</param>
	/// <returns>System.String.</returns>
	static string GetQueryStringScope(PreferenceScope scope)
		=> !scope.OrgId.IsNullOrEmpty() ? $"orgId={scope.OrgId}" : $"iamUserId={scope.UserId}";

	record WrappedData<T>(T[] Data);
}