namespace Apex.Services;

using Apex.Options;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;

/// <summary>
/// Class PlatformTokenGenerationService.
/// </summary>
/// <param name="logger">The logger.</param>
/// <param name="options">The options.</param>
/// <param name="secureIamOptions">The secure iam options.</param>
/// <param name="memoryCache">The memory cache.</param>
/// <param name="connection">The connection.</param>
/// <remarks>Initializes a new instance of the <see cref="PlatformTokenGenerationService" /> class.</remarks>
public sealed partial class PlatformTokenGenerationService(ILogger<PlatformTokenGenerationService> logger,
	IOptions<IamOptions> options, IOptions<SecureIamOptions> secureIamOptions,
	IMemoryCache memoryCache, IWebConnectionService connection)
	: BaseTokenGenerationService(logger, options, secureIamOptions, memoryCache, connection, "openid email profile")
{
}