namespace Apex.Services;

using Apex.Options;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;

/// <summary>
/// Class ApplicationTokenGenerationService.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="ApplicationTokenGenerationService" /> class.
/// </remarks>
/// <param name="logger">The logger.</param>
/// <param name="options">The options.</param>
/// <param name="secureIamOptions">The secure iam options.</param>
/// <param name="connection">The connection.</param>
/// <param name="memoryCache">The memory cache.</param>
public sealed partial class ApplicationTokenGenerationService(ILogger<ApplicationTokenGenerationService> logger,
	IOptions<IamOptions> options, IOptions<SecureIamOptions> secureIamOptions,
	IMemoryCache memoryCache, IWebConnectionService connection) : BaseTokenGenerationService(logger, options, secureIamOptions, memoryCache, connection, "openid")
{
}