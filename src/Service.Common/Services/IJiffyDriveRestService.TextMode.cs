namespace Apex.Services;

using Apex.Models;

/// <summary>
/// Interface IJiffyDriveRestService
/// </summary>
public partial interface IJiffyDriveRestService
{
	/// <summary>
	/// Gets the file as a text content.
	/// </summary>
	/// <param name="filePath">The file path.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	Task<string?> GetFileAsTextAsync(string filePath, UserSecurityInfo userInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Bulk get files as text.
	/// </summary>
	/// <param name="filePaths">The file paths.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;System.Nullable&lt;System.String&gt;&gt;&gt;.</returns>
	Task<IEnumerable<string?>> BulkGetFilesAsTextAsync(string?[] filePaths, UserSecurityInfo userInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Bulk download files into temporary files.
	/// </summary>
	/// <param name="filePaths">The file paths.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;System.ValueTuple&lt;System.Nullable&lt;System.String&gt;, System.Nullable&lt;System.String&gt;&gt;&gt;&gt;.</returns>
	Task<IEnumerable<(string? FilePath, string? FileName)>> BulkDownloadFilesAsTextAsync(string?[] filePaths, UserSecurityInfo userInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Downloads the file into temporary file.
	/// </summary>
	/// <param name="filePath">The file path.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.ValueTuple&lt;System.Nullable&lt;System.String&gt;, System.Nullable&lt;System.String&gt;&gt;&gt;.</returns>
	Task<(string? FilePath, string? FileName)> DownloadFileAsTextAsync(string? filePath, UserSecurityInfo userInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Uploads the file content.
	/// </summary>
	/// <param name="targetJiffyDriveFilePath">The target jiffy drive file path.</param>
	/// <param name="fileContent">Content of the file.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	Task<string?> UploadContentAsTextAsync(string? targetJiffyDriveFilePath, string? fileContent, UserSecurityInfo userInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Uploads the file.
	/// </summary>
	/// <param name="targetJiffyDriveFolderPath">The target jiffy drive folder path.</param>
	/// <param name="filePath">The file path.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	Task<string?> UploadFileAsTextAsync(string? targetJiffyDriveFolderPath, string? filePath, UserSecurityInfo userInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Bulk upload files.
	/// </summary>
	/// <param name="jiffyDriveBaseFolderPath">The target jiffy drive folder path.</param>
	/// <param name="filePaths">The file paths.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;System.Nullable&lt;System.String&gt;&gt;&gt;.</returns>
	Task<IEnumerable<string?>> BulkUploadFilesAsTextAsync(string? jiffyDriveBaseFolderPath, string?[] filePaths, UserSecurityInfo userInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Bulk upload files.
	/// </summary>
	/// <param name="filePaths">The file paths.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;System.Nullable&lt;System.String&gt;&gt;&gt;.</returns>
	Task<IEnumerable<string?>> BulkUploadFilesAsTextAsync((string JiffyDrivePath, string LocalFilePath)[] filePaths, UserSecurityInfo userInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Bulk upload file content
	/// </summary>
	/// <param name="fileContents">The file contents.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;System.Nullable&lt;System.String&gt;&gt;&gt;.</returns>
	Task<IEnumerable<string?>> BulkUploadContentAsTextAsync((string JiffyDrivePath, string? Content)[] fileContents, UserSecurityInfo userInfo, CancellationToken cancellationToken = default);
}