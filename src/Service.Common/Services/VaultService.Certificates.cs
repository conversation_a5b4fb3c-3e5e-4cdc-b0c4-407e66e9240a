namespace Apex.Services;

using System.Text.Json.Serialization;

/// <summary>
/// Record CustomCertificateRevokeRequest. This class cannot be inherited.
/// </summary>
internal sealed record CustomCertificateRevokeRequest
{
	/// <summary>
	/// Gets or sets the serial number.
	/// </summary>
	/// <value>The serial number.</value>
	[JsonPropertyName("serial_number")]
	public required string SerialNumber { get; set; }
}

/// <summary>
/// Record CustomCertificateRevokeResponse. This class cannot be inherited.
/// </summary>
internal sealed record CustomCertificateRevokeResponse
{
	/// <summary>
	/// Gets or sets the serial number.
	/// </summary>
	/// <value>The serial number.</value>
	[JsonPropertyName("data")]
	public required CustomCertificateRevokeRequestData Data { get; set; }
}

internal sealed record CustomCertificateRevokeRequestData
{
	/// <summary>
	/// Gets or sets the revocation time.
	/// </summary>
	/// <value>The serial number.</value>
	[JsonPropertyName("revocation_time")]
	public required int RevocationTime { get; set; }
}

/// <summary>
/// Class VaultService.
/// </summary>
public sealed partial class VaultService
{
	/// <summary>
	/// Generate certificate.
	/// </summary>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;CustomCertificate&gt; representing the asynchronous operation.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	public Task<GetCertificatesResponse?> GetCertificatesAsync(CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.VaultUrl);
		var certificatesUrl = $"{_serviceOptions.Value.VaultUrl!.TrimEnd('/')}/v1/pki_int/certs?list=true";
		_logger.LogDebug("GetCertificatesAsync - list certificates url - Url={Url}", certificatesUrl);

		return InternalGetRequestAsync<GetCertificatesResponse>(certificatesUrl, cancellationToken: cancellationToken);
	}

	/// <summary>
	/// Get certificate.
	/// </summary>
	/// <param name="serialNumber">The serial number.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>
	/// A Task&lt;CustomCertificate&gt; representing the asynchronous operation.
	/// </returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	public Task<CustomCertificate?> GetCertificateAsync(string serialNumber, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.VaultUrl);
		var certificateUrl = $"{_serviceOptions.Value.VaultUrl!.TrimEnd('/')}/v1/pki_int/cert/{serialNumber}";
		_logger.LogDebug("GetCertificateAsync - get certificate url - Url={Url}", certificateUrl);

		return InternalGetRequestAsync<CustomCertificate>(certificateUrl, cancellationToken: cancellationToken);
	}

	/// <summary>
	/// Generate certificate.
	/// </summary>
	/// <param name="request">The request.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>
	/// A Task&lt;CustomCertificate&gt; representing the asynchronous operation.
	/// </returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	public Task<CustomCertificate?> GenerateCertificateAsync(CustomCertificateRequest request, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.VaultUrl);
		ArgumentNullException.ThrowIfNull(_secureVaultOptions.Value.VaultPkiIntRole);
		var certificateIssueUrl = $"{_serviceOptions.Value.VaultUrl!.TrimEnd('/')}/v1/pki_int/issue/{_secureVaultOptions.Value.VaultPkiIntRole}";
		_logger.LogDebug("GenerateCertificateAsync - certificate generation url - Url={Url}", certificateIssueUrl);

		return InternalPostRequestAsync<CustomCertificateRequest, CustomCertificate>(certificateIssueUrl, request, cancellationToken: cancellationToken);
	}

	/// <summary>
	/// Revoke certificate.
	/// </summary>
	/// <param name="serialNumber">The serial number.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;CustomCertificate&gt; representing the asynchronous operation.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	public async Task<bool> RevokeCertificateAsync(string serialNumber, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.VaultUrl);
		var certificateIssueUrl = $"{_serviceOptions.Value.VaultUrl!.TrimEnd('/')}/v1/pki/revoke";
		_logger.LogDebug("RevokeCertificateAsync - certificate revoke url - Url={Url}", certificateIssueUrl);

		var response = await InternalPostRequestAsync<CustomCertificateRevokeRequest, CustomCertificateRevokeResponse>(certificateIssueUrl,
			new CustomCertificateRevokeRequest
			{
				SerialNumber = serialNumber
			},
			cancellationToken: cancellationToken);
		return response?.Data?.RevocationTime > 0;
	}

	/// <summary>
	/// helper to get request.
	/// </summary>
	/// <typeparam name="TOut">The type of the t out.</typeparam>
	/// <param name="requestUrl">The request URL.</param>
	/// <param name="throwOnFailure">if set to <c>true</c> [throw on failure].</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>
	/// A Task&lt;string?&gt; representing the asynchronous operation.
	/// </returns>
	/// <exception cref="TokenGenerationException">$"error on request Url={requestUrl} Error={await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	private async Task<TOut?> InternalGetRequestAsync<TOut>(string requestUrl, bool throwOnFailure = true, CancellationToken cancellationToken = default)
		where TOut : class
	{
		_logger.LogDebug("GetRequestAsync - getting vault access token...");
		var vaultToken = await _tokenGenerationService.CheckTokenExpiryAndGenerateTokenAsync(cancellationToken: cancellationToken);
		_logger.LogDebug("GetRequestAsync - vault access token - VaultToken={VaultToken}", vaultToken.Limit());
		ArgumentNullException.ThrowIfNull(vaultToken);

		var request = new HttpRequestMessage(HttpMethod.Get, requestUrl);
		request.Headers.TryAddWithoutValidation("X-Vault-Token", vaultToken);

		var client = _connection.GetClient();
		var response = await client.SendAsync(request, cancellationToken);
		if (!response.IsSuccessStatusCode)
		{
			if (throwOnFailure)
			{
				throw new TokenGenerationException($"error on request Url={requestUrl} Error={await response.Content.ReadAsStringAsync(cancellationToken)}");
			}
			else
			{
				return null;
			}
		}

		return await response.Content.ReadFromJsonAsync<TOut>(options: SerializationExtensions._SERIALIZEROPTIONS, cancellationToken: cancellationToken);
	}

	/// <summary>
	/// helper to delete request.
	/// </summary>
	/// <param name="requestUrl">The request URL.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>
	/// A Task&lt;string?&gt; representing the asynchronous operation.
	/// </returns>
	/// <exception cref="TokenGenerationException">$"error on request Url={requestUrl} Error={await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	private async Task InternalDeleteRequestAsync(string requestUrl, CancellationToken cancellationToken = default)
	{
		_logger.LogDebug("DeleteRequestAsync - getting vault access token...");
		var vaultToken = await _tokenGenerationService.CheckTokenExpiryAndGenerateTokenAsync(cancellationToken: cancellationToken);
		_logger.LogDebug("DeleteRequestAsync - vault access token - VaultToken={VaultToken}", vaultToken.Limit());
		ArgumentNullException.ThrowIfNull(vaultToken);

		var request = new HttpRequestMessage(HttpMethod.Delete, requestUrl);
		request.Headers.TryAddWithoutValidation("X-Vault-Token", vaultToken);

		var client = _connection.GetClient();
		var response = await client.SendAsync(request, cancellationToken);
		if (!response.IsSuccessStatusCode)
		{
			throw new TokenGenerationException($"error on request Url={requestUrl} Error={await response.Content.ReadAsStringAsync(cancellationToken)}");
		}
	}

	/// <summary>
	/// helper for post request.
	/// </summary>
	/// <typeparam name="TIn">The type of the t in.</typeparam>
	/// <typeparam name="TOut">The type of the t out.</typeparam>
	/// <param name="requestUrl">The request URL.</param>
	/// <param name="payload">The payload.</param>
	/// <param name="method">The method.</param>
	/// <param name="contentType">Type of the content.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>
	/// A Task&lt;string?&gt; representing the asynchronous operation.
	/// </returns>
	/// <exception cref="TokenGenerationException">$"error on request Url={requestUrl} Error={await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	private async Task<TOut?> InternalPostRequestAsync<TIn, TOut>(string requestUrl, TIn? payload,
		HttpMethod? method = null,
		string? contentType = null,
		CancellationToken cancellationToken = default)
	{
		_logger.LogDebug("PostRequestAsync - getting vault access token...");
		var vaultToken = await _tokenGenerationService.CheckTokenExpiryAndGenerateTokenAsync(cancellationToken: cancellationToken);
		_logger.LogDebug("PostRequestAsync - vault access token - VaultToken={VaultToken}", vaultToken.Limit());
		ArgumentNullException.ThrowIfNull(vaultToken);

		var request = new HttpRequestMessage(method ?? HttpMethod.Post, requestUrl)
		{
			Content = JsonContent.Create(payload, options: SerializationExtensions._SERIALIZEROPTIONS)
		};
		request.Headers.TryAddWithoutValidation("X-Vault-Token", vaultToken);
		if (!contentType.IsNullOrEmpty())
		{
			request.Content.Headers.TryAddWithoutValidation("Content-Type", contentType);
		}

		var client = _connection.GetClient();
		var response = await client.SendAsync(request, cancellationToken);
		if (!response.IsSuccessStatusCode)
		{
			throw new TokenGenerationException($"error on request Url={requestUrl} Error={await response.Content.ReadAsStringAsync(cancellationToken)}");
		}

		return await response.Content.ReadFromJsonAsync<TOut>(options: SerializationExtensions._SERIALIZEROPTIONS, cancellationToken: cancellationToken);
	}
}