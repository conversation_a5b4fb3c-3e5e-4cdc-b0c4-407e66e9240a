namespace Apex.Services;

using Apex.Models;
using System;

/// <summary>
/// Class MessengerRestService. This class cannot be inherited.
/// Implements the <see cref="Apex.Services.IMessengerRestService" />
/// </summary>
/// <seealso cref="Apex.Services.IMessengerRestService" />
public sealed partial class MessengerRestService
{
	/// <summary>
	/// </summary>
	/// <param name="messageInfo">The message info.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	public Task<string?> PostNonDurableQueueMessageAsync(QueueMessage messageInfo, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(messageInfo);
		ArgumentException.ThrowIfNullOrEmpty(messageInfo.Topic);

		return InternalPostNonDurableQueueMessageAsync(messageInfo, userInfo, cancellationToken);
	}

	/// <summary>
	/// Post message helper.
	/// </summary>
	/// <param name="messageInfo">The message info.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	/// <exception cref="Apex.Services.MessengerException">unable to send a message to messenger - TargetUrl={url} Status={responseMessage.StatusCode} Error={responseString}</exception>
	public async Task<string?> InternalPostNonDurableQueueMessageAsync(QueueMessage messageInfo, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default)
	{
		var url = $"{_serviceOptions.Value.MessengerUrl?.TrimEnd('/')}/v1/nondurable/{messageInfo.Topic}?isCrossTenant={(messageInfo.IsCrossTenant ? "true" : "false")}";
		var requestMessage = await InternalGetRequestMessageAsync(url, userInfo, cancellationToken);
		requestMessage.Method = HttpMethod.Put;
		requestMessage.Content = JsonContent.Create(new
		{
			payload = messageInfo.Payload
		}, options: SerializationExtensions._SERIALIZEROPTIONS);

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		var responseString = await responseMessage.Content.ReadAsStringAsync(cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new MessengerException($"unable to send a message to messenger/queue - TargetUrl={url} Status={responseMessage.StatusCode} Error={responseString}");
		}

		return responseString;
	}
}