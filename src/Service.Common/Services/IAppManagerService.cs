using Google.Protobuf;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Apex.Services;


/// <summary>
/// Class TenantDefinition. This class cannot be inherited.
/// </summary>
public sealed class TenantDefinition
{
	/// <summary>
	/// Gets or sets the identifier.
	/// </summary>
	/// <value>The identifier.</value>
	[JsonPropertyName("id")]
	public string? Id { get; set; }

	/// <summary>
	/// Gets or sets the created at.
	/// </summary>
	/// <value>The created at.</value>
	[JsonPropertyName("createdAt")]
	public DateTime? CreatedAt { get; set; }

	/// <summary>
	/// Gets or sets the updated at.
	/// </summary>
	/// <value>The updated at.</value>
	[JsonPropertyName("updatedAt")]
	public DateTime? UpdatedAt { get; set; }

	/// <summary>
	/// Gets or sets the name.
	/// </summary>
	/// <value>The name.</value>
	[JsonPropertyName("name")]
	public string? Name { get; set; }

	/// <summary>
	/// Gets or sets the description.
	/// </summary>
	/// <value>The description.</value>
	[JsonPropertyName("description")]
	public string? Description { get; set; }

	/// <summary>
	/// Gets or sets the URL.
	/// </summary>
	/// <value>The URL.</value>
	[JsonPropertyName("url")]
	public string? Url { get; set; }
}

/// <summary>
/// Class ApplicationDefinition. This class cannot be inherited.
/// </summary>
public sealed class ApplicationDefinition
{
	/// <summary>
	/// Gets or sets the identifier.
	/// </summary>
	/// <value>The identifier.</value>
	[JsonPropertyName("id")]
	public string? Id { get; set; }

	/// <summary>
	/// Gets or sets the created at.
	/// </summary>
	/// <value>The created at.</value>
	[JsonPropertyName("createdAt")]
	public DateTime? CreatedAt { get; set; }

	/// <summary>
	/// Gets or sets the updated at.
	/// </summary>
	/// <value>The updated at.</value>
	[JsonPropertyName("updatedAt")]
	public DateTime? UpdatedAt { get; set; }

	/// <summary>
	/// Gets or sets the tenant identifier.
	/// </summary>
	/// <value>The tenant identifier.</value>
	[JsonPropertyName("tenantId")]
	public string? TenantId { get; set; }

	/// <summary>
	/// Gets or sets the org unit identifier.
	/// </summary>
	/// <value>The org unit identifier.</value>
	[JsonPropertyName("orgUnitId")]
	public string? OrgUnitId { get; set; }

	/// <summary>
	/// Gets or sets the name.
	/// </summary>
	/// <value>The name.</value>
	[JsonPropertyName("name")]
	public string? Name { get; set; }

	/// <summary>
	/// Gets or sets the description.
	/// </summary>
	/// <value>The description.</value>
	[JsonPropertyName("description")]
	public string? Description { get; set; }

	/// <summary>
	/// Gets or sets the icon.
	/// </summary>
	/// <value>The icon.</value>
	[JsonPropertyName("icon")]
	public string? Icon { get; set; }

	/// <summary>
	/// Gets or sets the URL prefix.
	/// </summary>
	/// <value>The URL prefix.</value>
	[JsonPropertyName("urlPrefix")]
	public string? UrlPrefix { get; set; }

	/// <summary>
	/// Gets or sets the display name.
	/// </summary>
	/// <value>The display name.</value>
	[JsonPropertyName("displayName")]
	public string? DisplayName { get; set; }

	/// <summary>
	/// Gets or sets the org unit.
	/// </summary>
	/// <value>The org unit.</value>
	[JsonPropertyName("orgUnit")]
	public string? OrgUnit { get; set; }

	/// <summary>
	/// Gets or sets the creator identifier.
	/// </summary>
	/// <value>The creator identifier.</value>
	[JsonPropertyName("creatorId")]
	public string? CreatorId { get; set; }

	/// <summary>
	/// Gets or sets the display name of the creator.
	/// </summary>
	/// <value>The display name of the creator.</value>
	[JsonPropertyName("creatorDisplayName")]
	public string? CreatorDisplayName { get; set; }

	/// <summary>
	/// Gets or sets the domain.
	/// </summary>
	/// <value>The domain.</value>
	[JsonPropertyName("domain")]
	public string? Domain { get; set; }

	/// <summary>
	/// Gets or sets the category.
	/// </summary>
	/// <value>The category.</value>
	[JsonPropertyName("category")]
	public string? Category { get; set; }

	/// <summary>
	/// Gets or sets the insts.
	/// </summary>
	/// <value>The insts.</value>
	[JsonPropertyName("insts")]
	public List<Inst> Insts { get; set; }
}

/// <summary>
/// Class Features. This class cannot be inherited.
/// </summary>
public sealed class Features
{
	/// <summary>
	/// Gets or sets a value indicating whether [job manager].
	/// </summary>
	/// <value><c>null</c> if [job manager] contains no value, <c>true</c> if [job manager]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("jobmanager")]
	public bool? JobManager { get; set; }

	/// <summary>
	/// Gets or sets a value indicating whether this <see cref="Features"/> is database.
	/// </summary>
	/// <value><c>null</c> if [database] contains no value, <c>true</c> if [database]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("database")]
	public bool? Database { get; set; }

	/// <summary>
	/// Gets or sets a value indicating whether [document service].
	/// </summary>
	/// <value><c>null</c> if [document service] contains no value, <c>true</c> if [document service]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("docservice")]
	public bool? DocService { get; set; }

	/// <summary>
	/// Gets or sets a value indicating whether [event router].
	/// </summary>
	/// <value><c>null</c> if [event router] contains no value, <c>true</c> if [event router]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("eventrouter")]
	public bool? EventRouter { get; set; }

	/// <summary>
	/// Gets or sets a value indicating whether this <see cref="Features"/> is iam.
	/// </summary>
	/// <value><c>null</c> if [iam] contains no value, <c>true</c> if [iam]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("iam")]
	public bool? IAM { get; set; }

	/// <summary>
	/// Gets or sets a value indicating whether [jiffy drive].
	/// </summary>
	/// <value><c>null</c> if [jiffy drive] contains no value, <c>true</c> if [jiffy drive]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("jiffydrive")]
	public bool? JiffyDrive { get; set; }

	/// <summary>
	/// Gets or sets a value indicating whether this <see cref="Features"/> is service.
	/// </summary>
	/// <value><c>null</c> if [service] contains no value, <c>true</c> if [service]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("service")]
	public bool? Service { get; set; }

	/// <summary>
	/// Gets or sets a value indicating whether [template processor].
	/// </summary>
	/// <value><c>null</c> if [template processor] contains no value, <c>true</c> if [template processor]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("templateprocessor")]
	public bool? TemplateProcessor { get; set; }

	/// <summary>
	/// Gets or sets a value indicating whether this <see cref="Features"/> is transformation.
	/// </summary>
	/// <value><c>null</c> if [transformation] contains no value, <c>true</c> if [transformation]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("transformation")]
	public bool? Transformation { get; set; }

	/// <summary>
	/// Gets or sets a value indicating whether this <see cref="Features"/> is UI.
	/// </summary>
	/// <value><c>null</c> if [UI] contains no value, <c>true</c> if [UI]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("ui")]
	public bool? UI { get; set; }

	/// <summary>
	/// Gets or sets a value indicating whether this <see cref="Features"/> is validation.
	/// </summary>
	/// <value><c>null</c> if [validation] contains no value, <c>true</c> if [validation]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("validation")]
	public bool? Validation { get; set; }

	/// <summary>
	/// Gets or sets a value indicating whether this <see cref="Features"/> is workflow.
	/// </summary>
	/// <value><c>null</c> if [workflow] contains no value, <c>true</c> if [workflow]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("workflow")]
	public bool? Workflow { get; set; }
}

/// <summary>
/// Class Inst. This class cannot be inherited.
/// </summary>
public sealed class Inst
{
	/// <summary>
	/// Gets or sets the identifier.
	/// </summary>
	/// <value>The identifier.</value>
	[JsonPropertyName("id")]
	public string? Id { get; set; }

	/// <summary>
	/// Gets or sets the display name.
	/// </summary>
	/// <value>The name.</value>
	[JsonPropertyName("displayName")]
	public string? DisplayName { get; set; }

	/// <summary>
	/// Gets or sets the name.
	/// </summary>
	/// <value>The name.</value>
	[JsonPropertyName("name")]
	public string? Name { get; set; }

	/// <summary>
	/// Gets or sets the description.
	/// </summary>
	/// <value>The description.</value>
	[JsonPropertyName("description")]
	public string? Description { get; set; }

	/// <summary>
	/// Gets or sets the base version.
	/// </summary>
	/// <value>The base version.</value>
	[JsonPropertyName("baseVersion")]
	public string? BaseVersion { get; set; }

	/// <summary>
	/// Gets or sets the version.
	/// </summary>
	/// <value>The version.</value>
	[JsonPropertyName("version")]
	public string? Version { get; set; }

	/// <summary>
	/// Gets or sets the stage identifier.
	/// </summary>
	/// <value>The stage identifier.</value>
	[JsonPropertyName("stageId")]
	public string? StageId { get; set; }

	/// <summary>
	/// Gets or sets the features.
	/// </summary>
	/// <value>The features.</value>
	[JsonPropertyName("features")]
	public Features Features { get; set; }

	/// <summary>
	/// Gets or sets the banner image path.
	/// </summary>
	/// <value>The banner image path.</value>
	[JsonPropertyName("bannerImagePath")]
	public string? BannerImagePath { get; set; }

	/// <summary>
	/// Gets or sets the icon path.
	/// </summary>
	/// <value>The icon path.</value>
	[JsonPropertyName("iconPath")]
	public string? IconPath { get; set; }

	/// <summary>
	/// Gets or sets the domain.
	/// </summary>
	/// <value>The domain.</value>
	[JsonPropertyName("domain")]
	public string? Domain { get; set; }

	/// <summary>
	/// Gets or sets the category.
	/// </summary>
	/// <value>The category.</value>
	[JsonPropertyName("category")]
	public string? Category { get; set; }

	/// <summary>
	/// Gets or sets the stage.
	/// </summary>
	/// <value>The stage.</value>
	[JsonPropertyName("stage")]
	public Stage Stage { get; set; }

	/// <summary>
	/// Gets or sets the state.
	/// </summary>
	/// <value>The state.</value>
	[JsonPropertyName("state")]
	public State State { get; set; }

	/// <summary>
	/// Gets or sets the partition.
	/// </summary>
	/// <value>The partition.</value>
	[JsonPropertyName("partition")]
	public Partition Partition { get; set; }
}

/// <summary>
/// Class Partition. This class cannot be inherited.
/// </summary>
public sealed class Partition
{
	/// <summary>
	/// Gets or sets the identifier.
	/// </summary>
	/// <value>The identifier.</value>
	[JsonPropertyName("id")]
	public string? Id { get; set; }

	/// <summary>
	/// Gets or sets the name.
	/// </summary>
	/// <value>The name.</value>
	[JsonPropertyName("name")]
	public string? Name { get; set; }

	/// <summary>
	/// Gets or sets the name space.
	/// </summary>
	/// <value>The name space.</value>
	[JsonPropertyName("nameSpace")]
	public string? NameSpace { get; set; }

	/// <summary>
	/// Gets or sets the application FQDN suffix.
	/// </summary>
	/// <value>The application FQDN suffix.</value>
	[JsonPropertyName("appFqdnSuffix")]
	public string? AppFqdnSuffix { get; set; }
}

/// <summary>
/// Class Stage. This class cannot be inherited.
/// </summary>
public sealed class Stage
{
	/// <summary>
	/// Gets or sets the identifier.
	/// </summary>
	/// <value>The identifier.</value>
	[JsonPropertyName("id")]
	public string? Id { get; set; }

	/// <summary>
	/// Gets or sets the name.
	/// </summary>
	/// <value>The name.</value>
	[JsonPropertyName("name")]
	public string? Name { get; set; }
}

/// <summary>
/// Class State. This class cannot be inherited.
/// </summary>
public sealed class State
{
	/// <summary>
	/// Gets or sets the identifier.
	/// </summary>
	/// <value>The identifier.</value>
	[JsonPropertyName("id")]
	public string? Id { get; set; }

	/// <summary>
	/// Gets or sets the state value.
	/// </summary>
	/// <value>The state value.</value>
	[JsonPropertyName("state")]
	public string? StateValue { get; set; }
}


/// <summary>
/// Class ApplicationInstanceInfo. This class cannot be inherited.
/// </summary>
public sealed class ApplicationInstanceInfo
{
	/// <summary>
	/// Gets or sets the identifier.
	/// </summary>
	/// <value>The identifier.</value>
	[JsonPropertyName("id")]
	public string? Id { get; set; }

	/// <summary>
	/// Gets or sets the name.
	/// </summary>
	/// <value>The name.</value>
	[JsonPropertyName("name")]
	public string? Name { get; set; }

	/// <summary>
	/// Gets or sets the description.
	/// </summary>
	/// <value>The description.</value>
	[JsonPropertyName("description")]
	public string? Description { get; set; }

	/// <summary>
	/// Gets or sets the base version.
	/// </summary>
	/// <value>The base version.</value>
	[JsonPropertyName("baseVersion")]
	public string? BaseVersion { get; set; }

	/// <summary>
	/// Gets or sets the version.
	/// </summary>
	/// <value>The version.</value>
	[JsonPropertyName("version")]
	public string? Version { get; set; }

	/// <summary>
	/// Gets or sets the URL.
	/// </summary>
	/// <value>The URL.</value>
	[JsonPropertyName("url")]
	public string? Url { get; set; }

	/// <summary>
	/// Gets or sets the environment.
	/// </summary>
	/// <value>The environment.</value>
	[JsonPropertyName("environment")]
	public string? Environment { get; set; }

	/// <summary>
	/// Gets or sets the stage identifier.
	/// </summary>
	/// <value>The stage identifier.</value>
	[JsonPropertyName("stageId")]
	public string? StageId { get; set; }

	/// <summary>
	/// Gets or sets the features.
	/// </summary>
	/// <value>The features.</value>
	[JsonPropertyName("features")]
	public JsonDocument? Features { get; set; }

	/// <summary>
	/// Gets or sets the banner image path.
	/// </summary>
	/// <value>The banner image path.</value>
	[JsonPropertyName("bannerImagePath")]
	public string? BannerImagePath { get; set; }

	/// <summary>
	/// Gets or sets the icon path.
	/// </summary>
	/// <value>The icon path.</value>
	[JsonPropertyName("iconPath")]
	public string? IconPath { get; set; }

	/// <summary>
	/// Gets or sets the domain.
	/// </summary>
	/// <value>The domain.</value>
	[JsonPropertyName("domain")]
	public string? Domain { get; set; }

	/// <summary>
	/// Gets or sets the category.
	/// </summary>
	/// <value>The category.</value>
	[JsonPropertyName("category")]
	public string? Category { get; set; }

	/// <summary>
	/// Gets or sets a value indicating whether [domain model].
	/// </summary>
	/// <value><c>true</c> if [domain model]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("domainModel")]
	public bool DomainModel { get; set; }
}

/// <summary>
/// Interface IAppManagerService
/// </summary>
public partial interface IAppManagerService
{
	/// <summary>
	/// Gets the tenants.
	/// </summary>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;TenantDefinition&gt;&gt;.</returns>
	Task<IEnumerable<TenantDefinition>> GetTenantsAsync(CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets all the apps.
	/// </summary>
	/// <param name="tenantName">Name of the tenant.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;ApplicationDefinition&gt;&gt;.</returns>
	Task<ApplicationDefinition[]> GetAllAppsAsync(string tenantName, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the apps.
	/// </summary>
	/// <param name="tenantName">Name of the tenant.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;ApplicationDefinition&gt;&gt;.</returns>
	Task<IEnumerable<ApplicationDefinition>> GetAppsAsync(string tenantName, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the application instance information asynchronous.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;T&gt;.</returns>
	Task<ApplicationInstanceInfo?> GetAppInstanceInfoAsync(string tenantId, string appId, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the application components configuration.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="componentUrlFragment">The component URL fragment.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;T&gt;.</returns>
	Task<T?> GetServiceInfoAsync<T>(string tenantId, string appId, string componentUrlFragment,
		CancellationToken cancellationToken = default)
		where T : IMessage<T>, new();

	/// <summary>
	/// Gets the application components configuration from CLS.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="appName">Name of the application.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;T&gt;&gt;.</returns>
	Task<T?> GetApplicationComponentsConfigurationFromClsAsync<T>(string tenantId, string appId, string appName,
		CancellationToken cancellationToken = default)
		where T : class;

	/// <summary>
	/// Gets the application components configuration from CLS.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="appName">Name of the application.</param>
	/// <param name="appVersion">The application version.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;T&gt;&gt;.</returns>
	Task<T?> GetApplicationComponentsConfigurationFromClsExAsync<T>(string tenantId, string appId, string appName, string appVersion,
		CancellationToken cancellationToken = default)
		where T : class;

	/// <summary>
	/// Gets the application components configuration via coordinate from CLS.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="appName">Name of the application.</param>
	/// <param name="appVersion">The application version.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;T&gt;&gt;.</returns>
	Task<T?> GetApplicationComponentsConfigurationViaCoordinateFromClsAsync<T>(string tenantId, string appId, string appName, string appVersion, CancellationToken cancellationToken = default)
		where T : class;

	/// <summary>
	/// Gets the component via coordinate from CLS.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="componentName">Name of the component.</param>
	/// <param name="componentType">Type of the component.</param>
	/// <param name="componentInclude">The component include.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;T&gt;&gt;.</returns>
	Task<T?> GetLatestComponentViaCoordinateFromClsAsync<T>(string tenantId, string appId, string componentName, string componentType, string componentInclude,
			CancellationToken cancellationToken = default)
			where T : class;

	/// <summary>
	/// Gets the component via coordinate from CLS.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="componentName">Name of the component.</param>
	/// <param name="componentType">Type of the component.</param>
	/// <param name="componentVersion">The component version.</param>
	/// <param name="componentInclude">The component include - SHADOW,RESERVED.</param>
	/// <param name="componentNonce">The component nonce.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;T&gt;&gt;.</returns>
	Task<T?> GetComponentViaCoordinateFromClsAsync<T>(string tenantId, string appId, string componentName, string componentType,
			string componentVersion, string componentInclude, string componentNonce,
			CancellationToken cancellationToken = default)
			where T : class;

	/// <summary>
	/// Get component from model repo.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="componentType">Type of the component.</param>
	/// <param name="componentId">The component identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;T&gt; representing the asynchronous operation.</returns>
	Task<T?> GetComponentFromModelRepoAsync<T>(string tenantId, string appId, string componentType, string componentId,
		CancellationToken cancellationToken = default)
		where T : IMessage<T>, new();

	/// <summary>
	/// Get component configuration from configuration management.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="componentId">The component identifier.</param>
	/// <param name="environment">The environment.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;T&gt; representing the asynchronous operation.</returns>
	Task<T?> GetComponentConfigurationFromConfigManagementAsync<T>(string tenantId, string appId, string componentId, string environment,
		CancellationToken cancellationToken = default)
		where T : class;

	/// <summary>
	/// Gets the component secret path from configuration management.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="componentId">The component identifier.</param>
	/// <param name="environment">The environment.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;T&gt;&gt;.</returns>
	Task<T?> GetComponentSecretsFromConfigManagementAsync<T>(string tenantId, string appId, string componentId, string environment,
		CancellationToken cancellationToken = default)
		where T : class;

	/// <summary>
	/// Gets the component secret from CLS.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="componentId">The component identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;T&gt;&gt;.</returns>
	Task<T?> GetComponentSecretFromClsAsync<T>(string tenantId, string appId, string componentId,
		CancellationToken cancellationToken = default)
		where T : class;

	/// <summary>
	/// Gets the secret value.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="path">The path.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;T&gt;&gt;.</returns>
	Task<string> GetSecretValueAsync(string tenantId, string appId, string path, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the secret value.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="path">The path.</param>
	/// <param name="key">The key.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;T&gt;&gt;.</returns>
	Task<string> GetSecretValueAsync(string tenantId, string appId, string path, string? key, CancellationToken cancellationToken = default);
}