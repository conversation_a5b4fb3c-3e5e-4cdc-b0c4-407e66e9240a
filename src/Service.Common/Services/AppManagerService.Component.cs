using Apex.Models;
using Google.Protobuf;
using System.Text.Json;
using System.Web;

namespace Apex.Services;

/// <summary>
/// Class AppManagerService. This class cannot be inherited.
/// </summary>
public sealed partial class AppManagerService
{
	/// <summary>
	/// Get component from model repo.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="componentType">Type of the component.</param>
	/// <param name="componentId">The component identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IEnumerable`1&gt; representing the asynchronous operation.</returns>
	/// <exception cref="AppManagerException">$"error while getting component configuration - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	/// <exception cref="TokenGenerationException">$"error while getting component configuration - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public Task<T?> GetComponentFromModelRepoAsync<T>(string tenantId, string appId, string componentType, string componentId, CancellationToken cancellationToken = default)
		where T : IMessage<T>, new()
	{
		var requestUrl = $"{_serviceOptions.Value.ModelRepoUrl?.TrimEnd('/')}/rest/v1/tenant/{tenantId}/app/{appId}/component/{componentType}/{componentId}";
		_logger.LogInformation("GetComponentFromModelRepoAsync - Url={Url}", requestUrl);

		return GetWrappedAsGrpcJsonAsync<T>(requestUrl, tenantId, appId, componentType, cancellationToken);
	}

	/// <summary>
	/// Get as GRPC json.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="url">The URL.</param>
	/// <param name="tenantId">The tenant Id.</param>
	/// <param name="appId">The app Id.</param>
	/// <param name="componentType">Type of the component.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;EventRoutingResponse&gt;&gt;.</returns>
	/// <exception cref="AppManagerException">unable to get a response from the url - TargetUrl={url} Status={responseMessage.StatusCode} Error={responseString}</exception>
	public async Task<T?> GetWrappedAsGrpcJsonAsync<T>(string url, string tenantId, string appId, string componentType, CancellationToken cancellationToken = default)
		where T : IMessage<T>, new()
	{
		var responseMessage = await InternalGetUrlResponseAsync(url, tenantId, appId, null, cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			var responseString = await responseMessage.Content.ReadAsStringAsync(cancellationToken);
			throw new AppManagerException($"unable to get a response from the url - TargetUrl={url} Status={responseMessage.StatusCode} Error={responseString}");
		}
		var responseObject = await responseMessage.Content.ReadFromJsonAsync<JsonDocument>(cancellationToken);
		if (!responseObject.RootElement.TryGetProperty(componentType, out var wrappedProperty))
		{
			throw new AppManagerException($"unable to find wrapped object - TargetUrl={url} ComponentType={componentType}");
		}

		return ParseJson<T>(wrappedProperty.ToJSON());
	}

	/// <summary>
	/// Get application components configuration.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="appName">Name of the application.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IEnumerable`1&gt; representing the asynchronous operation.</returns>
	/// <exception cref="AppManagerException">$"error while getting component configuration - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	/// <exception cref="TokenGenerationException">$"error while getting component configuration - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public Task<T?> GetApplicationComponentsConfigurationFromClsAsync<T>(string tenantId, string appId, string appName, CancellationToken cancellationToken = default)
		where T : class
	{
		var requestUrl = $"{_serviceOptions.Value.ClsUrl?.TrimEnd('/')}/api/v1/components/_byName?type=application&name={appName}&includeReserved=true&includeNotReady=true";
		_logger.LogInformation("GetApplicationComponentsConfigurationFromClsAsync - Url={Url}", requestUrl);

		return InternalGetUrlResponseValueAsync<T>(requestUrl, tenantId, appId, "error while getting components from application - ", cancellationToken);
	}

	/// <summary>
	/// Get application components configuration.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="appName">Name of the application.</param>
	/// <param name="appVersion">The application version.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IEnumerable`1&gt; representing the asynchronous operation.</returns>
	/// <exception cref="AppManagerException">$"error while getting component configuration - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	/// <exception cref="TokenGenerationException">$"error while getting component configuration - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public Task<T?> GetApplicationComponentsConfigurationFromClsExAsync<T>(string tenantId, string appId, string appName, string appVersion, CancellationToken cancellationToken = default)
		where T : class
	{
		var requestUrl = $"{_serviceOptions.Value.ClsUrl?.TrimEnd('/')}/api/v1/components/_byName?type=application&name={appName}&includeReserved=true&includeNotReady=true&minVersion={appVersion}";
		_logger.LogInformation("GetApplicationComponentsConfigurationFromClsExAsync - Url={Url}", requestUrl);

		return InternalGetUrlResponseValueAsync<T>(requestUrl, tenantId, appId, "error while getting components from application - ", cancellationToken);
	}

	/// <summary>
	/// Gets the application via coordinate.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="appName">Name of the application.</param>
	/// <param name="appVersion">The application version.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>T?.</returns>
	/// <exception cref="AppManagerException">$"error while getting component via co-ordinate - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public Task<T?> GetApplicationComponentsConfigurationViaCoordinateFromClsAsync<T>(string tenantId, string appId, string appName, string appVersion, CancellationToken cancellationToken = default)
		where T : class
	{
		var requestUrl = $"{_serviceOptions.Value.ClsUrl?.TrimEnd('/')}/api/v1/components/_byCoordinates?name={appName}&type=application&version={appVersion}&includeReserved=true&includeNotReady=true";
		_logger.LogInformation("GetApplicationComponentsConfigurationViaCoordinateFromClsAsync - Url={Url}", requestUrl);

		return InternalGetUrlResponseValueAsync<T>(requestUrl, tenantId, appId, "error while getting application via co-ordinate - ", cancellationToken);
	}

	/// <summary>
	/// Gets the latest component via coordinate from CLS asynchronous.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="componentName">Name of the component.</param>
	/// <param name="componentType">Type of the component.</param>
	/// <param name="componentInclude">The component include.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;T&gt;&gt;.</returns>
	public async Task<T?> GetLatestComponentViaCoordinateFromClsAsync<T>(string tenantId, string appId, string componentName, string componentType, string componentInclude,
		CancellationToken cancellationToken = default)
	where T : class
	{
		var requestUrl = $"{_serviceOptions.Value.ClsUrl?.TrimEnd('/')}/api/v1/components?name={componentName}&type={componentType}&fetchLatest=true&include={componentInclude}";
		_logger.LogInformation("GetLatestComponentViaCoordinateFromClsAsync - Url={Url}", requestUrl);

		var components = await InternalGetUrlResponseValueAsync<T[]>(requestUrl, tenantId, appId, "error while getting component via co-ordinate - ", cancellationToken);
		return components?.FirstOrDefault();
	}

	/// <summary>
	/// Gets the component via coordinate.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="componentName">Name of the component.</param>
	/// <param name="componentType">Type of the component.</param>
	/// <param name="componentVersion">The component version.</param>
	/// <param name="componentInclude">The component include.</param>
	/// <param name="componentNonce">The component nonce.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>T?.</returns>
	/// <exception cref="AppManagerException">$"error while getting component via co-ordinate - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public Task<T?> GetComponentViaCoordinateFromClsAsync<T>(string tenantId, string appId, string componentName, string componentType, string componentVersion, string componentInclude, string componentNonce,
		CancellationToken cancellationToken = default)
		where T : class
	{
		var requestUrl = $"{_serviceOptions.Value.ClsUrl?.TrimEnd('/')}/api/v1/components/_byCoordinates?name={componentName}&type={componentType}&version={componentVersion}&include={componentInclude}&nonce={componentNonce}";
		_logger.LogInformation("GetComponentViaCoordinateFromClsAsync - Url={Url}", requestUrl);

		return InternalGetUrlResponseValueAsync<T>(requestUrl, tenantId, appId, "error while getting component via co-ordinate - ", cancellationToken);
	}

	/// <summary>
	/// Gets the component configuration from configuration management.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="componentId">The component identifier.</param>
	/// <param name="environment">The environment.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>T?.</returns>
	/// <exception cref="AppManagerException">$"error while getting component via co-ordinate - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public async Task<T?> GetComponentConfigurationFromConfigManagementAsync<T>(string tenantId, string appId, string componentId, string environment, CancellationToken cancellationToken = default)
		where T : class
	{
		T? result = default;
		try
		{
			var requestUrl = $"{_serviceOptions.Value.ConfigManagerUrl?.TrimEnd('/')}/config/v1/tenant/{tenantId}/component/{componentId}/type/deployment?env={environment}";
			_logger.LogInformation("GetComponentConfigurationFromConfigManagementAsync - without app id - Url={Url}", requestUrl);
			result = await InternalGetUrlResponseValueAsync<T>(requestUrl, tenantId, appId, "error while getting component configuration from configuration manager - ", cancellationToken);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "unable to get the component configuration from configuration manager");
		}

		if (result == null)
		{
			var requestUrl = $"{_serviceOptions.Value.ConfigManagerUrl?.TrimEnd('/')}/config/v1/tenant/{tenantId}/component/{componentId}/type/deployment?app={appId}&env={environment}";
			_logger.LogInformation("GetComponentConfigurationFromConfigManagementAsync - with app id - Url={Url}", requestUrl);
			result = await InternalGetUrlResponseValueAsync<T>(requestUrl, tenantId, appId, "error while getting component configuration from configuration manager - ", cancellationToken);
		}

		return result;
	}

	/// <summary>
	/// Get component secret from CLS.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="componentId">The component identifier.</param>
	/// <param name="environment">The environment.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;T?&gt; representing the asynchronous operation.</returns>
	/// <exception cref="AppManagerException">$"error while getting component via co-ordinate - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public async Task<T?> GetComponentSecretsFromConfigManagementAsync<T>(string tenantId, string appId, string componentId, string environment, CancellationToken cancellationToken = default)
		where T : class
	{
		T? result = default;
		try
		{
			var requestUrl = $"{_serviceOptions.Value.ConfigManagerUrl?.TrimEnd('/')}/config/v1/tenant/{tenantId}/component/{componentId}/type/deployment?format=property&validate=1&env={environment}";
			_logger.LogInformation("GetComponentSecretsFromConfigManagementAsync - without app id - Url={Url}", requestUrl);
			result = await InternalGetUrlResponseValueAsync<T>(requestUrl, tenantId, appId, "error while getting component secrets path from configuration manager", cancellationToken);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "error while getting component secrets path from configuration manager");
		}

		if (result == null)
		{
			var requestUrl = $"{_serviceOptions.Value.ConfigManagerUrl?.TrimEnd('/')}/config/v1/tenant/{tenantId}/component/{componentId}/type/deployment?app={appId}&format=property&validate=1&env={environment}";
			_logger.LogInformation("GetComponentSecretsFromConfigManagementAsync - with app id - Url={Url}", requestUrl);
			result = await InternalGetUrlResponseValueAsync<T>(requestUrl, tenantId, appId, "error while getting component secrets path from configuration manager", cancellationToken);
		}

		return result;
	}

	/// <summary>
	/// Get component secret from CLS.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="componentId">The component identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;T?&gt; representing the asynchronous operation.</returns>
	/// <exception cref="AppManagerException">$"error while getting component via co-ordinate - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public Task<T?> GetComponentSecretFromClsAsync<T>(string tenantId, string appId, string componentId, CancellationToken cancellationToken = default)
		where T : class
	{
		var requestUrl = $"{_serviceOptions.Value.ClsUrl?.TrimEnd('/')}/api/v1/default/components/{componentId}/metadata/configuration.jiffy-json.v1?format=property";
		_logger.LogInformation("GetComponentSecretFromClsAsync - Url={Url}", requestUrl);

		return InternalGetUrlResponseValueAsync<T>(requestUrl, tenantId, appId, "error while getting component secret", cancellationToken);
	}

	/// <summary>
	/// Get component secret from CLS.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="path">The path.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;T&gt;&gt;.</returns>
	/// <exception cref="AppManagerException">$"error while getting component via co-ordinate - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public Task<string> GetSecretValueAsync(string tenantId, string appId, string path, CancellationToken cancellationToken = default)
		=> GetSecretValueAsync(tenantId, appId, path, null, cancellationToken);

	/// <summary>
	/// Get component secret from CLS.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="path">The path.</param>
	/// <param name="key">The key.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;T&gt;&gt;.</returns>
	/// <exception cref="AppManagerException">$"error while getting component via co-ordinate - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public Task<string> GetSecretValueAsync(string tenantId, string appId, string path, string? key, CancellationToken cancellationToken = default)
	{
		var requestUrl = $"{_serviceOptions.Value.ConfigManagerUrl?.TrimEnd('/')}/config/v1/secrets?path={(!key.IsNullOrEmpty() ? $"{key}@" : null)}{HttpUtility.UrlEncode(path)}";
		_logger.LogInformation("GetSecretValueAsync - Url={Url}", requestUrl);

		return InternalGetUrlResponseStringAsync(requestUrl, tenantId, appId, "error while getting component secret value", cancellationToken);
	}

	/// <summary>
	/// helper to get URL content.
	/// </summary>
	/// <param name="requestUrl">The request URL.</param>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="errorMessage">The error message.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;T?&gt; representing the asynchronous operation.</returns>
	/// <exception cref="AppManagerException">$"{errorMessage} {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public async Task<string> InternalGetUrlResponseStringAsync(string requestUrl, string tenantId, string appId, string errorMessage, CancellationToken cancellationToken = default)
	{
		var response = await InternalGetUrlResponseAsync(requestUrl, tenantId, appId, errorMessage, cancellationToken);
		return await response.Content.ReadAsStringAsync(cancellationToken);
	}

	/// <summary>
	/// helper to get URL content.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="requestUrl">The request URL.</param>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="errorMessage">The error message.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;T?&gt; representing the asynchronous operation.</returns>
	/// <exception cref="AppManagerException">$"{errorMessage} {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public async Task<T?> InternalGetUrlResponseValueAsync<T>(string requestUrl, string tenantId, string appId, string errorMessage, CancellationToken cancellationToken = default)
		where T : class
	{
		var response = await InternalGetUrlResponseAsync(requestUrl, tenantId, appId, errorMessage, cancellationToken);
		try
		{
			// specialize only for strings
			if (typeof(T) != typeof(string))
			{
				return await response.Content.ReadFromJsonAsync<T>(SerializationExtensions._SERIALIZEROPTIONS, cancellationToken: cancellationToken);
			}
			else
			{
				return (T?)(object)await response.Content.ReadAsStringAsync(cancellationToken: cancellationToken);
			}
		}
		catch (Exception ex)
		{
			var json = await response.Content.ReadAsStringAsync(cancellationToken);
			_logger.LogError(ex, "Unable to deserialize the JSON content into type - JSON={JSON} Type={Type}", json, typeof(T));
			throw;
		}
	}

	/// <summary>
	/// helper to get URL content.
	/// </summary>
	/// <param name="requestUrl">The request URL.</param>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="errorMessage">The error message.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;T?&gt; representing the asynchronous operation.</returns>
	/// <exception cref="AppManagerException">$"{errorMessage} {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public async Task<HttpResponseMessage> InternalGetUrlResponseAsync(string requestUrl, string tenantId, string appId, string? errorMessage, CancellationToken cancellationToken = default)
	{
		var securityInfo = new UserSecurityInfo
		(
			tenantId,
			appId,
			await _tokenGenerationService.ResolveUserIdFromTenantAppIdAsync(tenantId, appId, clientName: "10SecondsTimeout", cancellationToken: cancellationToken),
			null
		);
		var request = await _tokenGenerationService.GetRequestMessageAsync(requestUrl, securityInfo, cancellationToken);
		var client = _connection.GetClient();
		var response = await client.SendAsync(request, cancellationToken);
		if (!response.IsSuccessStatusCode)
		{
			throw new AppManagerException($"{errorMessage} {await response.Content.ReadAsStringAsync(cancellationToken)}");
		}

		return response;
	}
}