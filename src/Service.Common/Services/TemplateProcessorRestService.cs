namespace Apex.Services;

using Apex.Models;
using Apex.Options;
using Microsoft.Extensions.Options;
using System.Net.Http.Headers;
using System.Text.Json;

/// <summary>
/// Class TemplateProcessorRestService. This class cannot be inherited.
/// </summary>
public sealed partial class TemplateProcessorRestService : ITemplateProcessorRestService
{
	/// <summary>
	/// The service options
	/// </summary>
	private readonly IOptions<ServiceOptions> _serviceOptions;

	/// <summary>
	/// The connection
	/// </summary>
	private readonly IWebConnectionService _connection;

	/// <summary>
	/// The token generation
	/// </summary>
	private readonly ITokenGenerationService _tokenGeneration;

	/// <summary>
	/// Initializes a new instance of the <see cref="TemplateProcessorRestService" /> class.
	/// </summary>
	/// <param name="serviceOptions">The service options.</param>
	/// <param name="connection">The connection.</param>
	/// <param name="tokenGeneration">The token generation.</param>
	public TemplateProcessorRestService(IOptions<ServiceOptions> serviceOptions, IWebConnectionService connection, ITokenGenerationService tokenGeneration)
	{
		_serviceOptions = serviceOptions;
		_connection = connection;
		_tokenGeneration = tokenGeneration;
	}

	/// <summary>
	/// Transforms the string.
	/// </summary>
	/// <param name="transformString">The transform string.</param>
	/// <param name="model">The modDsel.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	/// <exception cref="System.ArgumentException">a relative path was used for the content template path, but jiffy drive path options is not configured</exception>
	public Task<string?> TransformModelAsync(string? transformString, JsonDocument? model, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(transformString);
		return InternalTransformModelAsync(transformString, model, userInfo, cancellationToken);
	}

	/// <summary>
	/// Bulk transforms the string.
	/// </summary>
	/// <param name="transformStrings">The transform string.</param>
	/// <param name="model">The modDsel.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	/// <exception cref="System.ArgumentException"></exception>
	public Task<IEnumerable<string?>> BulkTransformModelAsync(string?[] transformStrings, JsonDocument? model, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(transformStrings);
		return InternalBulkTransformModelAsync(transformStrings, model, userInfo, cancellationToken);
	}

	/// <summary>
	/// bulk transform helper.
	/// </summary>
	/// <param name="transformStrings">The transform strings.</param>
	/// <param name="model">The model.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IEnumerable`1&gt; representing the asynchronous operation.</returns>
	public async Task<IEnumerable<string?>> InternalBulkTransformModelAsync(string?[] transformStrings, JsonDocument? model, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default)
	{
		string? NULLSTRING = null;
		var transformTasks = transformStrings.Select(t => !t.IsNullOrEmpty()
			? InternalTransformModelAsync(t, model, userInfo, cancellationToken)
			: Task.FromResult(NULLSTRING));
		return await Task.WhenAll(transformTasks);
	}

	/// <summary>
	/// helper for transform string.
	/// </summary>
	/// <param name="transformString">The transform string.</param>
	/// <param name="model">The model.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	private async Task<string?> InternalTransformModelAsync(string? transformString, JsonDocument? model, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default)
	{
		var transformUrl = new Uri($"{_serviceOptions.Value.TemplateProcessorUrl?.TrimEnd('/')}/v1/processtemplate");
		using var requestMessage = new HttpRequestMessage
		{
			RequestUri = transformUrl,
			Method = HttpMethod.Post,
			Content = JsonContent.Create(new
			{
				Template = transformString,
				Data = model
			})
		};

		var targetHeaders = requestMessage.Headers;
		userInfo?.UpdateWebHeaders(targetHeaders);

		var newAccessToken = await _tokenGeneration.CheckTokenExpiryAndGenerateTokenAsync(cancellationToken: cancellationToken);
		if (newAccessToken is not null)
		{
			targetHeaders.Authorization = new AuthenticationHeaderValue("Bearer", newAccessToken);
		}

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		var responseString = await responseMessage.Content.ReadAsStringAsync(cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new TemplateProcessorException($"unable to process the template string - TransformUrl={transformUrl} Status={responseMessage.StatusCode} DriveError={responseString}");
		}

		return responseString;
	}
}