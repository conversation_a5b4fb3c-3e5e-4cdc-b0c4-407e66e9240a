using System.Text.Json;
using System.Text.Json.Serialization;

namespace Apex.Services;

/// <summary>
/// Class VaultAuthenticationToken. This class cannot be inherited.
/// </summary>
public sealed record VaultAuthenticationToken
{
	/// <summary>
	/// Gets or sets a value indicating whether this <see cref="VaultAuthenticationToken"/> is renewable.
	/// </summary>
	/// <value><c>null</c> if [renewable] contains no value, <c>true</c> if [renewable]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("renewable")]
	public required bool Renewable { get; set; }

	/// <summary>
	/// Gets or sets the duration of the lease.
	/// </summary>
	/// <value>The duration of the lease.</value>
	[JsonPropertyName("lease_duration")]
	public required int LeaseDuration { get; set; }

	/// <summary>
	/// Gets or sets the expires at.
	/// </summary>
	/// <value>The expires at.</value>
	[JsonPropertyName("expires_at")]
	public DateTime ExpiresAt { get; set; }

	/// <summary>
	/// Gets or sets the metadata.
	/// </summary>
	/// <value>The metadata.</value>
	[JsonPropertyName("metadata")]
	public JsonDocument? Metadata { get; set; }

	/// <summary>
	/// Gets or sets the policies.
	/// </summary>
	/// <value>The policies.</value>
	[JsonPropertyName("policies")]
	public List<string>? Policies { get; set; }

	/// <summary>
	/// Gets or sets the accessor.
	/// </summary>
	/// <value>The accessor.</value>
	[JsonPropertyName("accessor")]
	public string? Accessor { get; set; }

	/// <summary>
	/// Gets or sets the client token.
	/// </summary>
	/// <value>The client token.</value>
	[JsonPropertyName("client_token")]
	public required string ClientToken { get; set; }
}

/// <summary>
/// Class VaultAuthenticationTokenResponse.
/// </summary>
internal record VaultAuthenticationTokenResponse
{
	/// <summary>
	/// Gets or sets the token.
	/// </summary>
	/// <value>The token.</value>
	[JsonPropertyName("auth")]
	public required VaultAuthenticationToken Token { get; init; }
}

/// <summary>
/// Class IVaultTokenGenerationService.
/// </summary>
public partial interface IVaultTokenGenerationService
{
	/// <summary>
	/// Gets the access token.
	/// </summary>
	/// <value>The access token.</value>
	public string? AccessToken { get; }

	/// <summary>
	/// Checks the token expiry and generate a token.
	/// </summary>
	/// <param name="graceTimeForTokenExpireInSeconds">The grace time for token expire in seconds.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.String.</returns>
	Task<string?> CheckTokenExpiryAndGenerateTokenAsync(long? graceTimeForTokenExpireInSeconds = null, CancellationToken cancellationToken = default);
}