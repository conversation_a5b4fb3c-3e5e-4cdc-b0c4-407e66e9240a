namespace Apex.Services;

using Apex.Options;
using Microsoft.Extensions.Options;

/// <summary>
/// Class OrgManagementService.
/// </summary>
/// <param name="_logger">The logger.</param>
/// <param name="_serviceOptions">The service options.</param>
/// <param name="_tokenGenerationService">The token generation service.</param>
/// <param name="_connection">The connection.</param>
/// <remarks>Initializes a new instance of the <see cref="OrgManagementService" /> class.</remarks>
public sealed partial class OrgManagementService(ILogger<OrgManagementService> _logger,
	IOptions<ServiceOptions> _serviceOptions,
	ITokenGenerationService _tokenGenerationService,
	IWebConnectionService _connection) : IOrgManagementService
{
}