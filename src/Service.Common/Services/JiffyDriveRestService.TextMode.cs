namespace Apex.Services;

using Apex.Models;
using System;
using System.Text;

/// <summary>
/// Class JiffyDriveRestService. This class cannot be inherited.
/// </summary>
public sealed partial class JiffyDriveRestService
{
	/// <summary>
	/// Gets the file as text.
	/// </summary>
	/// <param name="filePath">The file path.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	Task<string?> IJiffyDriveRestService.GetFileAsTextAsync(string? filePath, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(filePath);

		return InternalGetFileAsTextAsync(ToJiffyDriveObjectsUrl(filePath!), userInfo, cancellationToken);
	}

	/// <summary>
	/// Bulks get files.
	/// </summary>
	/// <param name="filePaths">The file paths.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	async Task<IEnumerable<string?>> IJiffyDriveRestService.BulkGetFilesAsTextAsync(string?[] filePaths, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(filePaths);

		var NULLSTRINGTask = Task.FromResult((string?)null);
		return await Task.WhenAll(filePaths.Select(filePath =>
		{
			return filePath.IsNullOrEmpty()
				? NULLSTRINGTask
				: InternalGetFileAsTextAsync(ToJiffyDriveObjectsUrl(filePath!), userInfo, cancellationToken);
		}).ToArray());
	}

	/// <summary>
	/// download the file.
	/// </summary>
	/// <param name="filePath">The file path.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	async Task<(string? FilePath, string? FileName)> IJiffyDriveRestService.DownloadFileAsTextAsync(string? filePath, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(filePath);

		var targetFileName = Path.GetFileName(filePath);
		var targetFilePath = Path.Combine(Path.GetTempPath(), Path.GetRandomFileName() + targetFileName);

		using var targetFileStream = File.OpenWrite(targetFilePath);
		await InternalDownloadFileToStreamAsync(targetFileStream, ToJiffyDriveObjectsUrl(filePath!), userInfo, cancellationToken);
		return (targetFilePath, targetFileName);
	}

	/// <summary>
	/// Bulk download files.
	/// </summary>
	/// <param name="filePaths">The file paths.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	async Task<IEnumerable<(string? FilePath, string? FileName)>> IJiffyDriveRestService.BulkDownloadFilesAsTextAsync(string?[] filePaths, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(filePaths);

		var NULLSTRINGTask = ((string?)null, (string?)null);
		var downloadTasks = filePaths.Select(async filePath =>
		{
			if (filePath.IsNullOrEmpty())
			{
				return NULLSTRINGTask;
			}

			var targetFileName = Path.GetFileName(filePath)!;
			var targetFilePath = Path.Combine(Path.GetTempPath(), Path.GetRandomFileName() + targetFileName);

			using var targetFileStream = File.OpenWrite(targetFilePath);
			await InternalDownloadFileToStreamAsync(targetFileStream, ToJiffyDriveObjectsUrl(filePath!), userInfo, cancellationToken);
			return (targetFilePath, targetFileName!);
		});

		return (await Task.WhenAll(downloadTasks)).ToArray();
	}

	/// <summary>
	/// Uploads the text content into a file.
	/// </summary>
	/// <param name="targetJiffyDriveFilePath">The target jiffy drive file path.</param>
	/// <param name="fileContent">Content of the file.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	Task<string?> IJiffyDriveRestService.UploadContentAsTextAsync(string? targetJiffyDriveFilePath, string? fileContent, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(targetJiffyDriveFilePath);
		return InternalUploadAsTextAsync(ToJiffyDriveObjectsUrl(targetJiffyDriveFilePath), fileContent, Path.GetFileName(targetJiffyDriveFilePath), userInfo, cancellationToken);
	}

	/// <summary>
	/// Uploads the file as text.
	/// </summary>
	/// <param name="targetJiffyDriveFolderPath">The target jiffy drive path.</param>
	/// <param name="filePath">The file path.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	Task<string?> IJiffyDriveRestService.UploadFileAsTextAsync(string? targetJiffyDriveFolderPath, string? filePath, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(targetJiffyDriveFolderPath);
		ArgumentNullException.ThrowIfNull(filePath);

		var sourceFilePath = filePath!;
		return InternalReadFileAndUploadAsTextAsync(ToJiffyDriveObjectsUrlEx(targetJiffyDriveFolderPath, sourceFilePath), sourceFilePath, userInfo, cancellationToken);
	}

	/// <summary>
	/// Bulk upload files.
	/// </summary>
	/// <param name="filePaths">The file paths.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;System.Nullable&lt;System.String&gt;&gt;&gt;.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	async Task<IEnumerable<string?>> IJiffyDriveRestService.BulkUploadFilesAsTextAsync((string JiffyDrivePath, string LocalFilePath)[] filePaths, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(filePaths);
		return (await Task.WhenAll(filePaths.Select(filePath => InternalReadFileAndUploadAsTextAsync(ToJiffyDriveObjectsUrl(filePath.JiffyDrivePath), filePath.LocalFilePath, userInfo, cancellationToken))))
			.ToArray();
	}

	/// <summary>
	/// Bulk upload files.
	/// </summary>
	/// <param name="jiffyDriveBaseFolderPath">The target jiffy drive path.</param>
	/// <param name="filePaths">The file paths.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;System.Nullable&lt;System.String&gt;&gt;&gt;.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	async Task<IEnumerable<string?>> IJiffyDriveRestService.BulkUploadFilesAsTextAsync(string? jiffyDriveBaseFolderPath, string?[] filePaths, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(jiffyDriveBaseFolderPath);
		ArgumentNullException.ThrowIfNull(filePaths);

		var NULLSTRINGTask = Task.FromResult((string?)null);
		return await Task.WhenAll(filePaths.Select(filePath =>
		{
			var sourceFilePath = filePath!;
			return filePath.IsNullOrEmpty()
				? NULLSTRINGTask
				: InternalReadFileAndUploadAsTextAsync(ToJiffyDriveObjectsUrlEx(jiffyDriveBaseFolderPath, sourceFilePath), sourceFilePath, userInfo, cancellationToken);
		}).ToArray());
	}

	/// <summary>
	/// Bulk upload file content
	/// </summary>
	/// <param name="fileContents">The file contents.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;System.Nullable&lt;System.String&gt;&gt;&gt;.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	async Task<IEnumerable<string?>> IJiffyDriveRestService.BulkUploadContentAsTextAsync((string JiffyDrivePath, string? Content)[] fileContents, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(fileContents);
		return await Task.WhenAll(fileContents.Select(fileInfo =>
		{
			var (jiffyDrivePath, fileContent) = (fileInfo.JiffyDrivePath, fileInfo.Content);
			return InternalUploadAsTextAsync(ToJiffyDriveObjectsUrl(jiffyDrivePath), fileContent, Path.GetFileName(jiffyDrivePath), userInfo, cancellationToken);
		}).ToArray());
	}

	/// <summary>
	/// helper to get file as a string.
	/// </summary>
	/// <param name="templateUri">The template URI.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	/// <exception cref="Apex.Services.JiffyDriveException">unable to access the template file from Jiffy Drive - Template={templateUri} Status={responseMessage.StatusCode} DriveError={responseString}</exception>
	/// <exception cref="Apex.Services.JiffyDriveException">template found in Jiffy Drive, but was empty - Template={templateUri}</exception>
	/// <exception cref="System.ArgumentException">unable to access the template file from Jiffy Drive - Template={templateUri} Status={responseMessage.StatusCode} DriveError={responseString}</exception>
	private async Task<string?> InternalGetFileAsTextAsync(string templateUri, UserSecurityInfo userInfo, CancellationToken cancellationToken = default)
	{
		var requestMessage = await InternalGetRequestMessage(templateUri, userInfo, cancellationToken);

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		var responseString = await responseMessage.Content.ReadAsStringAsync(cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new JiffyDriveException($"unable to access the file from Jiffy Drive - Template={templateUri} Status={responseMessage.StatusCode} DriveError={responseString}");
		}

		return responseString;
	}

	/// <summary>
	/// upload file helper.
	/// </summary>
	/// <param name="targetUri">The target URI.</param>
	/// <param name="sourceFilePath">The source file path.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	private async Task<string?> InternalReadFileAndUploadAsTextAsync(string targetUri, string sourceFilePath, UserSecurityInfo userInfo, CancellationToken cancellationToken = default)
	{
		return await InternalUploadFileContentAsBinaryStreamAsync(ToJiffyDriveObjectsUrl(targetUri),
			sourceFilePath,
			Path.GetFileName(sourceFilePath), userInfo, cancellationToken: cancellationToken);
	}

	/// <summary>
	/// Internals the upload file asynchronous.
	/// </summary>
	/// <param name="targetUri">The target URI.</param>
	/// <param name="fileContents">The file contents.</param>
	/// <param name="fileName">Name of the file.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	private Task<string?> InternalUploadAsTextAsync(string targetUri, string? fileContents, string fileName, UserSecurityInfo userInfo, CancellationToken cancellationToken = default)
		=> InternalUploadFileContentAsBinaryAsync(targetUri,
			fileContents is null ? [] : Encoding.UTF8.GetBytes(fileContents),
			fileName, userInfo, cancellationToken: cancellationToken);
}