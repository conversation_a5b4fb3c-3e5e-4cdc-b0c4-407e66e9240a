using Google.Protobuf;

namespace Apex.Services;

/// <summary>
/// Class AppManagerService. This class cannot be inherited.
/// </summary>
public sealed partial class AppManagerService
{
	/// <summary>
	/// Get application components configuration.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="componentUrlFragment">The component URL fragment.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IEnumerable`1&gt; representing the asynchronous operation.</returns>
	/// <exception cref="AppManagerException">$"error while getting component configuration - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	/// <exception cref="TokenGenerationException">$"error while getting component configuration - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public Task<T?> GetServiceInfoAsync<T>(string tenantId, string appId, string componentUrlFragment,
		CancellationToken cancellationToken = default)
		where T : IMessage<T>, new()
	{
		var requestUrl = $"{_serviceOptions.Value.ModelRepoUrl?.TrimEnd('/')}/rest/v1/tenant/{tenantId}/app/{appId}/composite{componentUrlFragment}";
		_logger.LogInformation("GetServiceInfoAsync - Url={Url}", requestUrl);

		return GetAsGrpcJsonAsync<T>(requestUrl, tenantId, appId, cancellationToken);
	}

	/// <summary>
	/// Get as GRPC json.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="url">The URL.</param>
	/// <param name="tenantId">The tenant Id.</param>
	/// <param name="appId">The app Id.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;EventRoutingResponse&gt;&gt;.</returns>
	public async Task<T?> GetAsGrpcJsonAsync<T>(string url, string tenantId, string appId, CancellationToken cancellationToken = default)
		where T : IMessage<T>, new()
	{
		var responseMessage = await InternalGetUrlResponseAsync(url, tenantId, appId, null, cancellationToken);
		var responseString = await responseMessage.Content.ReadAsStringAsync(cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new AppManagerException($"unable to get a response from the url - TargetUrl={url} Status={responseMessage.StatusCode} Error={responseString}");
		}
		_logger.LogDebug("GetAsGrpcJsonAsync - Url={Url} Response={Response}", url, responseString);
		return ParseJson<T>(responseString);
	}

	/// <summary>
	/// Manually Patching JSON - https://github.com/protocolbuffers/protobuf/issues/8316
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="json"></param>
	/// <param name="discardUnknownFields"></param>
	/// <returns></returns>
	private static T ParseJson<T>(string json, bool discardUnknownFields = true)
		where T : IMessage<T>, new()
	{
		var jp = new JsonParser(JsonParser.Settings.Default.WithIgnoreUnknownFields(discardUnknownFields));
		return jp.Parse<T>(json);
	}
}