namespace Apex.Services;

using Apex.Models;
using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;

/// <summary>
/// Class DataService . This class cannot be inherited.
/// </summary>
public sealed partial class DataService
{
	/// <summary>
	/// Inserts the object.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="endPointName">End name of the point.</param>
	/// <param name="instance">The instance.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;T&gt;&gt;.</returns>
	public async Task<T?> InsertObjectAsync<T>(string tenantId, string appId, string endPointName, T instance, CancellationToken cancellationToken = default)
		where T : class
		=> await InternalInsertUpdateObjectAsync(tenantId, appId, endPointName, instance, HttpMethod.Post, cancellationToken: cancellationToken);

	/// <summary>
	/// Updates the object.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="endPointName">End name of the point.</param>
	/// <param name="id">The identifier.</param>
	/// <param name="instance">The instance.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;T&gt;&gt;.</returns>
	public async Task<T?> UpdateObjectAsync<T>(string tenantId, string appId, string endPointName, string id, T instance, CancellationToken cancellationToken = default)
		where T : class
		=> await InternalInsertUpdateObjectAsync(tenantId, appId, endPointName, instance, HttpMethod.Put, urlSuffix: id, cancellationToken);

	/// <summary>
	/// helper to insert/update object.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="endPointName">End name of the point.</param>
	/// <param name="instance">The instance.</param>
	/// <param name="httpMethod">The HTTP method.</param>
	/// <param name="urlSuffix">The URL suffix.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;T&gt;.</returns>
	private async Task<T?> InternalInsertUpdateObjectAsync<T>(string tenantId, string appId, string endPointName, T instance, HttpMethod httpMethod, string? urlSuffix = null, CancellationToken cancellationToken = default)
		where T : class
	{
		ArgumentException.ThrowIfNullOrWhiteSpace(tenantId);
		ArgumentException.ThrowIfNullOrWhiteSpace(appId);
		ArgumentException.ThrowIfNullOrWhiteSpace(endPointName);

		var userId = await _tokenGeneration.ResolveUserIdFromTenantAppIdAsync(tenantId, appId, cancellationToken: cancellationToken);
		_logger.LogInformation("Resolving TenantId/AppId to user - TenantId={TenantId} AppId={AppId} UserId={UserId}", tenantId, appId, userId);

		var url = $"{_serviceOptions.Value.DataServiceUrl?.TrimEnd('/')}/{endPointName}/"; // adding / for API..
		if (urlSuffix is not null)
		{
			url = $"{url}{urlSuffix}";
		}
		_logger.LogInformation("Querying data service: Url={Url}", url);

		var userInfo = new UserSecurityInfo(tenantId, appId, userId, null);
		var requestMessage = await InternalGetRequestMessageAsync(url, userInfo, cancellationToken);
		requestMessage.Method = httpMethod;
		requestMessage.Content = JsonContent.Create(instance, options: SerializationExtensions._SERIALIZEROPTIONS);

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new DataServiceException($"unable to insert/update object - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}");
		}
		return await responseMessage.Content.ReadFromJsonAsync<T>(cancellationToken);
	}

	/// <summary>
	/// Deletes the objects with the given ids.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="endPointName">End name of the point.</param>
	/// <param name="ids">The ids.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;T&gt;&gt;.</returns>
	public async Task DeleteObjectsAsync(string tenantId, string appId, string endPointName, ICollection<string> ids, CancellationToken cancellationToken = default)
	{
		foreach (var id in ids)
		{
			await InternalDeleteObjectAsync(tenantId, appId, endPointName, id, cancellationToken);
		}
	}

	/// <summary>
	/// Helper to delete object.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="endPointName">End name of the point.</param>
	/// <param name="id">The identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;T&gt;.</returns>
	/// <exception cref="DataServiceException">$"unable to delete objects - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	private async Task InternalDeleteObjectAsync(string tenantId, string appId, string endPointName, string id, CancellationToken cancellationToken = default)
	{
		ArgumentException.ThrowIfNullOrWhiteSpace(tenantId);
		ArgumentException.ThrowIfNullOrWhiteSpace(appId);
		ArgumentException.ThrowIfNullOrWhiteSpace(endPointName);

		var userId = await _tokenGeneration.ResolveUserIdFromTenantAppIdAsync(tenantId, appId, cancellationToken: cancellationToken);
		_logger.LogInformation("Resolving TenantId/AppId to user - TenantId={TenantId} AppId={AppId} UserId={UserId}", tenantId, appId, userId);

		var url = $"{_serviceOptions.Value.DataServiceUrl?.TrimEnd('/')}/{endPointName}/{id}"; // adding / for API..
		_logger.LogInformation("Querying data service: Url={Url}", url);

		var userInfo = new UserSecurityInfo(tenantId, appId, userId, null);
		var requestMessage = await InternalGetRequestMessageAsync(url, userInfo, cancellationToken);
		requestMessage.Method = HttpMethod.Delete;

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new DataServiceException($"unable to delete object - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}");
		}
	}

#if DEBUG2
	/// <summary>
	/// Helper to delete objects.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="endPointName">End name of the point.</param>
	/// <param name="ids">The ids.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;T&gt;.</returns>
	/// <exception cref="DataServiceException">$"unable to delete objects - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	private async Task InternalDeleteObjectsAsync(string tenantId, string appId, string endPointName, ICollection<string> ids, CancellationToken cancellationToken = default)
	{
		ArgumentException.ThrowIfNullOrWhiteSpace(tenantId);
		ArgumentException.ThrowIfNullOrWhiteSpace(appId);
		ArgumentException.ThrowIfNullOrWhiteSpace(endPointName);

		var userId = await _tokenGeneration.ResolveUserIdFromTenantAppIdAsync(tenantId, appId, cancellationToken: cancellationToken);
		_logger.LogInformation("Resolving TenantId/AppId to user - TenantId={TenantId} AppId={AppId} UserId={UserId}", tenantId, appId, userId);

		var url = $"{_serviceOptions.Value.DataServiceUrl?.TrimEnd('/')}/{endPointName}/?id={ids.JoinWith()}"; // adding / for API..
		_logger.LogInformation("Querying data service: Url={Url}", url);

		var userInfo = new UserSecurityInfo(tenantId, appId, userId, null);
		var requestMessage = await InternalGetRequestMessageAsync(url, userInfo, cancellationToken);
		requestMessage.Method = HttpMethod.Delete;

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new DataServiceException($"unable to delete objects - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}");
		}
	}
#endif

	/// <summary>
	/// Gets the records using dynamic query.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="endPointName">End name of the point.</param>
	/// <param name="dynamicQuery">The dynamic query.</param>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;T&gt;&gt;.</returns>
	/// <exception cref="MessengerException">$"unable to get value from data service - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	/// <exception cref="System.ArgumentNullException">$"unable to get value from data service - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public async Task<IList<T>> GetRecordsUsingDynamicQueryAsync<T>(string tenantId, string appId, string endPointName, JsonDocument dynamicQuery, int pageIndex = 0, int pageSize = 100, CancellationToken cancellationToken = default)
	{
		ArgumentException.ThrowIfNullOrWhiteSpace(tenantId);
		ArgumentException.ThrowIfNullOrWhiteSpace(appId);
		ArgumentException.ThrowIfNullOrWhiteSpace(endPointName);
		ArgumentNullException.ThrowIfNull(dynamicQuery);

		var userId = await _tokenGeneration.ResolveUserIdFromTenantAppIdAsync(tenantId, appId, cancellationToken: cancellationToken);
		_logger.LogInformation("Resolving TenantId/AppId to user - TenantId={TenantId} AppId={AppId} UserId={UserId}", tenantId, appId, userId);

		var url = $"{_serviceOptions.Value.DataServiceUrl?.TrimEnd('/')}/{endPointName}/dynamic/query?_size={pageSize}&_page={pageIndex}";
		_logger.LogInformation("Querying data service: Url={Url} Query={Query}", url, dynamicQuery.ToJSON());

		var userInfo = new UserSecurityInfo(tenantId, appId, userId, null);
		var requestMessage = await InternalGetRequestMessageAsync(url, userInfo, cancellationToken);
		requestMessage.Method = HttpMethod.Post;
		requestMessage.Content = JsonContent.Create(dynamicQuery, options: SerializationExtensions._SERIALIZEROPTIONS);

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new DataServiceException($"unable to get records using dynamic query from data service - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}");
		}
		return (await responseMessage.Content.ReadFromJsonAsync<IList<T>>(cancellationToken)) ?? [];
	}

	/// <summary>
	/// Gets the records.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="endPointName">End name of the point.</param>
	/// <param name="fields">The fields.</param>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;T&gt;&gt;.</returns>
	/// <exception cref="MessengerException">$"unable to get value from data service - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	/// <exception cref="System.ArgumentNullException">$"unable to get value from data service - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public async Task<IList<T>> GetRecordsAsync<T>(string tenantId, string appId, string endPointName, string[] fields, int pageIndex = 0, int pageSize = 100, CancellationToken cancellationToken = default)
	{
		ArgumentException.ThrowIfNullOrWhiteSpace(tenantId);
		ArgumentException.ThrowIfNullOrWhiteSpace(appId);
		ArgumentException.ThrowIfNullOrWhiteSpace(endPointName);
		ArgumentNullException.ThrowIfNull(fields);

		var userId = await _tokenGeneration.ResolveUserIdFromTenantAppIdAsync(tenantId, appId, cancellationToken: cancellationToken);
		_logger.LogInformation("Resolving TenantId/AppId to user - TenantId={TenantId} AppId={AppId} UserId={UserId}", tenantId, appId, userId);

		var boFieldNames = fields.JoinWith(',');
		var url = $"{_serviceOptions.Value.DataServiceUrl?.TrimEnd('/')}/{endPointName}?_size={pageSize}&_page={pageIndex}&_sort=&_fields={boFieldNames}";
		_logger.LogInformation("Querying data service: Url={Url}", url);

		var userInfo = new UserSecurityInfo(tenantId, appId, userId, null);
		var requestMessage = await InternalGetRequestMessageAsync(url, userInfo, cancellationToken);

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new DataServiceException($"unable to get value from data service - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}");
		}
		return (await responseMessage.Content.ReadFromJsonAsync<IList<T>>(cancellationToken)) ?? [];
	}

	/// <summary>
	/// Helper method to get request message.
	/// </summary>
	/// <param name="requestUrl">The template URI.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>HttpRequestMessage.</returns>
	public async Task<HttpRequestMessage> InternalGetRequestMessageAsync(string requestUrl, UserSecurityInfo? userInfo, CancellationToken cancellationToken)
	{
		var requestMessage = new HttpRequestMessage
		{
			RequestUri = new Uri(requestUrl),
			Method = HttpMethod.Get,
		};

		var targetHeaders = requestMessage.Headers;
		userInfo?.UpdateWebHeaders(targetHeaders);

		var newAccessToken = await _tokenGeneration.CheckTokenExpiryAndGenerateTokenAsync(cancellationToken: cancellationToken);
		if (newAccessToken is not null)
		{
			targetHeaders.Authorization = new AuthenticationHeaderValue("Bearer", newAccessToken);
		}

		return requestMessage;
	}
}