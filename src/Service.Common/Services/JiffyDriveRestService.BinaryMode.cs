namespace Apex.Services;

using Apex.Models;
using System;
using System.Net.Http.Headers;
using System.Text;

/// <summary>
/// Class JiffyDriveRestService. This class cannot be inherited.
/// </summary>
public sealed partial class JiffyDriveRestService
{
	/// <summary>
	/// Gets the file as binary.
	/// </summary>
	/// <param name="filePath">The file path.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	Task<byte[]?> IJiffyDriveRestService.GetFileAsBinaryAsync(string? filePath, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(filePath);

		return InternalDownloadFileAsBinaryAsync(ToJiffyDriveObjectsUrl(filePath!), userInfo, cancellationToken);
	}

	/// <summary>
	/// Bulks get files.
	/// </summary>
	/// <param name="filePaths">The file paths.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	async Task<IEnumerable<byte[]?>> IJiffyDriveRestService.BulkGetFilesAsBinaryAsync(string?[] filePaths, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(filePaths);

		var NULLSTRINGTask = Task.FromResult((byte[]?)null);
		return await Task.WhenAll(filePaths.Select(filePath =>
		{
			return filePath.IsNullOrEmpty()
				? NULLSTRINGTask
				: InternalDownloadFileAsBinaryAsync(ToJiffyDriveObjectsUrl(filePath!), userInfo, cancellationToken);
		}).ToArray());
	}

	/// <summary>
	/// download the file.
	/// </summary>
	/// <param name="filePath">The file path.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	async Task<(string? FilePath, string? FileName)> IJiffyDriveRestService.DownloadFileAsBinaryAsync(string? filePath, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(filePath);

		var targetFileName = Path.GetFileName(filePath);
		var targetFilePath = Path.Combine(Path.GetTempPath(), Path.GetRandomFileName() + targetFileName);

		using var targetFileStream = File.OpenWrite(targetFilePath);
		await InternalDownloadFileToStreamAsync(targetFileStream, ToJiffyDriveObjectsUrl(filePath!), userInfo, cancellationToken);
		return (targetFilePath, targetFileName);
	}

	/// <summary>
	/// Bulk download files.
	/// </summary>
	/// <param name="filePaths">The file paths.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	async Task<IEnumerable<(string? FilePath, string? FileName)>> IJiffyDriveRestService.BulkDownloadFilesAsBinaryAsync(string?[] filePaths, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(filePaths);

		var NULLSTRINGTask = ((string?)null, (string?)null);
		var downloadTasks = filePaths.Select(async filePath =>
		{
			if (filePath.IsNullOrEmpty())
			{
				return NULLSTRINGTask;
			}

			var targetFileName = Path.GetFileName(filePath)!;
			var targetFilePath = Path.Combine(Path.GetTempPath(), Path.GetRandomFileName() + targetFileName);

			using var targetFileStream = File.OpenWrite(targetFilePath);
			await InternalDownloadFileToStreamAsync(targetFileStream, ToJiffyDriveObjectsUrl(filePath!), userInfo, cancellationToken);
			return (targetFilePath, targetFileName!);
		});

		return [.. (await Task.WhenAll(downloadTasks))];
	}

	/// <summary>
	/// Uploads the binary content into a file.
	/// </summary>
	/// <param name="targetJiffyDriveFilePath">The target jiffy drive file path.</param>
	/// <param name="fileContent">Content of the file.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="explodeZipFile">The explode zip file.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	Task<string?> IJiffyDriveRestService.UploadContentAsBinaryAsync(string? targetJiffyDriveFilePath, byte[]? fileContent, UserSecurityInfo userInfo, bool explodeZipFile, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(targetJiffyDriveFilePath);
		return InternalUploadContentAsBinaryAsync(ToJiffyDriveObjectsUrl(targetJiffyDriveFilePath), fileContent, Path.GetFileName(targetJiffyDriveFilePath), userInfo, explodeZipFile, cancellationToken);
	}

	/// <summary>
	/// Uploads the file as binary.
	/// </summary>
	/// <param name="targetJiffyDriveFolderPath">The target jiffy drive path.</param>
	/// <param name="filePath">The file path.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="explodeZipFile">The explode zip file.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	Task<string?> IJiffyDriveRestService.UploadFileAsBinaryAsync(string? targetJiffyDriveFolderPath, string? filePath, UserSecurityInfo userInfo, bool explodeZipFile, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(targetJiffyDriveFolderPath);
		ArgumentNullException.ThrowIfNull(filePath);

		var sourceFilePath = filePath!;
		return InternalReadFileAndUploadAsBinaryAsync(ToJiffyDriveObjectsUrlEx(targetJiffyDriveFolderPath, sourceFilePath), sourceFilePath, userInfo, explodeZipFile, cancellationToken);
	}

	/// <summary>
	/// Bulk upload files.
	/// </summary>
	/// <param name="filePaths">The file paths.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;System.Nullable&lt;System.String&gt;&gt;&gt;.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	async Task<IEnumerable<string?>> IJiffyDriveRestService.BulkUploadFilesAsBinaryAsync((string JiffyDrivePath, string LocalFilePath)[] filePaths, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(filePaths);
		return [.. (await Task.WhenAll(filePaths.Select(filePath => InternalReadFileAndUploadAsBinaryAsync(ToJiffyDriveObjectsUrl(filePath.JiffyDrivePath), filePath.LocalFilePath, userInfo, cancellationToken: cancellationToken))))];
	}

	/// <summary>
	/// Bulks the upload files as binary stream asynchronous.
	/// </summary>
	/// <param name="filePaths">The file paths.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;System.Nullable&lt;System.String&gt;&gt;&gt;.</returns>
	async Task<IEnumerable<string?>> IJiffyDriveRestService.BulkUploadFilesAsBinaryStreamAsync((string JiffyDrivePath, string LocalFilePath)[] filePaths, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(filePaths);
		return [.. (await Task.WhenAll(filePaths.Select(filePath => InternalReadFileAndUploadAsBinaryStreamAsync(ToJiffyDriveObjectsUrl(filePath.JiffyDrivePath), filePath.LocalFilePath, userInfo, cancellationToken: cancellationToken))))];
	}

	/// <summary>
	/// Bulk upload files.
	/// </summary>
	/// <param name="jiffyDriveBaseFolderPath">The target jiffy drive path.</param>
	/// <param name="filePaths">The file paths.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;System.Nullable&lt;System.String&gt;&gt;&gt;.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	async Task<IEnumerable<string?>> IJiffyDriveRestService.BulkUploadFilesAsBinaryAsync(string? jiffyDriveBaseFolderPath, string?[] filePaths, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(jiffyDriveBaseFolderPath);
		ArgumentNullException.ThrowIfNull(filePaths);

		var NULLSTRINGTask = Task.FromResult((string?)null);
		return await Task.WhenAll(filePaths.Select(filePath =>
		{
			var sourceFilePath = filePath!;
			return filePath.IsNullOrEmpty()
				? NULLSTRINGTask
				: InternalReadFileAndUploadAsBinaryAsync(ToJiffyDriveObjectsUrlEx(jiffyDriveBaseFolderPath, sourceFilePath), sourceFilePath, userInfo, cancellationToken: cancellationToken);
		}).ToArray());
	}

	/// <summary>
	/// Bulk upload file content
	/// </summary>
	/// <param name="fileContents">The file contents.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;System.Nullable&lt;System.String&gt;&gt;&gt;.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	async Task<IEnumerable<string?>> IJiffyDriveRestService.BulkUploadContentAsBinaryAsync((string JiffyDrivePath, byte[]? Content)[] fileContents, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(fileContents);
		return await Task.WhenAll(fileContents.Select(fileInfo =>
		{
			var (jiffyDrivePath, fileContent) = (fileInfo.JiffyDrivePath, fileInfo.Content);
			return InternalUploadContentAsBinaryAsync(ToJiffyDriveObjectsUrl(jiffyDrivePath), fileContent, Path.GetFileName(jiffyDrivePath), userInfo, cancellationToken: cancellationToken);
		}).ToArray());
	}

	/// <summary>
	/// upload file helper.
	/// </summary>
	/// <param name="targetUri">The target URI.</param>
	/// <param name="sourceFilePath">The source file path.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="explodeZipFile">The explode zip file.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	private async Task<string?> InternalReadFileAndUploadAsBinaryAsync(string targetUri, string sourceFilePath, UserSecurityInfo userInfo,
		bool explodeZipFile = false, CancellationToken cancellationToken = default)
	{
		return await InternalUploadFileContentAsBinaryStreamAsync(ToJiffyDriveObjectsUrl(targetUri),
			sourceFilePath,
			Path.GetFileName(sourceFilePath), userInfo, explodeZipFile, cancellationToken);
	}

	/// <summary>
	/// upload file helper.
	/// </summary>
	/// <param name="targetUri">The target URI.</param>
	/// <param name="sourceFilePath">The source file path.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="explodeZipFile">The explode zip file.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	private async Task<string?> InternalReadFileAndUploadAsBinaryStreamAsync(string targetUri, string sourceFilePath, UserSecurityInfo userInfo,
		bool explodeZipFile = false, CancellationToken cancellationToken = default)
	{
		return await InternalUploadFileContentAsBinaryStreamAsync(ToJiffyDriveObjectsUrl(targetUri),
			sourceFilePath,
			Path.GetFileName(sourceFilePath), userInfo, explodeZipFile, cancellationToken);
	}

	/// <summary>
	/// Internals the upload file asynchronous.
	/// </summary>
	/// <param name="targetUri">The target URI.</param>
	/// <param name="fileContents">The file contents.</param>
	/// <param name="fileName">Name of the file.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="explodeZipFile">The explode zip file.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	private Task<string?> InternalUploadContentAsBinaryAsync(string targetUri, byte[]? fileContents, string fileName, UserSecurityInfo userInfo, bool explodeZipFile = false, CancellationToken cancellationToken = default)
		=> InternalUploadFileContentAsBinaryAsync(targetUri,
			fileContents ?? [],
			fileName, userInfo, explodeZipFile, cancellationToken);

	/// <summary>
	/// upload file helper.
	/// </summary>
	/// <param name="targetUri">The template URI.</param>
	/// <param name="contents">The contents.</param>
	/// <param name="fileName">Name of the file.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="explodeZipFile">The explode zip file.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	/// <exception cref="JiffyDriveException">$"unable to upload the file to Jiffy Drive - TargetUrl={targetUri} FileName={fileName} Status={responseMessage.StatusCode} DriveError={responseString}</exception>
	/// <exception cref="Apex.Services.JiffyDriveException">$"unable to upload the file to Jiffy Drive - TargetUrl={targetUri} FileName={fileName} Status={responseMessage.StatusCode} DriveError={responseString}</exception>
	/// <exception cref="System.ArgumentException">unable to upload the file to Jiffy Drive - TargetUrl={targetUri} FileName={fileName} Status={responseMessage.StatusCode} DriveError={responseString}</exception>
	private async Task<string?> InternalUploadFileContentAsBinaryAsync(string targetUri, byte[]? contents, string fileName, UserSecurityInfo userInfo, bool explodeZipFile = false, CancellationToken cancellationToken = default)
	{
		if (explodeZipFile)
		{
			targetUri += "?isFolder=true";
		}

		using var requestMessage = new HttpRequestMessage
		{
			RequestUri = new Uri(targetUri),
			Method = HttpMethod.Post,
			Content = new MultipartFormDataContent
			{
				{
					new ByteArrayContent(contents ?? []), "object", fileName
				},
				{
					new StringContent("{}", Encoding.UTF8, "application/json"), "config"
				}
			}
		};

		// update headers if exists
		var targetHeaders = requestMessage.Headers;
		userInfo?.UpdateWebHeaders(targetHeaders);

#if DEBUG
		var newAccessToken = userInfo?.AccessToken ?? await _tokenGeneration.CheckTokenExpiryAndGenerateTokenAsync(cancellationToken: cancellationToken);
#else
		var newAccessToken = await _tokenGeneration.CheckTokenExpiryAndGenerateTokenAsync(cancellationToken: cancellationToken);
#endif
		if (newAccessToken is not null)
		{
			targetHeaders.Authorization = new AuthenticationHeaderValue("Bearer", newAccessToken);
		}

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			var responseString = await responseMessage.Content.ReadAsStringAsync(cancellationToken);
			throw new JiffyDriveException($"unable to upload the file to Jiffy Drive - TargetUrl={targetUri} FileName={fileName} Status={responseMessage.StatusCode} DriveError={responseString}");
		}

		return targetUri;
	}

	/// <summary>
	/// Internals the upload file content as binary stream.
	/// </summary>
	/// <param name="targetUri">The target URI.</param>
	/// <param name="filePath">The file path.</param>
	/// <param name="fileName">Name of the file.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="explodeZipFile">The explode zip file.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;System.String&gt;.</returns>
	private async Task<string?> InternalUploadFileContentAsBinaryStreamAsync(string targetUri, string filePath, string fileName, UserSecurityInfo userInfo, bool explodeZipFile = false, CancellationToken cancellationToken = default)
	{
		if (explodeZipFile)
		{
			targetUri += "?isFolder=true";
		}

		using var requestMessage = new HttpRequestMessage
		{
			RequestUri = new Uri(targetUri),
			Method = HttpMethod.Post,
			Content = new MultipartFormDataContent
			{
				{
					new StreamContent(File.OpenRead(filePath)), "object", fileName
				},
				{
					new StringContent("{}", Encoding.UTF8, "application/json"), "config"
				}
			}
		};

		// update headers if exists
		var targetHeaders = requestMessage.Headers;
		userInfo?.UpdateWebHeaders(targetHeaders);

#if DEBUG
		var newAccessToken = userInfo?.AccessToken ?? await _tokenGeneration.CheckTokenExpiryAndGenerateTokenAsync(cancellationToken: cancellationToken);
#else
		var newAccessToken = await _tokenGeneration.CheckTokenExpiryAndGenerateTokenAsync(cancellationToken: cancellationToken);
#endif
		if (newAccessToken is not null)
		{
			targetHeaders.Authorization = new AuthenticationHeaderValue("Bearer", newAccessToken);
		}

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			var responseString = await responseMessage.Content.ReadAsStringAsync(cancellationToken);
			throw new JiffyDriveException($"unable to upload the file to Jiffy Drive - TargetUrl={targetUri} FileName={fileName} Status={responseMessage.StatusCode} DriveError={responseString}");
		}

		return targetUri;
	}

	/// <summary>
	/// download file as binary helper.
	/// </summary>
	/// <param name="filePath">The file path.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.Byte[]&gt; representing the asynchronous operation.</returns>
	private async Task<byte[]?> InternalDownloadFileAsBinaryAsync(string filePath, UserSecurityInfo userInfo, CancellationToken cancellationToken = default)
	{
		using var targetFileStream = new MemoryStream();
		await InternalDownloadFileToStreamAsync(targetFileStream, ToJiffyDriveObjectsUrl(filePath!), userInfo, cancellationToken);
		return targetFileStream.ToArray();
	}

	/// <summary>
	/// helper to download file.
	/// </summary>
	/// <param name="outputFile">The output file.</param>
	/// <param name="templateUri">The template URI.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task representing the asynchronous operation.</returns>
	/// <exception cref="Apex.Services.JiffyDriveException">unable to access the template file from Jiffy Drive - Template={templateUri} Status={responseMessage.StatusCode} DriveError={responseString}</exception>
	private async Task InternalDownloadFileToStreamAsync(Stream outputFile, string templateUri, UserSecurityInfo userInfo, CancellationToken cancellationToken = default)
	{
		var requestMessage = await InternalGetRequestMessage(templateUri, userInfo, cancellationToken);
		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			var responseString = await responseMessage.Content.ReadAsStringAsync(cancellationToken);
			throw new JiffyDriveException($"unable to access the file from Jiffy Drive - Template={templateUri} Status={responseMessage.StatusCode} DriveError={responseString}");
		}

		var responseStream = await responseMessage.Content.ReadAsStreamAsync(cancellationToken);
		await responseStream.CopyToAsync(outputFile, cancellationToken);
	}
}