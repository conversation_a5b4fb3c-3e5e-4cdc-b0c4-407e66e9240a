namespace Apex.Services;

using Apex.Options;
using Microsoft.Extensions.Options;

/// <summary>
/// Class MessengerRestService. This class cannot be inherited.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="MessengerRestService" /> class.
/// </remarks>
/// <param name="_serviceOptions">The service options.</param>
/// <param name="_connection">The connection.</param>
/// <param name="_tokenGeneration">The token generation.</param>
public sealed partial class MessengerRestService(IOptions<ServiceOptions> _serviceOptions, IWebConnectionService _connection, ITokenGenerationService _tokenGeneration)
	: IMessengerRestService
{
}