namespace Apex.Services;

using Apex.Models;
using System;
using System.Net.Http.Headers;
using System.Net.Http.Json;

/// <summary>
/// Class WorkflowRestService. This class cannot be inherited.
/// Implements the <see cref="Apex.Services.IWorkflowRestService" />
/// </summary>
/// <seealso cref="Apex.Services.IWorkflowRestService" />
public sealed partial class WorkflowRestService
{
	/// <summary>
	/// Signals the workflow instance - success or failure
	/// if success, a model
	/// if failed, an optional error message.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="workflowInstanceId">The workflow instance identifier.</param>
	/// <param name="signalId">The signal identifier.</param>
	/// <param name="model">The model.</param>
	/// <param name="hasError">if set to <c>true</c> [has error].</param>
	/// <param name="errorMessage">The error message.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;WorkflowOutput&gt;.</returns>
	public Task SignalWorkflowAsync<T>(string workflowInstanceId, string signalId, T? model, bool hasError, string? errorMessage, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default)
	{
		ArgumentException.ThrowIfNullOrEmpty(workflowInstanceId);
		ArgumentException.ThrowIfNullOrEmpty(signalId);

		return InternalSignalWorkflowAsync(workflowInstanceId, signalId, model, hasError, errorMessage, userInfo, cancellationToken);
	}

	/// <summary>
	/// Internal signals the workflow instance.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="workflowInstanceId">The workflow instance identifier.</param>
	/// <param name="signalId">The signal identifier.</param>
	/// <param name="model">The model.</param>
	/// <param name="hasError">The has error.</param>
	/// <param name="errorMessage">The error message.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	/// <exception cref="WorkflowInvokeException">$"unable to signal workflow - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	private async Task InternalSignalWorkflowAsync<T>(string workflowInstanceId, string signalId, T? model, bool hasError, string? errorMessage, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default)
	{
		var url = $"{_serviceOptions.Value.WorkflowUrl?.TrimEnd('/')}/workflow/v1/instances/{workflowInstanceId}/signal/{signalId}";
		var requestMessage = await InternalGetRequestMessageAsync(url, userInfo, cancellationToken);
		requestMessage.Method = HttpMethod.Post;
		if (model is not null)
		{
			requestMessage.Content = JsonContent.Create(model);
		}

		if (hasError)
		{
			requestMessage.Headers.Add("wf-fail-on-signal", "true");
			requestMessage.Headers.Add("wf-fail-on-signal-message", errorMessage);
		}

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new WorkflowInvokeException($"unable to signal workflow - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}");
		}
	}

	/// <summary>
	/// Invokes the workflow.
	/// </summary>
	/// <param name="workflowPath">The workflow path.</param>
	/// <param name="input">The input.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	public Task<WorkflowOutput> InvokeWorkflowAsync(string workflowPath, WorkflowInput input, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default)
	{
		ArgumentException.ThrowIfNullOrEmpty(workflowPath);
		ArgumentNullException.ThrowIfNull(input);

		return InternalInvokeWorkflowAsync(workflowPath, input, userInfo, cancellationToken);
	}

	/// <summary>
	/// Internal invoke workflow.
	/// </summary>
	/// <param name="workflowPath">The workflow path.</param>
	/// <param name="input">The input.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	/// <exception cref="Exception">$"unable to invoke/trigger workflow - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	private async Task<WorkflowOutput> InternalInvokeWorkflowAsync(string workflowPath, WorkflowInput input, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default)
	{
		var url = $"{_serviceOptions.Value.WorkflowUrl?.TrimEnd('/')}/workflow/v1/instances/execute/{(input.ExecuteSync ? "sync" : "async")}{workflowPath}";
		var requestMessage = await InternalGetRequestMessageAsync(url, userInfo, cancellationToken);
		requestMessage.Method = HttpMethod.Post;
		requestMessage.Content = JsonContent.Create(input);

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new WorkflowInvokeException($"unable to invoke/trigger workflow - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}");
		}

		return (await responseMessage.Content.ReadFromJsonAsync<WorkflowOutput>(SerializationExtensions._SERIALIZEROPTIONS, cancellationToken))!;
	}

	/// <summary>
	/// Helper method to get request message.
	/// </summary>
	/// <param name="requestUrl">The template URI.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>HttpRequestMessage.</returns>
	private async Task<HttpRequestMessage> InternalGetRequestMessageAsync(string requestUrl, UserSecurityInfo? userInfo, CancellationToken cancellationToken)
	{
		var requestMessage = new HttpRequestMessage
		{
			RequestUri = new Uri(requestUrl),
			Method = HttpMethod.Get,
		};

		var targetHeaders = requestMessage.Headers;
		userInfo?.UpdateWebHeaders(targetHeaders);

		var newAccessToken = await _tokenGeneration.CheckTokenExpiryAndGenerateTokenAsync(cancellationToken: cancellationToken);
		if (newAccessToken is not null)
		{
			targetHeaders.Authorization = new AuthenticationHeaderValue("Bearer", newAccessToken);
		}

		return requestMessage;
	}
}