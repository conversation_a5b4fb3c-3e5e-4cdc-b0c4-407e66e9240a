using Apex.Models;
using System.Text.Json.Serialization;

namespace Apex.Services;

/// <summary>
/// Class IamResultWrapper. This class cannot be inherited.
/// </summary>
/// <typeparam name="T"></typeparam>
public sealed record IamResultWrapper<T>
(
		[property: JsonPropertyName("content")] T[] Content,
		[property: JsonPropertyName("pageable")] string? Pageable,
		[property: JsonPropertyName("totalPages")] int TotalPages,
		[property: JsonPropertyName("totalElements")] int TotalElements,
		[property: JsonPropertyName("last")] bool Last,
		[property: JsonPropertyName("size")] int Size,
		[property: JsonPropertyName("number")] int Number,
		[property: JsonPropertyName("sort")] IamWrapperSort? Sort,
		[property: JsonPropertyName("numberOfElements")] int NumberOfElements,
		[property: JsonPropertyName("first")] bool First,
		[property: JsonPropertyName("empty")] bool Empty
	);

/// <summary>
/// Class IamWrapperSort. This class cannot be inherited.
/// </summary>
public sealed record IamWrapperSort
(
	[property: JsonPropertyName("sorted")] bool Sorted,
	[property: JsonPropertyName("empty")] bool Empty,
	[property: JsonPropertyName("unsorted")] bool Unsorted
);

/// <summary>
/// Class ResultWrapper. This class cannot be inherited.
/// </summary>
/// <typeparam name="T"></typeparam>
public sealed record OmsResultWrapper<T>
(
	[property: JsonPropertyName("Data")] T[] Data,
	[property: JsonPropertyName("CurrentPage")] int CurrentPage,
	[property: JsonPropertyName("TotalItems")] int TotalItems,
	[property: JsonPropertyName("TotalPages")] int TotalPages,
	[property: JsonPropertyName("PageSize")] int PageSize,
	[property: JsonPropertyName("Last")] bool Last
);

/// <summary>
/// Class Role. This class cannot be inherited.
/// </summary>
public sealed record Role
(
	[property: JsonPropertyName("roleId")] string Id,
	[property: JsonPropertyName("roleName")] string Name,
	[property: JsonPropertyName("default")] bool IsDefault,
	[property: JsonPropertyName("active")] bool IsActive
);

/// <summary>
/// Class Application. This class cannot be inherited.
/// </summary>
public sealed record Application
(
	[property: JsonPropertyName("tenantId")] string TenantId,
	[property: JsonPropertyName("tenantName")] string TenantName,
	[property: JsonPropertyName("version")] string Version,
	[property: JsonPropertyName("applicationId")] string ApplicationId,
	[property: JsonPropertyName("applicationName")] string ApplicationName,
	[property: JsonPropertyName("applicationType")] int ApplicationType,
	[property: JsonPropertyName("appUrl")] string AppUrl,
	[property: JsonPropertyName("appGlobalId")] string AppGlobalId,
	[property: JsonPropertyName("sandbox")] bool Sandbox
);

/// <summary>
/// Class AssociatedRole.
/// </summary>
public sealed record AssociatedRole
(
	[property: JsonPropertyName("role")] Role Role,
	[property: JsonPropertyName("application")] Application Application
);

/// <summary>
/// Class Provider. This class cannot be inherited.
/// </summary>
public sealed record Provider
(
	[property: JsonPropertyName("providerId")] string ProviderId,
	[property: JsonPropertyName("providerName")] string ProviderName,
	[property: JsonPropertyName("active")] bool IsActive,
	[property: JsonPropertyName("authorizationEnabled")] bool AuthorizationEnabled,
	[property: JsonPropertyName("authType")] string? AuthType,
	[property: JsonPropertyName("emailLoginEnabled")] bool EmailLoginEnabled
);

/// <summary>
/// Class Group. This class cannot be inherited.
/// </summary>
public sealed record Group
(
	[property: JsonPropertyName("groupId")] string GroupId,
	[property: JsonPropertyName("parentGroupName")] string? ParentGroupName,
	[property: JsonPropertyName("ownerAppName")] string? OwnerAppName,
	[property: JsonPropertyName("status")] string? Status,
	[property: JsonPropertyName("collabGroup")] bool CollabGroup,
	[property: JsonPropertyName("associatedRoles")] IList<AssociatedRole> AssociatedRoles,
	[property: JsonPropertyName("groupName")] string? GroupName,
	[property: JsonPropertyName("parentGroupId")] string? ParentGroupId,
	[property: JsonPropertyName("ownerAppId")] string OwnerAppId,
	[property: JsonPropertyName("userIds")] IList<string> UserIds,
	[property: JsonPropertyName("users")] IList<User> Users,
	[property: JsonPropertyName("description")] string? Description,
	[property: JsonPropertyName("providerId")] string? ProviderId,
	[property: JsonPropertyName("providerName")] string? ProviderName,
	[property: JsonPropertyName("providerGroup")] string? ProviderGroup,
	[property: JsonPropertyName("default")] bool IsDefault,
	[property: JsonPropertyName("active")] bool IsActive
);

/// <summary>
/// Class User. This class cannot be inherited.
/// </summary>
public sealed record User
(
	[property: JsonPropertyName("userId")] string Id,
	[property: JsonPropertyName("firstName")] string? FirstName,
	[property: JsonPropertyName("lastName")] string? LastName,
	[property: JsonPropertyName("email")] string? Email,
	[property: JsonPropertyName("userType")] string? UserType,
	[property: JsonPropertyName("disabled")] bool Disabled,
	[property: JsonPropertyName("userName")] string? UserName,
	[property: JsonPropertyName("extraFields")] string? ExtraFields,
	[property: JsonPropertyName("source")] string? Source,
	[property: JsonPropertyName("status")] string? Status,
	[property: JsonPropertyName("referenceId")] string ReferenceId,
	[property: JsonPropertyName("enableImpersonation")] bool EnableImpersonation,
	[property: JsonPropertyName("provider")] Provider Provider,
	[property: JsonPropertyName("middleName")] string? MiddleName,
	[property: JsonPropertyName("suffix")] string? Suffix,
	[property: JsonPropertyName("image")] string? Image,
	[property: JsonPropertyName("organisationId")] string? OrganisationId,
	[property: JsonPropertyName("externalUserId")] string? ExternalUserId
);

/// <summary>
/// Class Org. This class cannot be inherited.
/// </summary>
public sealed record Org
(
	[property: JsonPropertyName("orgId")] string OrgId,
	[property: JsonPropertyName("name")] string? Name,
	[property: JsonPropertyName("level")] int? Level,
	[property: JsonPropertyName("externalId")] string? ExternalId,
	[property: JsonPropertyName("type")] string? Type
);

/// <summary>
/// Class OmsUser. This class cannot be inherited.
/// </summary>
public sealed record OmsUser
(
	[property: JsonPropertyName("id")] int id,
	[property: JsonPropertyName("name")] string Name,
	[property: JsonPropertyName("emailId")] string EmailId,
	[property: JsonPropertyName("iamUserId")] string IamUserId,
	[property: JsonPropertyName("role")] string Role
);


/// <summary>
/// Class IOrgManagementService. This class cannot be inherited.
/// </summary>
public partial interface IOrgManagementService
{
	/// <summary>
	/// Gets the tenant root org.
	/// </summary>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other string?s or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	Task<Org?> GetTenantRootOrgAsync(UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the tenant orgs.
	/// </summary>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="pageIndex">The page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other string?s or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	Task<IList<Org>> GetTenantOrgsAsync(UserSecurityInfo userSecurityInfo, int pageIndex = 0, int pageSize = 100, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the user orgs.
	/// </summary>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other string?s or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	Task<IList<Org>> GetUserOrgsAsync(string iamUserId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the groups.
	/// </summary>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other string?s or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	Task<IList<Group>> GetGroupsAsync(UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the roles.
	/// </summary>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other string?s or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	Task<IList<Role>> GetRolesAsync(UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the users for role.
	/// </summary>
	/// <param name="roleId">Name of the role.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other string?s or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	Task<IList<User>> GetUsersForRoleAsync(string roleId, UserSecurityInfo userSecurityInfo, int pageIndex = 0, int pageSize = 100, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets all users for role.
	/// </summary>
	/// <param name="roleId">Name of the role.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other string?s or threads to receive notice of cancellation.</param>
	/// <returns>IAsyncEnumerable&lt;IList&lt;User&gt;&gt;.</returns>
	IAsyncEnumerable<IList<User>> GetAllUsersForRoleAsync(string roleId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the user by identifier asynchronous.
	/// </summary>
	/// <param name="userId">The user identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other string?s or threads to receive notice of cancellation.</param>
	/// <returns>IAsyncEnumerable&lt;IList&lt;User&gt;&gt;.</returns>
	Task<User?> GetUserByIdAsync(string userId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the users for org.
	/// </summary>
	/// <param name="orgId">The org identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other string?s or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	Task<IList<OmsUser>> GetUsersForOrgAsync(string orgId, UserSecurityInfo userSecurityInfo, int pageIndex = 0, int pageSize = 100, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets all users for org.
	/// </summary>
	/// <param name="orgId">The org identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other string?s or threads to receive notice of cancellation.</param>
	/// <returns>IAsyncEnumerable&lt;IList&lt;User&gt;&gt;.</returns>
	IAsyncEnumerable<IList<OmsUser>> GetAllUsersForOrgAsync(string orgId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);
}