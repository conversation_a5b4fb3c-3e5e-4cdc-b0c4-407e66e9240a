namespace Apex.Services;

using Apex.Models;
using Apex.Options;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json.Serialization;

/// <summary>
/// IamToken - has other fields, but interested only in the access_token.
/// </summary>
internal sealed record IamToken([property: <PERSON>sonPropertyName("access_token")] string AccessToken, [property: JsonPropertyName("error")] string Error);

/// <summary>
/// Class BaseTokenGenerationService.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="BaseTokenGenerationService" /> class.
/// </remarks>
public abstract partial class BaseTokenGenerationService : ITokenGenerationService
{
	/// <summary>
	/// The options
	/// </summary>
	protected readonly IOptions<IamOptions> _iamOptions;

	/// <summary>
	/// The secure iam options
	/// </summary>
	protected readonly IOptions<SecureIamOptions> _secureIamOptions;

	/// <summary>
	/// The connection
	/// </summary>
	protected readonly IWebConnectionService _connection;

	/// <summary>
	/// The memory cache
	/// </summary>
	protected readonly IMemoryCache _memoryCache;

	/// <summary>
	/// The logger
	/// </summary>
	protected readonly ILogger _logger;

	/// <summary>
	/// The scope
	/// </summary>
	protected readonly string _scope;

	/// <summary>
	/// Initializes a new instance of the <see cref="BaseTokenGenerationService" /> class.
	/// </summary>
	/// <param name="logger">The logger.</param>
	/// <param name="options">The options.</param>
	/// <param name="secureIamOptions">The secure iam options.</param>
	/// <param name="memoryCache">The memory cache.</param>
	/// <param name="connection">The connection.</param>
	/// <param name="scope">The scope.</param>
	protected BaseTokenGenerationService(ILogger<BaseTokenGenerationService> logger, IOptions<IamOptions> options, IOptions<SecureIamOptions> secureIamOptions,
		IMemoryCache memoryCache,
		IWebConnectionService connection,
		string scope)
	{
		_logger = logger;
		_iamOptions = options;
		_secureIamOptions = secureIamOptions;
		_connection = connection;
		_memoryCache = memoryCache;
		_scope = scope;
	}

	/// <summary>
	/// The access token
	/// </summary>
	private string? _cachedPlatformAccessToken;

	/// <summary>
	/// Gets the access token.
	/// </summary>
	/// <value>The access token.</value>
	public string? AccessToken => _cachedPlatformAccessToken;

	/// <summary>
	/// Generate new token always.
	/// </summary>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.String.</returns>
	public async Task<string?> GenerateNewTokenAsync(CancellationToken cancellationToken = default)
	{
		_logger.LogDebug("GenerateNewTokenAsync - token is INVALID, generating a new one - expired/empty");
		var (options, secureOptions) = ValidationIamOptions();
		var payload = new[]
		{
			KeyValuePair.Create("client_id", new[]{ secureOptions.ClientId, options.Client }.Coalesce()!),
			KeyValuePair.Create("client_secret", new[]{ secureOptions.ClientSecret, options.Secret }.Coalesce()!),
			KeyValuePair.Create("grant_type", "client_credentials"),
			KeyValuePair.Create("scope", _scope),
		};

		var accessToken = await GetNewTokenAsync(options, payload!, cancellationToken);
		_logger.LogDebug("GenerateNewTokenAsync - NEW token - TenantId={TenantId} IamUrl={IamUrl} Token={Token}",
			options.TenantId, options.Url, accessToken.Limit());
		return accessToken;
	}

	/// <summary>
	/// Checks the token expiry and generate an impersonation token.
	/// </summary>
	/// <param name="graceTimeForTokenExpireInSeconds">The grace time for token expire in seconds.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.String.</returns>
	public async Task<string?> CheckTokenExpiryAndGenerateTokenAsync(long? graceTimeForTokenExpireInSeconds = null, CancellationToken cancellationToken = default)
	{
		var accessToken = _cachedPlatformAccessToken;
		if (!HasTokenExpired(accessToken, graceTimeForTokenExpireInSeconds))
		{
			_logger.LogDebug("CheckTokenExpiryAndGenerateTokenAsync - token is VALID - Token={Token}", accessToken.Limit());
			return accessToken;
		}

		_logger.LogDebug("CheckTokenExpiryAndGenerateTokenAsync - token is INVALID, generating a new one - expired/empty");
		var (options, secureOptions) = ValidationIamOptions();
		var payload = new[]
		{
			KeyValuePair.Create("client_id", new[]{ secureOptions.ClientId, options.Client }.Coalesce()!),
			KeyValuePair.Create("client_secret", new[]{ secureOptions.ClientSecret, options.Secret }.Coalesce()!),
			KeyValuePair.Create("grant_type", "client_credentials"),
			KeyValuePair.Create("scope", _scope),
		};

		accessToken = await GetNewTokenAsync(options, payload!, cancellationToken);
		_logger.LogDebug("CheckTokenExpiryAndGenerateTokenAsync - NEW token - TenantId={TenantId} IamUrl={IamUrl} Token={Token}",
			options.TenantId, options.Url, accessToken.Limit());
		Interlocked.Exchange(ref _cachedPlatformAccessToken, accessToken);
		return accessToken;
	}

	/// <summary>
	/// Gets the new token.
	/// </summary>
	/// <param name="iamOptions">The iam options.</param>
	/// <param name="payload">The payload.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>string.</returns>
	private Task<string?> GetNewTokenAsync(IamOptions iamOptions, IEnumerable<KeyValuePair<string, string>> payload, CancellationToken cancellationToken = default)
	{
		var iamURL = $"{iamOptions.Url?.TrimEnd('/')}/apexiam/v1/auth/token?tenantId={iamOptions.TenantId}";
		_logger.LogInformation("GetNewTokenAsync - TenantId={TenantId} Url={Url}", iamOptions.TenantId, iamURL);
		return GetNewTokenWithRetriesAsync(iamURL, payload, iamOptions.RetryPolicyClientName, cancellationToken);
	}

	/// <summary>
	/// Determines whether [is token usable] [the specified access token].
	/// </summary>
	/// <param name="accessToken">The access token.</param>
	/// <param name="graceTimeForTokenExpireInSeconds">The grace time for token expire in seconds.</param>
	/// <returns>bool.</returns>
	private bool HasTokenExpired(string? accessToken, long? graceTimeForTokenExpireInSeconds = null)
	{
		var expired = true;
		if (!accessToken.IsNullOrEmpty())
		{
			var handler = new JwtSecurityTokenHandler();
			var decodedToken = handler.ReadJwtToken(accessToken);

			var expiresAt = decodedToken.ValidTo;
			var expiresAtWithGrace = expiresAt.Subtract(TimeSpan.FromSeconds(graceTimeForTokenExpireInSeconds.GetValueOrDefault(_iamOptions.Value.GraceTimeForTokenExpireInSeconds)));
			expired = expiresAtWithGrace < DateTime.UtcNow;
		}
		return expired;
	}

	const int MAX_TOKENRENEW_ATTEMPTS = 50;

	/// <summary>
	/// Get new token from IAM with retries.
	/// </summary>
	/// <param name="requestUrl">The request URL.</param>
	/// <param name="payload">The payload.</param>
	/// <param name="clientName">Name of the client.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;string?&gt; representing the asynchronous operation.</returns>
	/// <exception cref="TokenGenerationException">$"error while getting new token - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	/// <exception cref="TokenGenerationException">$"IAM token generation error - {iamToken.Error}</exception>
	/// <exception cref="TokenGenerationException">IAM token generation error, access token is empty</exception>
	protected async Task<string?> GetNewTokenWithRetriesAsync(string requestUrl, IEnumerable<KeyValuePair<string, string>> payload, string? clientName, CancellationToken cancellationToken = default)
	{
		var attempts = 1;
		string? accessToken = null;

		do
		{
			if (attempts > 1)
			{
				_logger.LogWarning("GetNewTokenWithRetriesAsync - previous attempt of generating a new access token resulted in an empty user id, retrying - Uri={Uri} Attempts={Attempts}",
					requestUrl, attempts);

				// give some delay before trying
				await Task.Delay(500, cancellationToken);
			}
			else
			{
				_logger.LogInformation("GetNewTokenWithRetriesAsync - Uri={Uri} Attempts={Attempts}", requestUrl, attempts);
			}

			accessToken = await InternalGetNewTokenAsync(requestUrl, payload, clientName, cancellationToken);
		}
		while (++attempts <= MAX_TOKENRENEW_ATTEMPTS && accessToken.IsNullOrEmpty());
		return accessToken;
	}

	/// <summary>
	/// Get new token from IAM.
	/// </summary>
	/// <param name="requestUrl">The request URL.</param>
	/// <param name="payload">The payload.</param>
	/// <param name="clientName">Name of the client.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;string?&gt; representing the asynchronous operation.</returns>
	/// <exception cref="TokenGenerationException">$"error while getting new token - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	/// <exception cref="TokenGenerationException">$"IAM token generation error - {iamToken.Error}</exception>
	/// <exception cref="TokenGenerationException">IAM token generation error, access token is empty</exception>
	internal async Task<string?> InternalGetNewTokenAsync(string requestUrl, IEnumerable<KeyValuePair<string, string>> payload, string? clientName, CancellationToken cancellationToken = default)
	{
		var client = _connection.GetClient(clientName);
		var response = await client.PostAsync(requestUrl, new FormUrlEncodedContent(payload), cancellationToken);
		if (!response.IsSuccessStatusCode)
		{
			throw new TokenGenerationException($"error while getting new token - {await response.Content.ReadAsStringAsync(cancellationToken)}");
		}

		var iamToken = await response.Content.ReadFromJsonAsync<IamToken?>(cancellationToken: cancellationToken)
			?? throw new TokenGenerationException($"unable to receive a valid token from IAM - {await response.Content.ReadAsStringAsync(cancellationToken)}");
		if (!iamToken.Error.IsNullOrEmpty())
		{
			throw new TokenGenerationException($"IAM token generation error - {iamToken.Error}");
		}
		if (iamToken.AccessToken.IsNullOrEmpty())
		{
			throw new TokenGenerationException("IAM token generation error, access token is empty");
		}

		return iamToken.AccessToken;
	}

	/// <summary>
	/// Validations the IAM options.
	/// </summary>
	/// <returns>Apex.Options.IamOptions.</returns>
	protected internal (IamOptions, SecureIamOptions) ValidationIamOptions()
	{
		var options = _iamOptions.Value;
		ArgumentNullException.ThrowIfNull(options.TenantId);
		ArgumentNullException.ThrowIfNull(options.GraceTimeForTokenExpireInSeconds);
		ArgumentNullException.ThrowIfNull(options.Url);

		var secureOptions = _secureIamOptions.Value;
		if (secureOptions.ClientId.IsNullOrEmpty() && options.Client.IsNullOrEmpty())
		{
			throw new ArgumentException($"at least one of clientid, SERVICEACCOUNT_NAME has to be specified");
		}
		if (secureOptions.ClientSecret.IsNullOrEmpty() && options.Secret.IsNullOrEmpty())
		{
			throw new ArgumentException($"at least one of secret, SERVICEACCOUNT_PASSWORD has to be specified");
		}
		return (options, secureOptions);
	}

	/// <summary>
	/// Resolves the user id from tenant + application id.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="useCache">The use cache.</param>
	/// <param name="clientName">Name of the client.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;string&gt;.</returns>
	/// <exception cref="IamException">$"unable to resolve application tenant user - TenantId={tenantId} TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public async Task<string> ResolveUserIdFromTenantAppIdAsync(string tenantId, string appId, bool useCache = true, string? clientName = null, CancellationToken cancellationToken = default)
	{
		if (useCache && _memoryCache.TryGetValue((tenantId, appId), out var cachedUserIdValue) && cachedUserIdValue is string cachedUserId)
		{
			_logger.LogInformation("ResolveUserIdFromTenantAppIdAsync - CACHE HIT - TenantId={TenantId} AppId={AppId} UserId={UserId}", tenantId, appId, cachedUserId);
			return cachedUserId;
		}

		var url = $"{_iamOptions.Value.Url?.TrimEnd('/')}/apexiam/v1/tenant/{tenantId}/app/{appId}/service/principal";
		_logger.LogInformation("ResolveUserIdFromTenantAppIdAsync - Url={Url} TenantId={TenantId} AppId={AppId}", url, tenantId, appId);

		// NOTE: iam does not like only for this call to have the tid/app id setup, so DO NOT SET unless expectations change
		var requestMessage = await InternalGetRequestMessageAsync(url, new UserSecurityInfo(null!, null!, null, null), cancellationToken);
		var client = _connection.GetClient(clientName);
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new IamException($"unable to resolve application tenant user - TenantId={tenantId} TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}");
		}

		var userId = ((await responseMessage.Content.ReadFromJsonAsync<ApplicationUserInfo>(SerializationExtensions._SERIALIZEROPTIONS, cancellationToken))?.ServiceId)
			?? throw new IamException($"unable to deserialize application tenant user - TenantId={tenantId} TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}");

		if (useCache)
		{
			_logger.LogInformation("ResolveUserIdFromTenantAppIdAsync - CACHE MISS - TenantId={TenantId} AppId={AppId} UserId={UserId}", tenantId, appId, userId);
			_memoryCache.Set((tenantId, appId), userId, TimeSpan.FromMinutes(_iamOptions.Value.TenantAppUserIdCacheExpiryInMinutes));
		}
		else
		{
			_logger.LogInformation("ResolveUserIdFromTenantAppIdAsync - TenantId={TenantId} AppId={AppId} UserId={UserId})", tenantId, appId, userId);
		}

		return userId!;
	}

	/// <summary>
	/// Get application information.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="clientName">Name of the client.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;Apex.Services.ApplicationInfo&gt; representing the asynchronous operation.</returns>
	/// <exception cref="IamException">$"unable to get application info - TargetUrl={url} TenantId={tenantId} AppId={appId} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	/// <exception cref="IamException">$"unable to resolve application info from response - TargetUrl={url} TenantId={tenantId} AppId={appId} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public async Task<ApplicationInfo> GetApplicationInfoAsync(string tenantId, string appId, UserSecurityInfo userSecurityInfo, string? clientName = null, CancellationToken cancellationToken = default)
	{
		ArgumentException.ThrowIfNullOrEmpty(tenantId);
		ArgumentException.ThrowIfNullOrEmpty(appId);
		ArgumentNullException.ThrowIfNull(userSecurityInfo);

		var iamOptions = _iamOptions.Value;

		//curl--location 'https://dev-workflow.cluster.jiffy.ai/apexiam/v1/app/a5d95c24-46ac-4d13-b808-66b681c1d2f5' \
		var url = $"{iamOptions.Url?.TrimEnd('/')}/apexiam/v1/app/{appId}";
		_logger.LogInformation("GetApplicationInfoAsync - Url={Url} TenantId={TenantId} AppId={AppId}", url, tenantId, appId);

		// NOTE: iam does not like only for this call to have the tid/app id setup, so DO NOT SET unless expectations change
		var requestMessage = await InternalGetRequestMessageAsync(url, userSecurityInfo, cancellationToken);
		requestMessage.Method = HttpMethod.Get;

		var client = _connection.GetClient(iamOptions.RetryPolicyClientName);
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new IamException($"unable to get application info - TargetUrl={url} TenantId={tenantId} AppId={appId} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}");
		}

		var applicationInfo = await responseMessage.Content.ReadFromJsonAsync<ApplicationInfo>(SerializationExtensions._SERIALIZEROPTIONS, cancellationToken)
			?? throw new IamException($"unable to resolve application info from response - TargetUrl={url} TenantId={tenantId} AppId={appId} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}");
		_logger.LogInformation("GetApplicationInfoAsync - TenantId={TenantId} AppId={AppId} ApplicationInfo={ApplicationInfo})", tenantId, appId, applicationInfo.ToJSON());
		return applicationInfo;
	}

	/// <summary>
	/// Gets the request message.
	/// </summary>
	/// <param name="requestUrl">The template URI.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>HttpRequestMessage.</returns>
	public Task<HttpRequestMessage> GetRequestMessageAsync(string requestUrl, UserSecurityInfo? userInfo, CancellationToken cancellationToken)
		=> InternalGetRequestMessageAsync(requestUrl, userInfo, cancellationToken);

	/// <summary>
	/// Helper method to get request message.
	/// </summary>
	/// <param name="requestUrl">The template URI.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>HttpRequestMessage.</returns>
	public async Task<HttpRequestMessage> InternalGetRequestMessageAsync(string requestUrl, UserSecurityInfo? userInfo, CancellationToken cancellationToken)
	{
		var requestMessage = new HttpRequestMessage
		{
			RequestUri = new Uri(requestUrl),
			Method = HttpMethod.Get,
		};

		var targetHeaders = requestMessage.Headers;
		userInfo?.UpdateWebHeaders(targetHeaders);

		var newAccessToken = await CheckTokenExpiryAndGenerateTokenAsync(cancellationToken: cancellationToken);
		if (newAccessToken is not null)
		{
			targetHeaders.Authorization = new AuthenticationHeaderValue("Bearer", newAccessToken);
		}

		return requestMessage;
	}
}