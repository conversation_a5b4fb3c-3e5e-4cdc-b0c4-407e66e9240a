namespace Apex.Services;

/// <summary>
/// Class AppManagerService. This class cannot be inherited.
/// </summary>
public sealed partial class AppManagerService
{
	/// <summary>
	/// Get tenants.
	/// </summary>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IEnumerable`1&gt; representing the asynchronous operation.</returns>
	/// <exception cref="TokenGenerationException">$"error while getting tenants list - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public async Task<IEnumerable<TenantDefinition>> GetTenantsAsync(CancellationToken cancellationToken = default)
	{
		var client = _connection.GetClient();
		var tenants = new List<TenantDefinition>();
		bool hasMoreRecords;
		var offset = 0;
		do
		{
			var requestUrl = $"{_serviceOptions.Value.PamUrl?.TrimEnd('/')}/tenant/list?offset={offset}&limit={QUERY_PAGESIZE}";
			_logger.LogInformation("GetTenantsAsync - Url={Url}", requestUrl);

			var request = await _tokenGenerationService.GetRequestMessageAsync(requestUrl, null, cancellationToken);
			var response = await client.SendAsync(request, cancellationToken);
			if (!response.IsSuccessStatusCode)
			{
				throw new AppManagerException($"error while getting tenants list - {await response.Content.ReadAsStringAsync(cancellationToken)}");
			}

			var tempTenants = await response.Content.ReadFromJsonAsync<TenantDefinition[]>(SerializationExtensions._SERIALIZEROPTIONS, cancellationToken: cancellationToken);
			if (tempTenants?.Length > 0)
			{
				tenants.AddRange(tempTenants!);
			}

			hasMoreRecords = tempTenants?.Length >= QUERY_PAGESIZE;
			offset += QUERY_PAGESIZE;
		}
		while (hasMoreRecords);
		return tenants;
	}

	/// <summary>
	/// Get apps.
	/// </summary>
	/// <param name="tenantName">Name of the tenant.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IEnumerable`1&gt; representing the asynchronous operation.</returns>
	/// <exception cref="TokenGenerationException">$"error while getting tenants list - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public async Task<IEnumerable<ApplicationDefinition>> GetAppsAsync(string tenantName, CancellationToken cancellationToken = default)
	{
		var client = _connection.GetClient();
		var apps = new List<ApplicationDefinition>();
		bool hasMoreRecords;
		var offset = 0;
		do
		{
			var requestUrl = $"{_serviceOptions.Value.PamUrl?.TrimEnd('/')}/tenant/{tenantName}/app/list?inst=true&offset={offset}&limit={QUERY_PAGESIZE}";
			_logger.LogInformation("GetAppsAsync - Url={Url}", requestUrl);

			var request = await _tokenGenerationService.GetRequestMessageAsync(requestUrl, null, cancellationToken);
			var response = await client.SendAsync(request, cancellationToken);
			if (!response.IsSuccessStatusCode)
			{
				throw new AppManagerException($"error while getting tenant applications list - {await response.Content.ReadAsStringAsync(cancellationToken)}");
			}

			var tempApps = await response.Content.ReadFromJsonAsync<ApplicationDefinition[]>(SerializationExtensions._SERIALIZEROPTIONS, cancellationToken: cancellationToken);
			if (tempApps?.Length > 0)
			{
				apps.AddRange(tempApps!);
			}

			hasMoreRecords = tempApps?.Length > 0;
			offset += apps.Count + 1;
		}
		while (hasMoreRecords);
		return apps;
	}

	/// <summary>
	/// Get apps.
	/// </summary>
	/// <param name="tenantName">Name of the tenant.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IEnumerable`1&gt; representing the asynchronous operation.</returns>
	/// <exception cref="TokenGenerationException">$"error while getting tenants list - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public async Task<ApplicationDefinition[]> GetAllAppsAsync(string tenantName, CancellationToken cancellationToken = default)
	{
		var client = _connection.GetClient();
		var requestUrl = $"{_serviceOptions.Value.PamUrl?.TrimEnd('/')}/tenant/{tenantName}/app/list?offset=0&limit=-1&inst=true&fetchAll=true";
		_logger.LogInformation("GetAllAppsAsync - Url={Url}", requestUrl);

		var request = await _tokenGenerationService.GetRequestMessageAsync(requestUrl, null, cancellationToken);
		var response = await client.SendAsync(request, cancellationToken);
		if (!response.IsSuccessStatusCode)
		{
			throw new AppManagerException($"error while getting tenant applications list - {await response.Content.ReadAsStringAsync(cancellationToken)}");
		}

		return await response.Content!.ReadFromJsonAsync<ApplicationDefinition[]>(SerializationExtensions._SERIALIZEROPTIONS, cancellationToken: cancellationToken);
	}

	/// <summary>
	/// Get apps.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IEnumerable`1&gt; representing the asynchronous operation.</returns>
	/// <exception cref="TokenGenerationException">$"error while getting tenants list - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public async Task<ApplicationInstanceInfo?> GetAppInstanceInfoAsync(string tenantId, string appId, CancellationToken cancellationToken = default)
	{
		var client = _connection.GetClient();
		var requestUrl = $"{_serviceOptions.Value.PamUrl?.TrimEnd('/')}/tenant/{tenantId}/inst/{appId}";
		_logger.LogInformation("GetAppInstanceInfoAsync - Url={Url}", requestUrl);

		var request = await _tokenGenerationService.GetRequestMessageAsync(requestUrl, null, cancellationToken);
		var response = await client.SendAsync(request, cancellationToken);
		if (!response.IsSuccessStatusCode)
		{
			throw new AppManagerException($"error while getting applications info - {await response.Content.ReadAsStringAsync(cancellationToken)}");
		}

		return await response.Content.ReadFromJsonAsync<ApplicationInstanceInfo>(SerializationExtensions._SERIALIZEROPTIONS, cancellationToken: cancellationToken);
	}

	/// <summary>
	/// The query pagesize
	/// </summary>
	const int QUERY_PAGESIZE = 100;
}