using System.Text.Json;

namespace Apex.Services;

/// <summary>
/// Class IDataService.
/// </summary>
public partial interface IDataService
{
#pragma warning disable S107
	/// <summary>
	/// Imports the BO using the provided CSV file.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="endPointName">End name of the point.</param>
	/// <param name="csvJiffyDrivePath">The CSV jiffy drive path.</param>
	/// <param name="runId">The run identifier.</param>
	/// <param name="clientName">Name of the client.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	Task ImportBoCsvAsync(string tenantId, string appId, string endPointName, string csvJiffyDrivePath, string runId,
		string? clientName = null, CancellationToken cancellationToken = default);

	/// <summary>
	/// Imports the BO relationship using the provided CSV.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="sourceEndPointName">Name of the source end point.</param>
	/// <param name="targetEndPointName">Name of the target end point.</param>
	/// <param name="csvJiffyDrivePath">The CSV jiffy drive path.</param>
	/// <param name="runId">The run identifier.</param>
	/// <param name="clientName">Name of the client.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	Task ImportBoRelationshipCsvAsync(string tenantId, string appId, string sourceEndPointName, string targetEndPointName,
		string csvJiffyDrivePath, string runId, string? clientName = null, CancellationToken cancellationToken = default);

	/// <summary>
	/// Exports the BO records to the provided CSV file.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="endPointName">End name of the point.</param>
	/// <param name="csvJiffyDrivePath">The CSV jiffy drive path.</param>
	/// <param name="parameters">The parameters.</param>
	/// <param name="clientName">Name of the client.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	Task ExportBoCsvAsync(string tenantId, string appId, string endPointName, string csvJiffyDrivePath, Dictionary<string, string?>? parameters = null,
		string? clientName = null, CancellationToken cancellationToken = default);
#pragma warning restore S107

	/// <summary>
	/// Gets the records.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="endPointName">End name of the point.</param>
	/// <param name="fields">The fields.</param>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;T&gt;&gt;.</returns>
	Task<IList<T>> GetRecordsAsync<T>(string tenantId, string appId, string endPointName, string[] fields, int pageIndex = 0, int pageSize = 100, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the records using dynamic query.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="endPointName">End name of the point.</param>
	/// <param name="dynamicQuery">The dynamic query.</param>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;T&gt;&gt;.</returns>
	Task<IList<T>> GetRecordsUsingDynamicQueryAsync<T>(string tenantId, string appId, string endPointName, JsonDocument dynamicQuery, int pageIndex = 0, int pageSize = 100, CancellationToken cancellationToken = default);

	/// <summary>
	/// Inserts the object.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="endPointName">End name of the point.</param>
	/// <param name="instance">The instance.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;T&gt;&gt;.</returns>
	Task<T?> InsertObjectAsync<T>(string tenantId, string appId, string endPointName, T instance, CancellationToken cancellationToken = default)
		where T : class;

	/// <summary>
	/// Updates the object.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="endPointName">End name of the point.</param>
	/// <param name="id">The identifier.</param>
	/// <param name="instance">The instance.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;T&gt;&gt;.</returns>
	Task<T?> UpdateObjectAsync<T>(string tenantId, string appId, string endPointName, string id, T instance, CancellationToken cancellationToken = default)
		where T : class;

	/// <summary>
	/// Deletes the objects.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="endPointName">End name of the point.</param>
	/// <param name="ids">The ids.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	Task DeleteObjectsAsync(string tenantId, string appId, string endPointName, ICollection<string> ids, CancellationToken cancellationToken = default);
}