namespace Apex.Services;

/// <summary>
/// Class WebConnectionService. This class cannot be inherited.
/// Implements the <see cref="Apex.Services.IWebConnectionService" />
/// </summary>
/// <seealso cref="Apex.Services.IWebConnectionService" />
/// <param name="clientFactory">The client factory.</param>
/// <param name="_defaultClientName">The default client name.</param>
/// <remarks>Initializes a new instance of the <see cref="WebConnectionService" /> class.</remarks>
public sealed partial class WebConnectionService(IHttpClientFactory clientFactory, string? _defaultClientName = null) : IWebConnectionService
{
	/// <summary>
	/// The client factory
	/// </summary>
	private readonly IHttpClientFactory _clientFactory = clientFactory;

	/// <summary>
	/// Gets the client.
	/// </summary>
	/// <param name="clientName">Type of the client.</param>
	/// <returns>System.Net.Http.HttpClient.</returns>
	/// <value>The client.</value>
	public HttpClient GetClient(string? clientName = null) => _clientFactory.CreateClient(clientName ?? _defaultClientName ?? "default");
}