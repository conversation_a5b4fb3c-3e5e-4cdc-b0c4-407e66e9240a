namespace Apex.Services;

using Apex.Models;
using System.Text.Json;

/// <summary>
/// Class QueueMessage. This class cannot be inherited.
/// </summary>
public sealed partial class QueueMessage
{
	/// <summary>
	/// Gets or sets the topic.
	/// </summary>
	/// <value>The topic.</value>
	public string Topic { get; init; }

	/// <summary>
	/// Gets or sets a value indicating whether this instance is cross tenant.
	/// </summary>
	/// <value><c>true</c> if this instance is cross tenant; otherwise, <c>false</c>.</value>
	public bool IsCrossTenant { get; set; }

	/// <summary>
	/// Gets or sets the payload.
	/// </summary>
	/// <value>The payload.</value>
	public JsonDocument? Payload { get; init; }
}

/// <summary>
/// Interface IMessengerRestService
/// </summary>
public partial interface IMessengerRestService
{
	/// <summary>
	/// Posts the non durable queue message.
	/// </summary>
	/// <param name="messageInfo">The message information.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	Task<string?> PostNonDurableQueueMessageAsync(QueueMessage messageInfo, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default);
}