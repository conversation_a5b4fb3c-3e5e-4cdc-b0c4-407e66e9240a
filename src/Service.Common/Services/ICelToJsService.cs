using Apex.Models;
using System.Text.Json.Serialization;

namespace Apex.Services;

/// <summary>
/// Interface ICelToJsService
/// </summary>
public partial interface ICelToJsService
{
	/// <summary>
	/// Cel to js.
	/// </summary>
	/// <param name="expression">The expression.</param>
	/// <param name="userSecurityInfo"></param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	Task<BundledExpressionOutput> CelToJsAsync(string expression, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken);

	/// <summary>
	/// Cel to js multiline.
	/// </summary>
	/// <param name="expression">The expression.</param>
	/// <param name="userSecurityInfo"></param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	Task<BundledExpressionOutput> CelToJsMultiAsync(string expression, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken);
}

/// <summary>
/// The expression translated output class
/// </summary>
public sealed class ExpressionTranslatedOutput
{
	/// <summary>
	/// Gets or sets the workflow identifier.
	/// </summary>
	/// <value>The workflow identifier.</value>
	[JsonPropertyName("jsCode")]
	public string? JsCode { get; set; }

	/// <summary>
	/// Gets or sets the result.
	/// </summary>
	/// <value>The result.</value>
	[JsonPropertyName("inputVariables")]
	public List<string?>? InputVariables { get; set; }

	/// <summary>
	/// Gets or sets the result.
	/// </summary>
	/// <value>The result.</value>
	[JsonPropertyName("masterDataRefs")]
	public List<string?>? MasterDataRefs { get; set; }

	/// <summary>
	/// Gets or sets the result.
	/// </summary>
	/// <value>The result.</value>
	[JsonPropertyName("allVariables")]
	public List<string?>? AllVariables { get; set; }

	/// <summary>
	/// Gets or sets the result.
	/// </summary>
	/// <value>The result.</value>
	[JsonPropertyName("functions")]
	public List<string?>? Functions { get; set; }
}

/// <summary>
/// The expression output class
/// </summary>
public sealed class BundledExpressionOutput
{
	/// <summary>
	/// Gets or sets the value of the js
	/// </summary>
	public string? Js { get; set; }

	/// <summary>
	/// Gets or sets the value of the input variables
	/// </summary>
	public List<string?>? InputVariables { get; set; }
}