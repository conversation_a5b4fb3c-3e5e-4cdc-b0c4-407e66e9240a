namespace Apex.Services;

using Apex.Options;
using Microsoft.Extensions.Options;

/// <summary>
/// Class AppManagerService.
/// </summary>
/// <remarks>
/// Initializes a new instance of the <see cref="AppManagerService" /> class.
/// </remarks>
/// <param name="_logger">The logger.</param>
/// <param name="_serviceOptions">The service options.</param>
/// <param name="_tokenGenerationService">The token generation service.</param>
/// <param name="_connection">The connection.</param>
public sealed partial class AppManagerService(ILogger<AppManagerService> _logger,
	IOptions<ServiceOptions> _serviceOptions,
	ITokenGenerationService _tokenGenerationService,
	IWebConnectionService _connection) : IAppManagerService
{
}