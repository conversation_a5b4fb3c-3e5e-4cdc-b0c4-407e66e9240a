namespace Apex.Services;

/// <summary>
/// Class ApplicationUserInfo. This class cannot be inherited.
/// </summary>
public sealed class ApplicationUserInfo
{
	/// <summary>
	/// Gets or sets the service identifier.
	/// </summary>
	/// <value>The service identifier.</value>
	public string? ServiceId { get; set; }

	/// <summary>
	/// Gets or sets the name of the application.
	/// </summary>
	/// <value>The name of the application.</value>
	public string? ApplicationName { get; set; }

	/// <summary>
	/// Gets or sets the name of the service.
	/// </summary>
	/// <value>The name of the service.</value>
	public string? ServiceName { get; set; }

	/// <summary>
	/// Gets or sets the service secret.
	/// </summary>
	/// <value>The service secret.</value>
	public string? ServiceSecret { get; set; }

	/// <summary>
	/// Gets or sets a value indicating whether [enable impersonation].
	/// </summary>
	/// <value><c>true</c> if [enable impersonation]; otherwise, <c>false</c>.</value>
	public bool EnableImpersonation { get; set; }
}