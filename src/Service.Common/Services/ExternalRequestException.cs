using System.Runtime.Serialization;
namespace Apex.Services;

/// <summary>
/// Class ExternalRequestException.
/// Implements the <see cref="System.Exception" />
/// </summary>
/// <seealso cref="System.Exception" />
[Serializable]
public sealed partial class ExternalRequestException : Exception
{
	/// <summary>
	/// Initializes a new instance of the <see cref="ExternalRequestException" /> class.
	/// </summary>
	/// <param name="errorMessage">The error message.</param>
	/// <returns>ExternalRequestException.</returns>
	public ExternalRequestException(string errorMessage)
		: base(errorMessage)
	{
	}

	/// <summary>
	/// Initializes a new instance of the <see cref="ExternalRequestException" /> class.
	/// </summary>
	/// <param name="serializationInfo">The serialization information.</param>
	/// <param name="streamingContext">The streaming context.</param>
	private ExternalRequestException(SerializationInfo serializationInfo, StreamingContext streamingContext)
		: base(serializationInfo, streamingContext)
	{
	}
}