namespace Apex.Services;

using Apex.Models;
using System.Text.Json;

/// <summary>
/// Interface ITemplateProcessorRestService
/// </summary>
public partial interface ITemplateProcessorRestService
{
	/// <summary>
	/// Transforms the model using the specified template.
	/// </summary>
	/// <param name="transformString">The transform string.</param>
	/// <param name="model">The modDsel.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	Task<string?> TransformModelAsync(string? transformString, JsonDocument? model, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Bulk transforms the model using the specified template.
	/// </summary>
	/// <param name="transformStrings">The transform strings.</param>
	/// <param name="model">The model.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	Task<IEnumerable<string?>> BulkTransformModelAsync(string?[] transformStrings, JsonDocument? model, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default);
}