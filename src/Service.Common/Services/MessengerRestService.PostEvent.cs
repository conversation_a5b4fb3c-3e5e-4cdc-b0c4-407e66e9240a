namespace Apex.Services;

using Apex.Models;
using System;
using System.Net.Http.Headers;

/// <summary>
/// Class MessengerRestService. This class cannot be inherited.
/// Implements the <see cref="Apex.Services.IMessengerRestService" />
/// </summary>
/// <seealso cref="Apex.Services.IMessengerRestService" />
public sealed partial class MessengerRestService
{
	/// <summary>
	/// Force unsubscribes from the specified topic.
	/// </summary>
	/// <param name="topic">The topic.</param>
	/// <param name="identifier">The identifier.</param>
	/// <param name="isCrossTenant">Post message into a cross tenant stream.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	public async Task<string?> UnsubscribeAsync(string topic, string identifier, bool isCrossTenant, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default)
	{
		var url = $"{_serviceOptions.Value.MessengerUrl?.TrimEnd('/')}/v1/durable/unsubscribe/{topic}/identifier/{identifier}?isCrossTenant={(isCrossTenant ? "true" : "false")}";
		var requestMessage = await InternalGetRequestMessageAsync(url, userInfo, cancellationToken);
		requestMessage.Method = HttpMethod.Put;

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		var responseString = await responseMessage.Content.ReadAsStringAsync(cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new MessengerException($"unable to unsubscribe from messenger - TargetUrl={url} Status={responseMessage.StatusCode} Error={responseString}");
		}

		return responseString;
	}

	/// <summary>
	/// Posts the event.
	/// </summary>
	/// <param name="topic">The topic.</param>
	/// <param name="eventInfo">The event information.</param>
	/// <param name="isCrossTenant">Post message into a cross tenant stream.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	public Task<string?> PostEventAsync(string? topic, Event? eventInfo, bool isCrossTenant, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(topic);
		ArgumentNullException.ThrowIfNull(eventInfo);

		return InternalPostEventAsync(topic, eventInfo, isCrossTenant, userInfo, cancellationToken);
	}

	/// <summary>
	/// Post event helper.
	/// </summary>
	/// <param name="topic">The topic.</param>
	/// <param name="eventInfo">The event information.</param>
	/// <param name="isCrossTenant">Post message into a cross tenant stream.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	/// <exception cref="Apex.Services.MessengerException">unable to send a message to messenger - TargetUrl={url} Status={responseMessage.StatusCode} Error={responseString}</exception>
	public async Task<string?> InternalPostEventAsync(string topic, Event eventInfo, bool isCrossTenant, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default)
	{
		// header does not exist already
		if (eventInfo.AuthInfo == null && userInfo is not null)
		{
			eventInfo.AuthInfo = new EventAuthInfo
			{
				TenantId = userInfo.TenantId,
				AppId = userInfo.AppId,
				UserId = userInfo.UserId,
			};
		}

		// init header with hops to avoid spurious exceptions
		eventInfo.Header ??= new EventHeader
		{
			Hops = 1,
		};

		var url = $"{_serviceOptions.Value.MessengerUrl?.TrimEnd('/')}/v1/durable/{topic}?isCrossTenant={(isCrossTenant ? "true" : "false")}";
		var requestMessage = await InternalGetRequestMessageAsync(url, userInfo, cancellationToken);
		requestMessage.Method = HttpMethod.Put;
		requestMessage.Content = JsonContent.Create(eventInfo);

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		var responseString = await responseMessage.Content.ReadAsStringAsync(cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new MessengerException($"unable to send a message to messenger - TargetUrl={url} Status={responseMessage.StatusCode} Error={responseString}");
		}

		return responseString;
	}

	/// <summary>
	/// Helper method to get request message.
	/// </summary>
	/// <param name="requestUrl">The template URI.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>HttpRequestMessage.</returns>
	private async Task<HttpRequestMessage> InternalGetRequestMessageAsync(string requestUrl, UserSecurityInfo? userInfo, CancellationToken cancellationToken)
	{
		var requestMessage = new HttpRequestMessage
		{
			RequestUri = new Uri(requestUrl),
			Method = HttpMethod.Get,
		};

		var targetHeaders = requestMessage.Headers;
		userInfo?.UpdateWebHeaders(targetHeaders);

		var newAccessToken = await _tokenGeneration.CheckTokenExpiryAndGenerateTokenAsync(cancellationToken: cancellationToken);
		if (newAccessToken is not null)
		{
			targetHeaders.Authorization = new AuthenticationHeaderValue("Bearer", newAccessToken);
		}

		return requestMessage;
	}
}