namespace Apex.Services;

using Apex.Options;
using Microsoft.Extensions.Options;
using System;
using System.Text.Json.Serialization;

/// <summary>
/// Class TokenRequest. This class cannot be inherited.
/// </summary>
internal sealed class TokenRequest
{
	/// <summary>
	/// Gets or sets the role identifier.
	/// </summary>
	/// <value>The role identifier.</value>
	[JsonPropertyName("role")]
	public string Role { get; set; }

	/// <summary>
	/// Gets or sets the secret identifier.
	/// </summary>
	/// <value>The secret identifier.</value>
	[JsonPropertyName("jwt")]
	public string Jwt { get; set; }
}

/// <summary>
/// Class VaultTokenGenerationService.
/// </summary>
public sealed partial class VaultTokenGenerationService : IVaultTokenGenerationService
{
	/// <summary>
	/// The vault options
	/// </summary>
	private readonly IOptions<VaultOptions> _vaultOptions;

	/// <summary>
	/// The secure vault options
	/// </summary>
	private readonly IOptions<SecureVaultOptions> _secureVaultOptions;

	/// <summary>
	/// The service options
	/// </summary>
	private readonly IOptions<ServiceOptions> _serviceOptions;

	/// <summary>
	/// The connection
	/// </summary>
	private readonly IWebConnectionService _connection;

	/// <summary>
	/// The logger
	/// </summary>
	private readonly ILogger _logger;

	/// <summary>
	/// Initializes a new instance of the <see cref="VaultTokenGenerationService" /> class.
	/// </summary>
	/// <param name="logger">The logger.</param>
	/// <param name="vaultOptions">The vault options.</param>
	/// <param name="secureVaultOptions">The vault options.</param>
	/// <param name="serviceOptions">The service options.</param>
	/// <param name="connection">The connection.</param>
	public VaultTokenGenerationService(ILogger<VaultTokenGenerationService> logger,
		IOptions<VaultOptions> vaultOptions,
		IOptions<SecureVaultOptions> secureVaultOptions,
		IOptions<ServiceOptions> serviceOptions,
		IWebConnectionService connection)
	{
		_logger = logger;
		_vaultOptions = vaultOptions;
		_secureVaultOptions = secureVaultOptions;
		_serviceOptions = serviceOptions;
		_connection = connection;
	}

	/// <summary>
	/// The access token
	/// </summary>
	private VaultAuthenticationToken? _cachedAccessToken;

	/// <summary>
	/// Gets the access token.
	/// </summary>
	/// <value>The access token.</value>
	public string? AccessToken => _cachedAccessToken?.ClientToken;

	/// <summary>
	/// Checks the token expiry and generate an impersonation token.
	/// </summary>
	/// <param name="graceTimeForTokenExpireInSeconds">The grace time for token expire in seconds.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.String.</returns>
	public async Task<string?> CheckTokenExpiryAndGenerateTokenAsync(long? graceTimeForTokenExpireInSeconds = null, CancellationToken cancellationToken = default)
	{
		var accessToken = _cachedAccessToken;
		if (!HasTokenExpired(accessToken, graceTimeForTokenExpireInSeconds))
		{
			var clientToken = accessToken?.ClientToken;
			_logger.LogDebug("CheckTokenExpiryAndGenerateTokenAsync - token is VALID - Token={Token}", clientToken.Limit());
			return clientToken;
		}

		_logger.LogDebug("CheckTokenExpiryAndGenerateTokenAsync - token is INVALID, generating a new one - expired/empty");
		accessToken = await GetNewTokenAsync(cancellationToken);
		Interlocked.Exchange(ref _cachedAccessToken, accessToken);
		return accessToken?.ClientToken;
	}

	/// <summary>
	/// Gets the new token.
	/// </summary>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>string.</returns>
	private async Task<VaultAuthenticationToken?> GetNewTokenAsync(CancellationToken cancellationToken = default)
	{
		ValidationOptions();

		var role = _secureVaultOptions.Value.RoleId!;
		var jwt = await File.ReadAllTextAsync(_vaultOptions.Value.VaultSecretPath!, cancellationToken);

		var vaultLoginUrl = $"{_serviceOptions.Value.VaultUrl!.TrimEnd('/')}/v1/auth/kubernetes/login";
		_logger.LogInformation("GetNewTokenAsync - LoginUrl={LoginUrl} Role={Role} Jwt={Jwt}...",
			vaultLoginUrl, role, jwt.Limit());

		var payload = new TokenRequest
		{
			Role = role,
			Jwt = jwt!
		};
		var tokenResponse = await GetNewTokenAsync<TokenRequest, VaultAuthenticationTokenResponse>(vaultLoginUrl, payload, cancellationToken)
			?? throw new TokenGenerationException("Unable to generate a valid vault token");
		var token = tokenResponse.Token;
		token.ExpiresAt = DateTime.UtcNow.AddSeconds(token.LeaseDuration);
		_logger.LogDebug("GetNewTokenAsync - NEW token - Token={Token}", token.ClientToken?.Limit());
		return token;
	}

	/// <summary>
	/// Get new token.
	/// </summary>
	/// <typeparam name="TIn">The type of the t in.</typeparam>
	/// <typeparam name="TOut">The type of the t out.</typeparam>
	/// <param name="requestUrl">The request URL.</param>
	/// <param name="payload">The payload.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;string?&gt; representing the asynchronous operation.</returns>
	/// <exception cref="TokenGenerationException">$"error while getting new token from vault - {await response.Content.ReadAsStringAsync(cancellationToken)}</exception>
	private async Task<TOut?> GetNewTokenAsync<TIn, TOut>(string requestUrl, TIn? payload, CancellationToken cancellationToken = default)
	{
		var client = _connection.GetClient();
		var response = await client.PostAsync(requestUrl, JsonContent.Create(payload, options: SerializationExtensions._SERIALIZEROPTIONS), cancellationToken);
		if (!response.IsSuccessStatusCode)
		{
			throw new TokenGenerationException($"error while getting new token from vault - {await response.Content.ReadAsStringAsync(cancellationToken)}");
		}

		return await response.Content.ReadFromJsonAsync<TOut>(options: SerializationExtensions._SERIALIZEROPTIONS, cancellationToken: cancellationToken);
	}

	/// <summary>
	/// Determines whether [is token usable] [the specified access token].
	/// </summary>
	/// <param name="accessToken">The access token.</param>
	/// <param name="graceTimeForTokenExpireInSeconds">The grace time for token expire in seconds.</param>
	/// <returns>bool.</returns>
	private bool HasTokenExpired(VaultAuthenticationToken? accessToken, long? graceTimeForTokenExpireInSeconds = null)
	{
		var expired = true;
		if (accessToken is not null)
		{
			var expiresAtWithGrace = accessToken.ExpiresAt.Subtract(TimeSpan.FromSeconds(graceTimeForTokenExpireInSeconds.GetValueOrDefault(_vaultOptions.Value.GraceTimeForTokenExpireInSeconds)));
			expired = expiresAtWithGrace < DateTime.UtcNow;
		}
		return expired;
	}

	/// <summary>
	/// Validates the options.
	/// </summary>
	private void ValidationOptions()
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.VaultUrl);
		ArgumentNullException.ThrowIfNull(_vaultOptions.Value.VaultSecretPath);
		ArgumentNullException.ThrowIfNull(_secureVaultOptions.Value.RoleId);
	}
}