namespace Apex.Services;

using Apex.Models;
using System.Text.Json;
using System.Text.Json.Serialization;

/// <summary>
/// Class WorkflowInput.
/// </summary>
public sealed class WorkflowInput
{
	/// <summary>
	/// Gets or sets a value indicating whether [execute synchronize].
	/// </summary>
	/// <value><c>true</c> if [execute synchronize]; otherwise, <c>false</c>.</value>
	[JsonPropertyName("executeSync")]
	public bool ExecuteSync { get; set; }

	/// <summary>
	/// Gets or sets the arguments.
	/// </summary>
	/// <value>The arguments.</value>
	[JsonPropertyName("arguments")]
	public JsonDocument? Arguments { get; set; }

	/// <summary>
	/// Gets or sets the workflow identifier.
	/// </summary>
	/// <value>The workflow identifier.</value>
	[JsonPropertyName("workflowId")]
	public string? WorkflowId { get; set; }
}

/// <summary>
/// Class WorkflowOutput. This class cannot be inherited.
/// </summary>
public sealed class WorkflowOutput
{
	/// <summary>
	/// Gets or sets the workflow identifier.
	/// </summary>
	/// <value>The workflow identifier.</value>
	[JsonPropertyName("workflowId")]
	public string? WorkflowId { get; set; }

	/// <summary>
	/// Gets or sets the result.
	/// </summary>
	/// <value>The result.</value>
	[JsonPropertyName("result")]
	public JsonDocument? Result { get; set; }
}

/// <summary>
/// Interface IWorkflowRestService
/// </summary>
public partial interface IWorkflowRestService
{
	/// <summary>
	/// Invokes the workflow.
	/// </summary>
	/// <param name="workflowPath">The workflow path.</param>
	/// <param name="input">The input.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	Task<WorkflowOutput> InvokeWorkflowAsync(string workflowPath, WorkflowInput input, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Signals the workflow instance - success or failure
	/// if success, a model
	/// if failed, an optional error message.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="workflowInstanceId">The workflow instance identifier.</param>
	/// <param name="signalId">The signal identifier.</param>
	/// <param name="model">The model.</param>
	/// <param name="hasError">if set to <c>true</c> [has error].</param>
	/// <param name="errorMessage">The error message.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;WorkflowOutput&gt;.</returns>
	Task SignalWorkflowAsync<T>(string workflowInstanceId, string signalId, T? model, bool hasError, string? errorMessage, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default);
}