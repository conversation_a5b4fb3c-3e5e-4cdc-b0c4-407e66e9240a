namespace Apex.Services;

using Apex.Models;
using System;
using System.Net.Http.Headers;

/// <summary>
/// Class ListFolderResponse. This class cannot be inherited.
/// </summary>
internal sealed partial class ListFolderResponse
{
	/// <summary>
	/// Gets or sets the contents.
	/// </summary>
	/// <value>The contents.</value>
	public ListFolderContent[]? Contents { get; set; }
}

/// <summary>
/// Class Content. This class cannot be inherited.
/// </summary>
internal sealed partial class ListFolderContent
{
	/// <summary>
	/// Gets or sets the content identifier.
	/// </summary>
	/// <value>The content identifier.</value>
	public string? ContentId { get; set; }
}

/// <summary>
/// Class JiffyDriveRestService. This class cannot be inherited.
/// </summary>
public sealed partial class JiffyDriveRestService
{
	/// <summary>
	/// Lists the folder.
	/// </summary>
	/// <param name="bucketAndFolderPath">The bucket and folder path.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	/// <exception cref="JiffyDriveException">$"unable to list the folder from Jiffy Drive - Template={templateUri} Status={responseMessage.StatusCode} DriveError={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	/// <exception cref="System.ArgumentNullException">$"unable to list the folder from Jiffy Drive - Template={templateUri} Status={responseMessage.StatusCode} DriveError={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	/// <exception cref="Apex.Services.JiffyDriveException"></exception>
	async Task<IEnumerable<string>> IJiffyDriveRestService.ListFolderAsync(string bucketAndFolderPath, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		ArgumentNullException.ThrowIfNull(bucketAndFolderPath);

		var templateUri = ToJiffyDriveObjectsUrl(bucketAndFolderPath, "buckets");
		var requestMessage = await InternalGetRequestMessage(templateUri, userInfo, cancellationToken);
		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new JiffyDriveException($"unable to list the folder from Jiffy Drive - Template={templateUri} Status={responseMessage.StatusCode} DriveError={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}");
		}

		var list = await responseMessage.Content.ReadFromJsonAsync<ListFolderResponse>(SerializationExtensions._SERIALIZEROPTIONS, cancellationToken);
		return (list?.Contents?.Select(c => c.ContentId!).Where(c => c is not null).ToArray()) ?? Array.Empty<string>();
	}

	/// <summary>
	/// Get file.
	/// </summary>
	/// <param name="filePath">The file path.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	async Task<bool> IJiffyDriveRestService.DeleteFileAsync(string filePath, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		filePath = ToJiffyDriveObjectsUrl(filePath);
		var requestMessage = await InternalGetRequestMessage(filePath, userInfo, cancellationToken);
		requestMessage.Method = HttpMethod.Delete;

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		return responseMessage.IsSuccessStatusCode;
	}

	/// <summary>
	/// helper to get file request message.
	/// </summary>
	/// <param name="templateUri">The template URI.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>HttpRequestMessage.</returns>
	private async Task<HttpRequestMessage> InternalGetRequestMessage(string templateUri, UserSecurityInfo userInfo, CancellationToken cancellationToken)
	{
		var requestMessage = new HttpRequestMessage
		{
			RequestUri = new Uri(templateUri),
			Method = HttpMethod.Get,
		};

		var targetHeaders = requestMessage.Headers;
		userInfo?.UpdateWebHeaders(targetHeaders);
#if DEBUG
		var newAccessToken = userInfo?.AccessToken ?? await _tokenGeneration.CheckTokenExpiryAndGenerateTokenAsync(cancellationToken: cancellationToken);
#else
		var newAccessToken = await _tokenGeneration.CheckTokenExpiryAndGenerateTokenAsync(cancellationToken: cancellationToken);
#endif
		if (newAccessToken is not null)
		{
			targetHeaders.Authorization = new AuthenticationHeaderValue("Bearer", newAccessToken);
		}

		return requestMessage;
	}

	/// <summary>
	/// Converts a given application name to application shared bucket name.
	/// </summary>
	/// <returns>System.String.</returns>
	public string ToApplicationSharedBucketName() => $"shared";

	/// <summary>
	/// Converts a given application name to application private bucket name.
	/// </summary>
	/// <returns>System.String.</returns>
	public string ToApplicationPrivateBucketName() => $"private";

	/// <summary>
	/// Converts to jiffydrivequalifiedurl.
	/// </summary>
	/// <param name="bucketNameOrFullPath">The base path.</param>
	/// <param name="context">The context.</param>
	/// <returns>System.String.</returns>
	/// <exception cref="System.ArgumentException">a relative path was used for the content template path, but jiffy drive path options is not configured</exception>
	public string ToJiffyDriveObjectsUrl(string bucketNameOrFullPath, string context = "objects")
	{
		bucketNameOrFullPath = bucketNameOrFullPath.Trim('/');

		if (!Uri.IsWellFormedUriString(bucketNameOrFullPath, UriKind.Absolute))
		{
			var jiffyDriveUrl = _serviceOptions.Value.JiffyDriveUrl
				?? throw new ArgumentException("a relative path was used for the content template path, but jiffy drive path options is not configured");
			bucketNameOrFullPath = $"{jiffyDriveUrl.TrimEnd('/')}/v1/{context}/{bucketNameOrFullPath}";
		}

		return bucketNameOrFullPath;
	}

	/// <summary>
	/// Converts a url to jiffy qualified url.
	/// </summary>
	/// <param name="bucketNameOrFullPath">The base path.</param>
	/// <param name="filePath">The template URI.</param>
	/// <param name="context">The context.</param>
	/// <returns>System.String.</returns>
	/// <exception cref="System.ArgumentException">a relative path was used for the content template path, but jiffy drive path options is not configured</exception>
	public string ToJiffyDriveObjectsUrlEx(string bucketNameOrFullPath, string filePath, string context = "objects")
	{
		filePath = Path.GetFileName(filePath);
		bucketNameOrFullPath = bucketNameOrFullPath.Trim('/');

		if (!Uri.IsWellFormedUriString(bucketNameOrFullPath, UriKind.Absolute))
		{
			var jiffyDriveUrl = _serviceOptions.Value.JiffyDriveUrl
				?? throw new ArgumentException("a relative path was used for the content template path, but jiffy drive path options is not configured");
			filePath = $"{jiffyDriveUrl.TrimEnd('/')}/v1/{context}/{bucketNameOrFullPath}/{filePath}";
		}
		else
		{
			filePath = $"{bucketNameOrFullPath}/{filePath}";
		}

		return filePath;
	}
}