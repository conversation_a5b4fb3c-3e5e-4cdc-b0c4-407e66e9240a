using Apex.Models;

namespace Apex.Services;

/// <summary>
/// Class ApplicationInfo. This class cannot be inherited.
/// </summary>
public sealed class ApplicationInfo
{
	/// <summary>
	/// Gets or sets the tenant identifier.
	/// </summary>
	/// <value>The tenant identifier.</value>
	public string? TenantId { get; set; }

	/// <summary>
	/// Gets or sets the name of the tenant.
	/// </summary>
	/// <value>The name of the tenant.</value>
	public string? TenantName { get; set; }

	/// <summary>
	/// Gets or sets the application identifier.
	/// </summary>
	/// <value>The application identifier.</value>
	public string? ApplicationId { get; set; }

	/// <summary>
	/// Gets or sets the name of the application.
	/// </summary>
	/// <value>The name of the application.</value>
	public string? ApplicationName { get; set; }

	/// <summary>
	/// Gets or sets the type of the application.
	/// </summary>
	/// <value>The type of the application.</value>
	public int ApplicationType { get; set; }

	/// <summary>
	/// Gets or sets the application URL.
	/// </summary>
	/// <value>The application URL.</value>
	public string? AppUrl { get; set; }

	/// <summary>
	/// Gets or sets the application global identifier.
	/// </summary>
	/// <value>The application global identifier.</value>
	public string? AppGlobalId { get; set; }
}

/// <summary>
/// Class ITokenGenerationService.
/// </summary>
public partial interface ITokenGenerationService
{
	/// <summary>
	/// Gets the access token.
	/// </summary>
	/// <value>The access token.</value>
	public string? AccessToken { get; }

	/// <summary>
	/// Generates a token always - does not cache or use existing cached token.
	/// </summary>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.String.</returns>
	Task<string?> GenerateNewTokenAsync(CancellationToken cancellationToken = default);

	/// <summary>
	/// Checks the token expiry and generate a token.
	/// </summary>
	/// <param name="graceTimeForTokenExpireInSeconds">The grace time for token expire in seconds.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.String.</returns>
	Task<string?> CheckTokenExpiryAndGenerateTokenAsync(long? graceTimeForTokenExpireInSeconds = null, CancellationToken cancellationToken = default);

	/// <summary>
	/// Resolves the user id from tenant + application id.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="useCache">if set to <c>true</c> [use cache].</param>
	/// <param name="clientName">Name of the client.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	Task<string> ResolveUserIdFromTenantAppIdAsync(string tenantId, string appId, bool useCache = true, string? clientName = null, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the application information given the tenantid/appid.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="clientName">Name of the client.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	Task<ApplicationInfo> GetApplicationInfoAsync(string tenantId, string appId, UserSecurityInfo userSecurityInfo, string? clientName = null, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the request message.
	/// </summary>
	/// <param name="requestUrl">The request URL.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;System.Net.Http.HttpRequestMessage&gt;.</returns>
	Task<HttpRequestMessage> GetRequestMessageAsync(string requestUrl, UserSecurityInfo? userInfo, CancellationToken cancellationToken);
}