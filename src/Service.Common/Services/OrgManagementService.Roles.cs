using Apex.Models;
using System.Net.Http.Headers;
using System.Runtime.CompilerServices;

namespace Apex.Services;

/// <summary>
/// Class OrgManagementService.
/// </summary>
public sealed partial class OrgManagementService
{
	/// <summary>
	/// Gets the roles.
	/// </summary>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IList`1&gt; representing the asynchronous operation.</returns>
	/// <exception cref="ArgumentNullException"></exception>
	public async Task<IList<Group>> GetGroupsAsync(UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.IamUrl);

		var url = $"{_serviceOptions.Value.IamUrl!.TrimEnd('/')}/v1/group";
		_logger.LogDebug("GetGroupsAsync - get roles url - Url={Url}", url);

		return await InternalGetRequestAsync<List<Group>>(url, userSecurityInfo, cancellationToken: cancellationToken) ?? [];
	}

	/// <summary>
	/// Gets the roles.
	/// </summary>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IList`1&gt; representing the asynchronous operation.</returns>
	/// <exception cref="ArgumentNullException"></exception>
	public async Task<IList<Role>> GetRolesAsync(UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.IamUrl);

		var url = $"{_serviceOptions.Value.IamUrl!.TrimEnd('/')}/v1/role";
		_logger.LogDebug("GetRolesAsync - get roles url - Url={Url}", url);

		return await InternalGetRequestAsync<List<Role>>(url, userSecurityInfo, cancellationToken: cancellationToken) ?? [];
	}

	/// <summary>
	/// Gets the users for role.
	/// </summary>
	/// <param name="roleId">Id of the role.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IList`1&gt; representing the asynchronous operation.</returns>
	/// <exception cref="ArgumentNullException"></exception>
	public async Task<IList<User>> GetUsersForRoleAsync(string roleId, UserSecurityInfo userSecurityInfo, int pageIndex = 0, int pageSize = 100, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.IamUrl);

		var url = $"{_serviceOptions.Value.IamUrl!.TrimEnd('/')}/v1/user?roleId={roleId}&page={pageIndex}&maxItems={pageSize}";
		_logger.LogDebug("GetUsersForRoleAsync - get users for role url - Url={Url}", url);

		var result = await InternalGetRequestAsync<IamResultWrapper<User>>(url, userSecurityInfo, cancellationToken: cancellationToken);
		return result!.Content;
	}

	/// <summary>
	/// Gets the users for role.
	/// </summary>
	/// <param name="roleId">Name of the role.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IList`1&gt; representing the asynchronous operation.</returns>
	/// <exception cref="ArgumentNullException"></exception>
	public async IAsyncEnumerable<IList<User>> GetAllUsersForRoleAsync(string roleId, UserSecurityInfo userSecurityInfo, [EnumeratorCancellation] CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.IamUrl);

		var page = 0;
		IamResultWrapper<User>? result;
		do
		{
			var url = $"{_serviceOptions.Value.IamUrl!.TrimEnd('/')}/v1/user?roleId={roleId}&page={page++}&maxItems=100";
			_logger.LogDebug("GetAllUsersForRoleAsync - get users for role url - Url={Url}", url);

			result = await InternalGetRequestAsync<IamResultWrapper<User>>(url, userSecurityInfo, cancellationToken: cancellationToken);
			if (result?.Content is User[] users)
			{
				yield return users;
			}
		}
		while (result?.Content?.Length > 0 && page < result.TotalPages);
	}

	/// <summary>
	/// Gets the user by identifier.
	/// </summary>
	/// <param name="userId">The user identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other string?s or threads to receive notice of cancellation.</param>
	/// <returns>IAsyncEnumerable&lt;IList&lt;User&gt;&gt;.</returns>
	public async Task<User?> GetUserByIdAsync(string userId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.OmsUrl);

		var url = $"{_serviceOptions.Value.OmsUrl!.TrimEnd('/')}/v1/user/{userId}";
		_logger.LogDebug("GetUserByIdAsync - get users for role url - Url={Url}", url);

		return await InternalGetRequestAsync<User>(url, userSecurityInfo, cancellationToken: cancellationToken);
	}

	/// <summary>
	/// Gets the users for org.
	/// </summary>
	/// <param name="orgId">The org identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IList`1&gt; representing the asynchronous operation.</returns>
	/// <exception cref="ArgumentNullException"></exception>
	public async Task<IList<OmsUser>> GetUsersForOrgAsync(string orgId, UserSecurityInfo userSecurityInfo, int pageIndex = 0, int pageSize = 100, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.OmsUrl);

		var url = $"{_serviceOptions.Value.OmsUrl!.TrimEnd('/')}/user?orgId={orgId}&page={pageIndex}&size={pageSize}";
		_logger.LogDebug("GetUsersForRoleAsync - get users for role url - Url={Url}", url);

		var result = await InternalGetRequestAsync<OmsResultWrapper<OmsUser>>(url, userSecurityInfo, cancellationToken: cancellationToken);
		return result!.Data;
	}

	/// <summary>
	/// Gets the users for org.
	/// </summary>
	/// <param name="orgId">The org identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IList`1&gt; representing the asynchronous operation.</returns>
	/// <exception cref="ArgumentNullException"></exception>
	public async IAsyncEnumerable<IList<OmsUser>> GetAllUsersForOrgAsync(string orgId, UserSecurityInfo userSecurityInfo, [EnumeratorCancellation] CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.OmsUrl);

		var page = 0;
		OmsResultWrapper<OmsUser>? result;
		do
		{
			var url = $"{_serviceOptions.Value.OmsUrl!.TrimEnd('/')}/user?orgId={orgId}&page={page++}&size=100";
			_logger.LogDebug("GetAllUsersForOrgAsync - get users for role url - Url={Url}", url);

			result = await InternalGetRequestAsync<OmsResultWrapper<OmsUser>>(url, userSecurityInfo, cancellationToken: cancellationToken);
			if (result?.Data is OmsUser[] users)
			{
				yield return users;
			}
		}
		while (result?.Data?.Length > 0);
	}

	/// <summary>
	/// helper to get request.
	/// </summary>
	/// <typeparam name="TOut">The type of the t out.</typeparam>
	/// <param name="requestUrl">The request URL.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="throwError">The throw error.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;TOut&gt;.</returns>
	private async Task<TOut?> InternalGetRequestAsync<TOut>(string requestUrl, UserSecurityInfo userSecurityInfo, bool throwError = true, CancellationToken cancellationToken = default)
	{
		var request = new HttpRequestMessage(HttpMethod.Get, requestUrl);
		var targetHeaders = request.Headers;
		userSecurityInfo?.UpdateWebHeaders(targetHeaders);

		var newAccessToken = await _tokenGenerationService.CheckTokenExpiryAndGenerateTokenAsync(cancellationToken: cancellationToken);
		if (newAccessToken is not null)
		{
			targetHeaders.Authorization = new AuthenticationHeaderValue("Bearer", newAccessToken);
		}

		var client = _connection.GetClient();
		var response = await client.SendAsync(request, cancellationToken);
		if (!response.IsSuccessStatusCode)
		{
			if (throwError)
			{
				throw new OrgManagementServiceException($"error on request Url={requestUrl} Error={await response.Content.ReadAsStringAsync(cancellationToken)}");

			}
			else
			{
				return default;
			}
		}

#if DEBUG2
		var responseString = await response.Content.ReadAsStringAsync(cancellationToken);
#endif
		return await response.Content.ReadFromJsonAsync<TOut>(options: SerializationExtensions._SERIALIZEROPTIONS, cancellationToken: cancellationToken);
	}
}