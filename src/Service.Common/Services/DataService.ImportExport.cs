namespace Apex.Services;

using Apex.Models;
using System;
using System.Web;

/// <summary>
/// Class DataService . This class cannot be inherited.
/// </summary>
public sealed partial class DataService
{
	/// <summary>
	/// Exports the BO records to the provided Csv file.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="endPointName">End name of the point.</param>
	/// <param name="csvJiffyDrivePath">The Csv jiffy drive path.</param>
	/// <param name="parameters">The parameters.</param>
	/// <param name="clientName">Name of the client.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;T&gt;&gt;.</returns>
	/// <exception cref="MessengerException">$"unable to get value from data service - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	/// <exception cref="System.ArgumentNullException">$"unable to get value from data service - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public async Task ExportBoCsvAsync(string tenantId, string appId, string endPointName, string csvJiffyDrivePath, Dictionary<string, string?>? parameters = null, string? clientName = null, CancellationToken cancellationToken = default)
	{
		ArgumentException.ThrowIfNullOrWhiteSpace(tenantId);
		ArgumentException.ThrowIfNullOrWhiteSpace(appId);
		ArgumentException.ThrowIfNullOrWhiteSpace(endPointName);
		ArgumentException.ThrowIfNullOrWhiteSpace(csvJiffyDrivePath);

		// http://app-data-manager.dev-workflow.svc.cluster.local:8002/api/jiffy/defaultInternalService/query/BankStatementItem/to-drive/private/exports/bankstatementitems.csv
		var url = $"{_serviceOptions.Value.DataServiceUrl?.TrimEnd('/')}/query/{endPointName}/to-drive/{csvJiffyDrivePath}";
		if (parameters is not null && parameters.Count > 0)
		{
			url = $"{url}?{parameters.Select(kv => $"{kv.Key}={HttpUtility.UrlEncode(kv.Value)}").JoinWith('&')}";
		}
		_logger.LogInformation("Export to csv to drive request url: Url={Url}", url);

		var responseMessage = await InternalProcessBoCsvAsync(tenantId, appId, url, clientName: clientName, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new DataServiceException($"unable to export data from BO to a csv file in jiffy drive - Url={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}");
		}
	}

	/// <summary>
	/// Imports the BO using the provided Csv file.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="endPointName">End name of the point.</param>
	/// <param name="csvJiffyDrivePath">The Csv jiffy drive path.</param>
	/// <param name="runId">The run identifier.</param>
	/// <param name="clientName">Name of the client.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;T&gt;&gt;.</returns>
	/// <exception cref="MessengerException">$"unable to get value from data service - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	/// <exception cref="System.ArgumentNullException">$"unable to get value from data service - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public async Task ImportBoCsvAsync(string tenantId, string appId, string endPointName, string csvJiffyDrivePath, string runId,
		string? clientName = null, CancellationToken cancellationToken = default)
	{
		ArgumentException.ThrowIfNullOrWhiteSpace(tenantId);
		ArgumentException.ThrowIfNullOrWhiteSpace(appId);
		ArgumentException.ThrowIfNullOrWhiteSpace(endPointName);
		ArgumentException.ThrowIfNullOrWhiteSpace(csvJiffyDrivePath);

		// http://app-data-manager.dev-workflow.svc.cluster.local:8002/api/jiffy/defaultInternalService/entity/BankStatementItem/from-drive?file=private/load_bankstatementitems.csv
		var url = $"{_serviceOptions.Value.DataServiceUrl?.TrimEnd('/')}/entity/{endPointName}/from-drive?file={csvJiffyDrivePath}";
		_logger.LogInformation("Import csv from drive request url: Url={Url}", url);

		var responseMessage = await InternalProcessBoCsvAsync(tenantId, appId, url, clientName: clientName, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new DataServiceException($"unable to import BO using the csv file from jiffy drive - Url={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}");
		}
	}

	/// <summary>
	/// Imports the BO using the provided Csv file.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="sourceEndPointName">Name of the source end point.</param>
	/// <param name="targetEndPointName">Name of the target end point.</param>
	/// <param name="csvJiffyDrivePath">The Csv jiffy drive path.</param>
	/// <param name="runId">The run identifier.</param>
	/// <param name="clientName">Name of the client.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IEnumerable&lt;T&gt;&gt;.</returns>
	/// <exception cref="MessengerException">$"unable to get value from data service - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	/// <exception cref="System.ArgumentNullException">$"unable to get value from data service - TargetUrl={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}</exception>
	public async Task ImportBoRelationshipCsvAsync(string tenantId, string appId, string sourceEndPointName, string targetEndPointName,
		string csvJiffyDrivePath, string runId, string? clientName = null, CancellationToken cancellationToken = default)
	{
		ArgumentException.ThrowIfNullOrWhiteSpace(tenantId);
		ArgumentException.ThrowIfNullOrWhiteSpace(appId);
		ArgumentException.ThrowIfNullOrWhiteSpace(sourceEndPointName);
		ArgumentException.ThrowIfNullOrWhiteSpace(targetEndPointName);
		ArgumentException.ThrowIfNullOrWhiteSpace(csvJiffyDrivePath);

		// http://app-data-manager.dev-workflow.svc.cluster.local:8002/api/jiffy/defaultInternalService/entity/PipelinenameRemittancelineitem/link/bankStatementItem/from-drive?file=...&runId=...
		var url = $"{_serviceOptions.Value.DataServiceUrl?.TrimEnd('/')}/entity/{sourceEndPointName}/link/{targetEndPointName}/from-drive?file={csvJiffyDrivePath}&runId={runId}";
		_logger.LogInformation("Import csv from drive request url: Url={Url}", url);

		var responseMessage = await InternalProcessBoCsvAsync(tenantId, appId, url, clientName: clientName, cancellationToken: cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new DataServiceException($"unable to import BO using the csv file from jiffy drive - Url={url} Status={responseMessage.StatusCode} Error={await responseMessage.Content.ReadAsStringAsync(cancellationToken)}");
		}
	}

	/// <summary>
	/// Internal helper to process the BO Csv.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="requestUrl">The request URL.</param>
	/// <param name="clientName">Name of the client.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;System.Net.Http.HttpRequestMessage&gt;.</returns>
	private async Task<HttpResponseMessage> InternalProcessBoCsvAsync(string tenantId, string appId, string requestUrl, string? clientName = null, CancellationToken cancellationToken = default)
	{
		ArgumentException.ThrowIfNullOrWhiteSpace(tenantId);
		ArgumentException.ThrowIfNullOrWhiteSpace(appId);
		ArgumentException.ThrowIfNullOrWhiteSpace(requestUrl);

		var userId = await _tokenGeneration.ResolveUserIdFromTenantAppIdAsync(tenantId, appId, cancellationToken: cancellationToken);
		_logger.LogInformation("Resolving TenantId/AppId to user - TenantId={TenantId} AppId={AppId} UserId={UserId}", tenantId, appId, userId);

		var userInfo = new UserSecurityInfo(tenantId, appId, userId, null);
		var requestMessage = await InternalGetRequestMessageAsync(requestUrl, userInfo, cancellationToken);
		requestMessage.Method = HttpMethod.Post;

		var client = _connection.GetClient(clientName ?? "default2HoursTimeout");
		return await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
	}
}