namespace Apex.Services;

using System.Text.Json;
using System.Text.Json.Serialization;

/// <summary>
///
/// </summary>
/// <typeparam name="T"></typeparam>
public sealed record KVGetResponseData<T>
(
	[property: JsonPropertyName("data")] T? Data
);

/// <summary>
///
/// </summary>
/// <typeparam name="T"></typeparam>
public sealed record KVGetResponse<T>
(
	[property: JsonPropertyName("data")] KVGetResponseData<T> Data
);

/// <summary>
///
/// </summary>
/// <typeparam name="T"></typeparam>
internal sealed record KVCreate<T>
{
	[JsonPropertyName("data")] public T? Data { get; set; }
}

/// <summary>
/// 
/// </summary>
internal sealed record KVCreateResponse
{
	[JsonPropertyName("data")] public JsonDocument? Data { get; set; }
}

/// <summary>
/// Class VaultService.
/// </summary>
/// <seealso cref="Apex.Services.IVaultService" />
public sealed partial class VaultService
{
	/// <summary>
	/// Creates the secret value.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="key">The key.</param>
	/// <param name="value">The value.</param>
	/// <param name="cancellationToken">The cancellation token.</param>
	/// <returns></returns>
	/// <exception cref="System.ArgumentNullException"></exception>

	public async Task<bool> CreateSecretValueAsync<T>(string key, T value, CancellationToken cancellationToken = default)
		where T : class
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.VaultUrl);
		ArgumentNullException.ThrowIfNull(_vaultOptions.Value.VaultMountPath);

		var kvUrl = $"{_serviceOptions.Value.VaultUrl!.TrimEnd('/')}/v1/{_vaultOptions.Value.VaultMountPath}/data/{key}";
		_logger.LogDebug("CreateSecretValueAsync - create KV url - Url={Url}", kvUrl);

		var result = await InternalPostRequestAsync<KVCreate<T>, KVCreateResponse>(kvUrl,
			new KVCreate<T> { Data = value },
			cancellationToken: cancellationToken);
		return result?.Data is not null;
	}

	/// <summary>
	/// Gets the secret value.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="key">The key.</param>
	/// <param name="cancellationToken">The cancellation token.</param>
	/// <returns></returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	public async Task<T?> GetSecretValueAsync<T>(string key, CancellationToken cancellationToken = default)
		where T : class
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.VaultUrl);
		ArgumentNullException.ThrowIfNull(_vaultOptions.Value.VaultMountPath);

		var kvUrl = $"{_serviceOptions.Value.VaultUrl!.TrimEnd('/')}/v1/{_vaultOptions.Value.VaultMountPath}/data/{key}";
		_logger.LogDebug("GetSecretValueAsync - get KV url - Url={Url}", kvUrl);

		var result = await InternalGetRequestAsync<KVGetResponse<T>>(kvUrl, throwOnFailure: false, cancellationToken: cancellationToken);
		return result?.Data?.Data;
	}

	/// <summary>
	/// Deletes the secret value.
	/// </summary>
	/// <param name="key">The key.</param>
	/// <param name="cancellationToken">The cancellation token.</param>
	/// <exception cref="System.ArgumentNullException"></exception>
	public Task DeleteSecretValueAsync(string key, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(_serviceOptions.Value.VaultUrl);
		ArgumentNullException.ThrowIfNull(_vaultOptions.Value.VaultMountPath);

		var kvUrl = $"{_serviceOptions.Value.VaultUrl!.TrimEnd('/')}/v1/{_vaultOptions.Value.VaultMountPath}/data/{key}";
		_logger.LogDebug("DeleteSecretValueAsync - delete KV url - Url={Url}", kvUrl);

		return InternalDeleteRequestAsync(kvUrl, cancellationToken: cancellationToken);
	}
}