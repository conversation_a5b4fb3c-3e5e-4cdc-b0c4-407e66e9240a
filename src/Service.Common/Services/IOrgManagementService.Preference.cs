using Apex.Models;
using System.Text.Json.Serialization;

namespace Apex.Services;

/// <summary>
/// Class PreferenceScope. This class cannot be inherited.
/// </summary>
public sealed record PreferenceScope
(
	string? UserId = null,
	string? OrgId = null
)
{
	/// <summary>
	/// Validates this instance.
	/// </summary>
	/// <exception cref="ArgumentNullException">UserId</exception>
	public void Validate()
	{
		if (UserId.IsNullOrEmpty() && OrgId.IsNullOrEmpty())
		{
			throw new ArgumentException($"both {nameof(UserId)} and {nameof(OrgId)} cannot be null, at least one has to be specified");
		}
	}
}

/// <summary>
/// Class Preference. This class cannot be inherited.
/// </summary>
public sealed record Preference
(
	[property: JsonPropertyName("name")] string Name,
	[property: JsonPropertyName("preferences")] IList<PreferenceKeyValue>? Preferences
);

/// <summary>
/// Class PreferenceKeyValue. This class cannot be inherited.
/// </summary>
public sealed record PreferenceKeyValue
(
	[property: JsonPropertyName("key")] string Key,
	[property: JsonPropertyName("value")] string? Value
);

/// <summary>
/// Class IOrgManagementService. This class cannot be inherited.
/// </summary>
public partial interface IOrgManagementService
{
	/// <summary>
	/// Gets the user/org preference.
	/// </summary>
	/// <param name="scope">The scope.</param>
	/// <param name="preferenceGroupName">Name of the preference group.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	Task<Preference?> GetPreferenceAsync(PreferenceScope scope, string preferenceGroupName, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Inserts/Updates the user/org preference.
	/// </summary>
	/// <param name="scope">The scope.</param>
	/// <param name="preference">The preference.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	Task UpsertPreferenceAsync(PreferenceScope scope, Preference preference, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Deletes the user/org preference.
	/// </summary>
	/// <param name="scope">The scope.</param>
	/// <param name="preferenceName">Name of the preference.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	Task DeletePreferenceAsync(PreferenceScope scope, string preferenceName, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the user merged preference (with org).
	/// </summary>
	/// <param name="scope">The scope.</param>
	/// <param name="preferenceGroupName">Name of the preference group.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;IList&lt;User&gt;&gt;.</returns>
	Task<IList<PreferenceKeyValue>?> GetMergedPreferenceKeyValuesAsync(PreferenceScope scope, string preferenceGroupName, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);
}