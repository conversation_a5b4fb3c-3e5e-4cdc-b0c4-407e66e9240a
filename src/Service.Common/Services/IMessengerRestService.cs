namespace Apex.Services;

using Apex.Models;
using System.ComponentModel.DataAnnotations;
using System.Text.Json;

/// <summary>
/// Class Event. This class cannot be inherited.
/// </summary>
public sealed partial class Event
{
	/// <summary>
	/// Gets or sets the event identifier.
	/// </summary>
	/// <value>The event identifier.</value>
	public string? EventId { get; set; }

	/// <summary>
	/// Gets the name of the event.
	/// </summary>
	/// <value>The name of the event.</value>
	[Required]
	public string? EventName { get; set; }

	/// <summary>
	/// Gets the payload.
	/// </summary>
	/// <value>The payload.</value>
	public JsonDocument? Payload { get; set; }

	/// <summary>
	/// Gets or sets the additional configuration.
	/// </summary>
	/// <value>The additional configuration.</value>
	public JsonDocument? AdditionalConfiguration { get; set; }

	/// <summary>
	/// Gets or sets the authentication information.
	/// </summary>
	/// <value>The authentication information.</value>
	public EventAuthInfo? AuthInfo { get; set; }

	/// <summary>
	/// Gets or sets the header.
	/// </summary>
	/// <value>The header.</value>
	public EventHeader? Header { get; set; }
}

/// <summary>
/// Class EventHeader.
/// </summary>
public sealed partial class EventHeader
{
	/// <summary>
	/// Gets or sets the visited.
	/// </summary>
	/// <value>The visited.</value>
	public string[]? Visited { get; set; }

	/// <summary>
	/// Gets or sets the hops.
	/// </summary>
	/// <value>The hops.</value>
	public int Hops { get; set; }

	/// <summary>
	/// Gets or sets the expire.
	/// </summary>
	/// <value>The expire.</value>
	public DateTime? Expire { get; set; }
}

/// <summary>
/// Class EventAuthInfo.
/// </summary>
public sealed class EventAuthInfo
{
	/// <summary>
	/// Gets or sets the tenant identifier.
	/// </summary>
	/// <value>The tenant identifier.</value>
	public string? TenantId { get; set; }

	/// <summary>
	/// Gets or sets the application identifier.
	/// </summary>
	/// <value>The application identifier.</value>
	public string? AppId { get; set; }

	/// <summary>
	/// Gets or sets the user identifier.
	/// </summary>
	/// <value>The user identifier.</value>
	public string? UserId { get; set; }
}

/// <summary>
/// Interface IMessengerRestService
/// </summary>
public partial interface IMessengerRestService
{
	/// <summary>
	/// Sets the value.
	/// </summary>
	/// <param name="key">The key.</param>
	/// <param name="value">The value.</param>
	/// <param name="expiryDuration">Duration of the expiry - as per https://en.wikipedia.org/wiki/ISO_8601#Durations.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	Task<bool> SetValueAsync(string key, string? value, string? expiryDuration, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Sets the value using the specified key
	/// </summary>
	/// <param name="key">The key</param>
	/// <param name="value">The value</param>
	/// <param name="expiryDuration">The expiry duration</param>
	/// <param name="userInfo">The user info</param>
	/// <param name="isCrossTenant"></param>
	/// <param name="cancellationToken">The cancellation token</param>
	/// <returns>A task containing the bool</returns>
	Task<bool> SetValueAsync(string key, object? value, string? expiryDuration, UserSecurityInfo? userInfo, bool isCrossTenant = false, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the value.
	/// </summary>
	/// <param name="key">The key.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="isCrossTenant"></param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	Task<string?> GetValueAsync(string key, UserSecurityInfo? userInfo, bool isCrossTenant = false,
		CancellationToken cancellationToken = default);

	/// <summary>
	/// Increments the value.
	/// </summary>
	/// <param name="key">The key.</param>
	/// <param name="expiryDuration">Duration of the expiry.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="isCrossTenant"></param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	Task<bool> IncrementValueAsync(string key, string? expiryDuration, UserSecurityInfo? userInfo,
		bool isCrossTenant = false, CancellationToken cancellationToken = default);

	/// <summary>
	/// Force unsubscribes from the specified topic.
	/// </summary>
	/// <param name="topic">The topic.</param>
	/// <param name="identifier">The identifier.</param>
	/// <param name="isCrossTenant">Post message into a cross tenant stream.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	Task<string?> UnsubscribeAsync(string topic, string identifier, bool isCrossTenant, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Posts the event.
	/// </summary>
	/// <param name="topic">The topic.</param>
	/// <param name="eventInfo">The event information.</param>
	/// <param name="isCrossTenant">Post message into a cross tenant stream.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	Task<string?> PostEventAsync(string topic, Event? eventInfo, bool isCrossTenant, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default);
}