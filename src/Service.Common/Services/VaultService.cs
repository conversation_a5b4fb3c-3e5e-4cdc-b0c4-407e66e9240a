namespace Apex.Services;

using Apex.Options;
using Microsoft.Extensions.Options;

/// <summary>
/// Class VaultService.
/// </summary>
/// <param name="_logger">The logger.</param>
/// <param name="_vaultOptions">The vault options.</param>
/// <param name="_secureVaultOptions">The secure vault options.</param>
/// <param name="_serviceOptions">The service options.</param>
/// <param name="_tokenGenerationService">The token generation service.</param>
/// <param name="_connection">The connection.</param>
/// <remarks>Initializes a new instance of the <see cref="VaultService" /> class.</remarks>
public sealed partial class VaultService(ILogger<VaultService> _logger,
	IOptions<VaultOptions> _vaultOptions,
	IOptions<SecureVaultOptions> _secureVaultOptions,
	IOptions<ServiceOptions> _serviceOptions,
	IVaultTokenGenerationService _tokenGenerationService,
	IWebConnectionService _connection) : IVaultService
{
}