namespace Apex.Services;

using Apex.Models;
using System;

/// <summary>
/// Class MessengerRestService. This class cannot be inherited.
/// Implements the <see cref="Apex.Services.IMessengerRestService" />
/// </summary>
/// <seealso cref="Apex.Services.IMessengerRestService" />
public sealed partial class MessengerRestService
{
	/// <summary>
	/// Gets the value.
	/// </summary>
	/// <param name="key">The key.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="isCrossTenant"></param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	public Task<string?> GetValueAsync(string key, UserSecurityInfo? userInfo, bool isCrossTenant = false, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(key);
		return InternalGetValueAsync(key, userInfo, isCrossTenant, cancellationToken);
	}

	/// <summary>
	/// Internal get value.
	/// </summary>
	/// <param name="key">The key.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="isCrossTenant"></param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	/// <exception cref="Apex.Services.MessengerException">unable to get value from messenger - TargetUrl={url} Status={responseMessage.StatusCode} Error={responseString}</exception>
	private async Task<string?> InternalGetValueAsync(string key, UserSecurityInfo? userInfo, bool isCrossTenant = false, CancellationToken cancellationToken = default)
	{
		var url = $"{_serviceOptions.Value.MessengerUrl?.TrimEnd('/')}/v1/cache/kv/{key}";
		if (isCrossTenant)
		{
			url = $"{url}?isCrossTenant=true";
		}
		var requestMessage = await InternalGetRequestMessageAsync(url, userInfo, cancellationToken);
		requestMessage.Method = HttpMethod.Get;

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		var responseString = await responseMessage.Content.ReadAsStringAsync(cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new MessengerException($"unable to get value from messenger - TargetUrl={url} Status={responseMessage.StatusCode} Error={responseString}");
		}
		return responseString;
	}

	/// <summary>
	/// Sets the value using the specified key
	/// </summary>
	/// <param name="key">The key</param>
	/// <param name="value">The value</param>
	/// <param name="expiryDuration">The expiry duration</param>
	/// <param name="userInfo">The user info</param>
	/// <param name="isCrossTenant"></param>
	/// <param name="cancellationToken">The cancellation token</param>
	/// <returns>A task containing the bool</returns>
	public Task<bool> SetValueAsync(string key, object? value, string? expiryDuration, UserSecurityInfo? userInfo,
		bool isCrossTenant = false,
		CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(key);

		var baseUrl = $"{_serviceOptions.Value.MessengerUrl?.TrimEnd('/')}/v1/cache/kv/{key}";
		var queryParams = new List<string>();
		if (!expiryDuration.IsNullOrEmpty())
		{
			queryParams.Add($"expiry={expiryDuration}");
		}
		queryParams.Add($"isCrossTenant={isCrossTenant.ToString().ToLower()}");
		var url = $"{baseUrl}?{string.Join("&", queryParams)}";
		return InternalPutValueAsync(url, value, userInfo, cancellationToken);
	}

	/// <summary>
	/// Set value.
	/// </summary>
	/// <param name="key">The key.</param>
	/// <param name="value">The value.</param>
	/// <param name="expiryDuration">Duration of the expiry - as per https://en.wikipedia.org/wiki/ISO_8601#Durations.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	/// <exception cref="System.ArgumentNullException"></exception>
	/// <exception cref="Apex.Services.MessengerException"></exception>
	public Task<bool> SetValueAsync(string key, string? value, string? expiryDuration, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(key);

		var url = $"{_serviceOptions.Value.MessengerUrl?.TrimEnd('/')}/v1/cache/kv/{key}?{(!expiryDuration.IsNullOrEmpty() ? $"expiry={expiryDuration}" : null)}";
		return InternalPutValueAsync(url, value, userInfo, cancellationToken);
	}

	/// <summary>
	/// Increments the value.
	/// </summary>
	/// <param name="key">The key.</param>
	/// <param name="expiryDuration">Duration of the expiry - as per https://en.wikipedia.org/wiki/ISO_8601#Durations.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="isCrossTenant"></param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	/// <exception cref="ArgumentNullException"></exception>
	/// <exception cref="System.ArgumentNullException"></exception>
	/// <exception cref="Apex.Services.MessengerException"></exception>
	public Task<bool> IncrementValueAsync(string key, string? expiryDuration, UserSecurityInfo? userInfo, bool isCrossTenant = false, CancellationToken cancellationToken = default)
	{
		ArgumentNullException.ThrowIfNull(key);

		var baseUrl = $"{_serviceOptions.Value.MessengerUrl?.TrimEnd('/')}/v1/cache/kv/{key}/operation/incr";
		var queryParams = new List<string>();

		if (!expiryDuration.IsNullOrEmpty())
		{
			queryParams.Add($"expiry={expiryDuration}");
		}

		queryParams.Add($"isCrossTenant={isCrossTenant.ToString().ToLower()}");

		var url = $"{baseUrl}?{string.Join("&", queryParams)}";
		return InternalPutValueAsync<string>(url, null, userInfo, cancellationToken);
	}

	/// <summary>
	/// Internal put value.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="url">The URL.</param>
	/// <param name="value">The value.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.Boolean&gt; representing the asynchronous operation.</returns>
	/// <exception cref="MessengerException">unable to set value in messenger - TargetUrl={url} Status={responseMessage.StatusCode} Error={responseString}</exception>
	/// <exception cref="Apex.Services.MessengerException">unable to set value in messenger - TargetUrl={url} Status={responseMessage.StatusCode} Error={responseString}</exception>
	private async Task<bool> InternalPutValueAsync<T>(string url, T? value, UserSecurityInfo? userInfo, CancellationToken cancellationToken = default)
	{
		var requestMessage = await InternalGetRequestMessageAsync(url, userInfo, cancellationToken);
		requestMessage.Method = HttpMethod.Put;
		requestMessage.Content = value is not null ? JsonContent.Create(value) : null;

		var client = _connection.GetClient();
		var responseMessage = await client.SendAsync(requestMessage, cancellationToken: cancellationToken);
		var responseString = await responseMessage.Content.ReadAsStringAsync(cancellationToken);
		if (!responseMessage.IsSuccessStatusCode)
		{
			throw new MessengerException($"unable to set/increment value in messenger - TargetUrl={url} Status={responseMessage.StatusCode} Error={responseString}");
		}
		return true;
	}
}