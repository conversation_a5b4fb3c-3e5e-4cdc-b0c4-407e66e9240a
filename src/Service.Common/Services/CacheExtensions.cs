using Apex.Options;
using Microsoft.Extensions.Options;
using StackExchange.Redis;

namespace Apex.Services;

/// <summary>
/// Class CacheExtensions.
/// </summary>
public static partial class CacheExtensions
{
	/// <summary>
	/// Adds the new hybrid cache.
	/// </summary>
	/// <param name="services">The services.</param>
	/// <param name="configuration">The configuration.</param>
	/// <returns>IServiceCollection.</returns>
	public static IServiceCollection AddNewHybridCache(this IServiceCollection services, IConfiguration configuration)
	{
		services.AddOptions<SecureRedisOptions>().Bind(configuration);
		services.AddHybridCache();
		services.AddStackExchangeRedisCache(options =>
		{
			var sp = services.BuildServiceProvider();
			var secureRedisOptions = (sp.GetService<IOptions<SecureRedisOptions>>()?.Value)
				?? throw new ConfigurationException("unable to find valid cache options");

			var endPoints = new EndPointCollection
			{
				{ secureRedisOptions.Url, int.Parse(secureRedisOptions.Port) }
			};

			options.ConfigurationOptions = new ConfigurationOptions
			{
				User = secureRedisOptions.Username,
				Password = secureRedisOptions.Password,
				EndPoints = endPoints
			};
		});

		return services;
	}
}