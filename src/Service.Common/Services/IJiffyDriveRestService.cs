namespace Apex.Services;

using Apex.Models;

/// <summary>
/// Interface IJiffyDriveRestService
/// </summary>
public partial interface IJiffyDriveRestService
{
	/// <summary>
	/// Lists the folder.
	/// </summary>
	/// <param name="bucketAndFolderPath">The bucket and folder path.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.String&gt;.</returns>
	Task<IEnumerable<string>> ListFolderAsync(string bucketAndFolderPath, UserSecurityInfo userInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Deletes the file.
	/// </summary>
	/// <param name="filePath">The file path.</param>
	/// <param name="userInfo">The user information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.Nullable&lt;System.String&gt;&gt;.</returns>
	Task<bool> DeleteFileAsync(string filePath, UserSecurityInfo userInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Converts a given application name to application shared bucket name.
	/// </summary>
	/// <returns>System.String.</returns>
	string ToApplicationSharedBucketName();

	/// <summary>
	/// Converts a given application name to application private bucket name.
	/// </summary>
	/// <returns>System.String.</returns>
	string ToApplicationPrivateBucketName();

	/// <summary>
	/// Converts a given path to a jiffy absolute drive path.
	/// </summary>
	/// <param name="bucketNameOrFullPath">The bucket name or full path.</param>
	/// <param name="context">The context.</param>
	/// <returns>System.String.</returns>
	string ToJiffyDriveObjectsUrl(string bucketNameOrFullPath, string context = "objects");

	/// <summary>
	/// Converts a given path to a jiffy absolute drive path.
	/// </summary>
	/// <param name="bucketNameOrFullPath">The bucket name or full path.</param>
	/// <param name="filePath">The file path.</param>
	/// <param name="context">The context.</param>
	/// <returns>System.String.</returns>
	string ToJiffyDriveObjectsUrlEx(string bucketNameOrFullPath, string filePath, string context = "objects");
}