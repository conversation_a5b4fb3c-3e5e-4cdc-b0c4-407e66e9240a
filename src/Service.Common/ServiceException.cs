using System.Runtime.Serialization;

namespace Apex;

/// <summary>
/// Class ServiceException.
/// Implements the <see cref="System.Exception" />
/// </summary>
/// <seealso cref="System.Exception" />
[Serializable]
public sealed partial class ServiceException : Exception
{
	/// <summary>
	/// Initializes a new instance of the <see cref="ServiceException" /> class.
	/// </summary>
	/// <param name="errorMessage">The error message.</param>
	/// <returns>InvalidStudentNameException.</returns>
	public ServiceException(string errorMessage)
		: base(errorMessage)
	{
	}

	/// <summary>
	/// Initializes a new instance of the <see cref="ServiceException" /> class.
	/// </summary>
	/// <param name="serializationInfo">The serialization information.</param>
	/// <param name="streamingContext">The streaming context.</param>
	private ServiceException(SerializationInfo serializationInfo, StreamingContext streamingContext)
		: base(serializationInfo, streamingContext)
	{
	}
}