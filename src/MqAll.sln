
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.2.32616.157
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Mq", "Mq\Mq.csproj", "{F0477470-4318-4117-9BFF-EC3531344709}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Service", "Service", "{CC051D24-F28E-42E7-8A92-F2B81DAF1DAF}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Test", "Test", "{74A17785-D571-4922-B7A1-27F3152D8A4C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MqTest", "..\test\MqTest\MqTest.csproj", "{064A5761-9BF2-4B62-87E6-AEF70E6D72B1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{779CBD0E-276D-4923-8C2A-3F2AC6321834}"
	ProjectSection(SolutionItems) = preProject
		docker-compose-localsetup.yaml = docker-compose-localsetup.yaml
		Dockerfile = Dockerfile
		nuget.config = nuget.config
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MqRouting", "..\..\mqrouting\src\MqRouting\MqRouting.csproj", "{DCB25A85-C536-49B8-85A0-5CD64CA32B55}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MqChannels", "..\..\mqchannels\src\MqChannels\MqChannels.csproj", "{D014CA92-01BE-4AC3-9520-954344FF79F5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Service.Common", "..\..\net-service-common\src\Service.Common\Service.Common.csproj", "{D59C79F8-08C3-4E6D-82AF-41EEAD3F9229}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MqChannelsTest", "..\..\mqchannels\test\MqChannelsTest\MqChannelsTest.csproj", "{B3B11BAC-C1C0-4015-8F29-5AE9278DEE12}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MqRoutingTest", "..\..\mqrouting\test\MqRoutingTest\MqRoutingTest.csproj", "{F2D47BE6-884B-4886-AF0A-29AC3F80D232}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Common.Mq", "..\..\net-common-mq\src\Common.Mq\Common.Mq.csproj", "{53C3F935-C11E-1575-9DF3-991AA793DEAB}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F0477470-4318-4117-9BFF-EC3531344709}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F0477470-4318-4117-9BFF-EC3531344709}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F0477470-4318-4117-9BFF-EC3531344709}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F0477470-4318-4117-9BFF-EC3531344709}.Release|Any CPU.Build.0 = Release|Any CPU
		{064A5761-9BF2-4B62-87E6-AEF70E6D72B1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{064A5761-9BF2-4B62-87E6-AEF70E6D72B1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{064A5761-9BF2-4B62-87E6-AEF70E6D72B1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{064A5761-9BF2-4B62-87E6-AEF70E6D72B1}.Release|Any CPU.Build.0 = Release|Any CPU
		{DCB25A85-C536-49B8-85A0-5CD64CA32B55}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DCB25A85-C536-49B8-85A0-5CD64CA32B55}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DCB25A85-C536-49B8-85A0-5CD64CA32B55}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DCB25A85-C536-49B8-85A0-5CD64CA32B55}.Release|Any CPU.Build.0 = Release|Any CPU
		{D014CA92-01BE-4AC3-9520-954344FF79F5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D014CA92-01BE-4AC3-9520-954344FF79F5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D014CA92-01BE-4AC3-9520-954344FF79F5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D014CA92-01BE-4AC3-9520-954344FF79F5}.Release|Any CPU.Build.0 = Release|Any CPU
		{D59C79F8-08C3-4E6D-82AF-41EEAD3F9229}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D59C79F8-08C3-4E6D-82AF-41EEAD3F9229}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D59C79F8-08C3-4E6D-82AF-41EEAD3F9229}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D59C79F8-08C3-4E6D-82AF-41EEAD3F9229}.Release|Any CPU.Build.0 = Release|Any CPU
		{B3B11BAC-C1C0-4015-8F29-5AE9278DEE12}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B3B11BAC-C1C0-4015-8F29-5AE9278DEE12}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B3B11BAC-C1C0-4015-8F29-5AE9278DEE12}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B3B11BAC-C1C0-4015-8F29-5AE9278DEE12}.Release|Any CPU.Build.0 = Release|Any CPU
		{F2D47BE6-884B-4886-AF0A-29AC3F80D232}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F2D47BE6-884B-4886-AF0A-29AC3F80D232}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F2D47BE6-884B-4886-AF0A-29AC3F80D232}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F2D47BE6-884B-4886-AF0A-29AC3F80D232}.Release|Any CPU.Build.0 = Release|Any CPU
		{53C3F935-C11E-1575-9DF3-991AA793DEAB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{53C3F935-C11E-1575-9DF3-991AA793DEAB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{53C3F935-C11E-1575-9DF3-991AA793DEAB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{53C3F935-C11E-1575-9DF3-991AA793DEAB}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{F0477470-4318-4117-9BFF-EC3531344709} = {CC051D24-F28E-42E7-8A92-F2B81DAF1DAF}
		{064A5761-9BF2-4B62-87E6-AEF70E6D72B1} = {74A17785-D571-4922-B7A1-27F3152D8A4C}
		{DCB25A85-C536-49B8-85A0-5CD64CA32B55} = {CC051D24-F28E-42E7-8A92-F2B81DAF1DAF}
		{D014CA92-01BE-4AC3-9520-954344FF79F5} = {CC051D24-F28E-42E7-8A92-F2B81DAF1DAF}
		{D59C79F8-08C3-4E6D-82AF-41EEAD3F9229} = {CC051D24-F28E-42E7-8A92-F2B81DAF1DAF}
		{B3B11BAC-C1C0-4015-8F29-5AE9278DEE12} = {74A17785-D571-4922-B7A1-27F3152D8A4C}
		{F2D47BE6-884B-4886-AF0A-29AC3F80D232} = {74A17785-D571-4922-B7A1-27F3152D8A4C}
		{53C3F935-C11E-1575-9DF3-991AA793DEAB} = {CC051D24-F28E-42E7-8A92-F2B81DAF1DAF}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {FEA025D6-5503-4EF5-9680-D5A0C999410D}
	EndGlobalSection
EndGlobal
