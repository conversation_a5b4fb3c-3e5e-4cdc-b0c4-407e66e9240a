<Project Sdk="Microsoft.NET.Sdk.Web">
	<Import Project="../Build.Executable.properties" />

	<PropertyGroup>
		<AssemblyName>Net.Service.MqChannels</AssemblyName>
		<RootNamespace>MqChannels</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<Compile Remove="Models\OneSignal\**" />
		<Compile Remove="Scripts\**" />
		<Content Remove="Models\OneSignal\**" />
		<Content Remove="Scripts\**" />
		<EmbeddedResource Remove="Models\OneSignal\**" />
		<EmbeddedResource Remove="Scripts\**" />
		<None Remove="Models\OneSignal\**" />
		<None Remove="Scripts\**" />
		<Compile Update="Services\MessengerProviderService.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\IMessengerProviderService.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
	</ItemGroup>

	<ItemGroup>
		<Compile Remove="BackgroundServices\OneSignalEmailChannelProviderBackgroundService.cs" />
		<Compile Remove="Models\InAppMessage.cs" />
		<Compile Remove="Services\IOneSignalProviderService.cs" />
		<Compile Remove="Services\OneSignalProviderService.cs" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Asp.Versioning.Mvc" Version="8.1.0" />
		<PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
		<PackageReference Include="JsonPatch.Net" Version="3.3.0" />
		<PackageReference Include="RabbitMq.Client" Version="7.1.2" />
		<PackageReference Include="Net.Common.Mq" Version="3.0.15" />
		<PackageReference Include="Net.Common.PgRepository2" Version="3.0.19" />
		<!--<ProjectReference Include="..\..\..\net-common-repository\src\Common.PgRepository2\Common.PgRepository2.csproj" />-->
		<PackageReference Include="Net.Service.Common" Version="3.0.50" />
		<!--<ProjectReference Include="..\..\..\net-service-common\src\Service.Common\Service.Common.csproj" />-->
	</ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="MqChannelsTest" />
	</ItemGroup>

	<ItemGroup>
		<Compile Update="BackgroundServices\ChannelBackgroundService.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="BackgroundServices\MessengerInAppChannelProviderBackgroundService.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\ChannelTypeHandler\BaseChannelTypeHandler.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\ChannelTypeHandler\SmsChannelTypeHandler.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\ChannelTypeHandler\InAppChannelTypeHandler.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\ChannelTypeHandler\EmailChannelTypeHandler.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\ChannelTypeHandler\WebHookChannelTypeHandler.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\ChannelTypeHandler\WorkflowChannelTypeHandler.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\Email\EmailHistoryService.CRUD.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\InApp\InAppHistoryService.CRUD.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="**\*.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
	</ItemGroup>

	<ItemGroup>
		<Compile Update="Controllers\Broadcast\BroadcastAlertsController.CRUD.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Controllers\Broadcast\BroadcastAlertsController.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Controllers\BellIconPreferences\BellIconPreferencesController.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Controllers\BellIconPreferences\BellIconPreferencesController.Org.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Properties\Resources.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>Resources.resx</DependentUpon>
		</Compile>
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Update="Properties\Resources.resx">
			<Generator>ResXFileCodeGenerator</Generator>
			<LastGenOutput>Resources.Designer.cs</LastGenOutput>
		</EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
		<None Update="appsettings.yaml">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="appsettings.Development.yaml" Condition="$(Configuration) == 'Debug'">
			<DependentUpon>appsettings.yaml</DependentUpon>
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="**\*.sql">
			<SonarQubeExclude>true</SonarQubeExclude>
		</None>
	</ItemGroup>
	<ItemGroup Condition="$(Configuration) == 'Debug'">
		<None Update="up.cmd">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<Target Name="CopyToSecretsFolder" AfterTargets="AfterBuild" Condition="$(Configuration) == 'Debug'">
		<Message Importance="high" Text="copying principal.properties =&gt; $(USERPROFILE)\secrets-store" />
		<Copy SourceFiles="db.properties" DestinationFolder="$(USERPROFILE)\secrets-store" SkipUnchangedFiles="true" ContinueOnError="false" />
		<Copy SourceFiles="principal.properties" DestinationFolder="$(USERPROFILE)\secrets-store" SkipUnchangedFiles="true" ContinueOnError="false" />
		<Copy SourceFiles="extra.properties" DestinationFolder="$(USERPROFILE)\secrets-store" SkipUnchangedFiles="true" ContinueOnError="false" />
	</Target>

</Project>
