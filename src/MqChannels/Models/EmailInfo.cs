using System.Text.Json;
using System.Text.Json.Serialization;

namespace EmailSender.Models;

/// <summary>
/// Email Priority
/// </summary>
public enum EmailPriority
{
	/// <summary>
	/// The normal
	/// </summary>
	Normal,

	/// <summary>
	/// The low
	/// </summary>
	Low,

	/// <summary>
	/// The high
	/// </summary>
	High,

	/// <summary>
	/// The undefined
	/// </summary>
	Undefined = Normal
}

/// <summary>
/// Class BaseEmailInfo.
/// </summary>
public abstract class BaseEmailInfo
{
	/// <summary>
	/// Gets the identifier.
	/// </summary>
	/// <value>The identifier.</value>
	public string? Id { get; set; }

	/// <summary>
	/// Gets the name of the provider.
	/// </summary>
	/// <value>The name of the provider.</value>
	public string? ProviderName { get; set; }

	/// <summary>
	/// Gets the data.
	/// </summary>
	/// <value>The data.</value>
	public JsonDocument? Data { get; set; }

	/// <summary>
	/// Gets the jiffy drive urls to attach to email.
	/// </summary>
	/// <value>The attachments.</value>
	[JsonIgnore]
	public string[]? AttachmentItems { get; set; }

	/// <summary>
	/// Gets or sets the temporary attachments.
	/// </summary>
	/// <value>The temporary attachments.</value>
	[JsonIgnore]
	internal (string? FilePath, string? FileName, string? OriginalFilePath)[]? TempAttachments { get; set; }

	/// <summary>
	/// Gets or sets the additional temporary files.
	/// </summary>
	/// <value>The additional temporary files.</value>
	[JsonIgnore]
	internal (string? FilePath, string? FileName, string? OriginalFilePath)[]? AdditionalTempFiles { get; set; }

	/// <summary>
	/// Gets the reply to.
	/// </summary>
	/// <value>The in reply to.</value>
	public string? ReplyTo { get; set; }

	/// <summary>
	/// Gets the in reply to.
	/// </summary>
	/// <value>The in reply to.</value>
	public string? InReplyTo { get; set; }

	/// <summary>
	/// Gets or sets the references.
	/// </summary>
	/// <value>The references.</value>
	public string[]? References { get; set; }

	/// <summary>
	/// Clones the specified new instance.
	/// </summary>
	/// <param name="newInstance">The new instance.</param>
	/// <returns>BaseEmailInfo.</returns>
	internal BaseEmailInfo Clone(BaseEmailInfo newInstance)
	{
		newInstance.Id = this.Id;
		newInstance.ProviderName = this.ProviderName;
		newInstance.Data = this.Data;
		newInstance.AttachmentItems = this.AttachmentItems;
		newInstance.TempAttachments = this.TempAttachments;
		newInstance.ReplyTo = this.ReplyTo;
		newInstance.InReplyTo = this.InReplyTo;
		newInstance.References = this.References;
		return newInstance;
	}
}

/// <summary>
/// class CustomEmail.
/// </summary>
public sealed partial class CustomEmail : BaseEmailInfo
{
	/// <summary>
	/// Gets or sets to - separator - ;
	/// </summary>
	/// <value>To.</value>
	public string? To { get; set; }

	/// <summary>
	/// Gets or sets the cc - separator - ;
	/// </summary>
	/// <value>The cc.</value>
	public string? Cc { get; set; }

	/// <summary>
	/// Gets or sets the BCC - separator - ;
	/// </summary>
	/// <value>The BCC.</value>
	public string? Bcc { get; set; }

	/// <summary>
	/// Gets or sets the subject.
	/// </summary>
	/// <value>The subject.</value>
	public string? Subject { get; set; }

	/// <summary>
	/// Gets or sets the text body.
	/// </summary>
	/// <value>The text body.</value>
	public string? TextBody { get; set; }

	/// <summary>
	/// Gets or sets the HTML body.
	/// </summary>
	/// <value>The HTML body.</value>
	public string? HtmlBody { get; set; }

	/// <summary>
	/// Gets or sets the priority.
	/// </summary>
	/// <value>The priority.</value>
	public EmailPriority Priority { get; set; } = EmailPriority.Normal;

	/// <summary>
	/// Gets the jiffy drive urls to attach to email.
	/// </summary>
	/// <value>The attachments.</value>
	public string? Attachments { get; set; }

	/// <summary>
	/// Clones this instance.
	/// </summary>
	/// <returns>CustomEmail.</returns>
	public CustomEmail Clone()
	{
		var newInstance = (CustomEmail)base.Clone(new CustomEmail());
		newInstance.To = this.To;
		newInstance.Cc = this.Cc;
		newInstance.Bcc = this.Bcc;
		newInstance.Subject = this.Subject;
		newInstance.TextBody = this.TextBody;
		newInstance.HtmlBody = this.HtmlBody;
		newInstance.Priority = this.Priority;
		newInstance.Attachments = this.Attachments;
		return newInstance;
	}
}

/// <summary>
/// Class ForwardOrReplyToEmailInfo.
/// </summary>
public abstract partial class ForwardOrReplyToEmailInfo
{
	/// <summary>
	/// Gets or sets the email.
	/// </summary>
	/// <value>The email.</value>
	public required CustomEmail Email { get; set; }
}

/// <summary>
/// Class ForwardCustomEmail. This class cannot be inherited.
/// </summary>
public sealed partial class ForwardCustomEmail : ForwardOrReplyToEmailInfo
{
	/// <summary>
	/// Gets or sets the forwarding email.
	/// </summary>
	/// <value>The forwarding email.</value>
	public required ReceivedEmail ForwardingEmailRef { get; set; }
}

/// <summary>
/// Class ReplyToCustomEmail. This class cannot be inherited.
/// </summary>
public sealed partial class ReplyToCustomEmail : ForwardOrReplyToEmailInfo
{
	/// <summary>
	/// Gets or sets the replyting to email.
	/// </summary>
	/// <value>The forwarding email.</value>
	public required ReceivedEmail ReplyingToEmailRef { get; set; }
}

/// <summary>
/// Class TemplatedEmail.
/// </summary>
public sealed partial class TemplatedEmail : BaseEmailInfo
{
	/// <summary>
	/// Gets the template path.
	/// </summary>
	/// <value>The template path.</value>
	public string? TemplatePath { get; set; }
}

/// <summary>
/// Class ReceivedEmail.
/// NOTE: Keep this in sync with email receiver
/// </summary>
public sealed partial class ReceivedEmail
{
	/// <summary>
	/// Gets the message identifier.
	/// </summary>
	/// <value>The message identifier.</value>
	public string? MessageId { get; init; }

	/// <summary>
	/// Gets from.
	/// </summary>
	/// <value>From.</value>
	public string? From { get; init; }

	/// <summary>
	/// Gets or sets to.
	/// </summary>
	/// <value>To.</value>
	public string? To { get; init; }

	/// <summary>
	/// Gets or sets the cc.
	/// </summary>
	/// <value>The cc.</value>
	public string? Cc { get; init; }

	/// <summary>
	/// Gets or sets the priority.
	/// </summary>
	/// <value>The priority.</value>
	public EmailPriority Priority { get; init; } = EmailPriority.Normal;

	/// <summary>
	/// Gets or sets the subject.
	/// </summary>
	/// <value>The subject.</value>
	public string? Subject { get; init; }

	/// <summary>
	/// Gets or sets the text body.
	/// </summary>
	/// <value>The text body.</value>
	public string? TextBody { get; init; }

	/// <summary>
	/// Gets or sets the HTML body.
	/// </summary>
	/// <value>The HTML body.</value>
	public string? HtmlBody { get; init; }

	/// <summary>
	/// Gets the jiffy drive urls of email attachments.
	/// </summary>
	/// <value>The attachments.</value>
	public string[]? Attachments { get; internal set; }

	/// <summary>
	/// Gets or sets the reply to.
	/// </summary>
	/// <value>The reply to.</value>
	public string? ReplyTo { get; set; }

	/// <summary>
	/// Gets the references.
	/// </summary>
	/// <value>The references.</value>
	public string[]? References { get; set; }

	/// <summary>
	/// Gets or sets the in reply to.
	/// </summary>
	/// <value>The in reply to.</value>
	public string? InReplyTo { get; set; }
}

/// <summary>
/// Class SendEmailResponse.
/// </summary>
public sealed partial class SendEmailResponse
{
	/// <summary>
	/// Gets the message identifier.
	/// </summary>
	/// <value>The message identifier.</value>
	public string? MessageId { get; init; }

	/// <summary>
	/// Gets the status.
	/// </summary>
	/// <value>The status.</value>
	public string? Status { get; init; }
}

/// <summary>
/// Class TestCredentialResponse. This class cannot be inherited.
/// </summary>
public sealed partial class TestMailboxResponse
{
	/// <summary>
	/// Gets a value indicating whether this <see cref="TestMailboxResponse" /> is success.
	/// </summary>
	/// <value><c>true</c> if success; otherwise, <c>false</c>.</value>
	public bool Success { get; set; }

	/// <summary>
	/// Gets the message.
	/// </summary>
	/// <value>The message.</value>
	public string? Message { get; set; }
}

/// <summary>
/// Class EmailTemplateInfo - Email template info stored as a bunch of handler bars template.
/// </summary>
public sealed partial record EmailTemplateInfo
{
	/// <summary>
	/// Gets or sets to - template.
	/// </summary>
	/// <value>To.</value>
	public string? To { get; init; }

	/// <summary>
	/// Gets or sets the cc - template.
	/// </summary>
	/// <value>The cc.</value>
	public string? Cc { get; init; }

	/// <summary>
	/// Gets or sets the BCC - template.
	/// </summary>
	/// <value>The BCC.</value>
	public string? Bcc { get; init; }

	/// <summary>
	/// Gets or sets the subject - template.
	/// </summary>
	/// <value>The subject.</value>
	public string? Subject { get; init; }

	/// <summary>
	/// Gets or sets the text body - template.
	/// </summary>
	/// <value>The text body.</value>
	public string? TextBody { get; init; }

	/// <summary>
	/// Gets or sets the HTML body - template.
	/// </summary>
	/// <value>The HTML body.</value>
	public string? HtmlBody { get; init; }

	/// <summary>
	/// Gets or sets the priority - template.
	/// </summary>
	/// <value>The priority.</value>
	public string? Priority { get; init; }

	/// <summary>
	/// Gets the attachments.
	/// </summary>
	/// <value>The attachments.</value>
	public string? Attachments { get; init; }
}

/// <summary>
/// Class EmailInfoWrapper.
/// </summary>
public record EmailInfoWrapper(string TenantId, string AppId, CustomEmail? CustomEmail, TemplatedEmail? TemplatedEmail)
{
	/// <summary>
	/// Gets the message identifier.
	/// </summary>
	/// <value>The message identifier.</value>
	public string? MessageId => CustomEmail?.Id ?? TemplatedEmail?.Id;
}