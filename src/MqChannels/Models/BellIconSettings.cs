using System.Text.Json.Serialization;

namespace MqChannels.Models;

/// <summary>
/// Class BellIconSettings. This class cannot be inherited.
/// </summary>
public sealed class BellIconSettings
{
	/// <summary>
	/// The default
	/// </summary>
	public readonly static BellIconSettings Default = new();

	/// <summary>
	/// Gets or sets a value indicating whether this <see cref="BellIconSettings"/> is pagination.
	/// </summary>
	/// <value><c>true</c> if pagination; otherwise, <c>false</c>.</value>
	[JsonPropertyName("pagination")]
	public bool Pagination { get; set; } = true;

	/// <summary>
	/// Gets or sets the size of the page.
	/// </summary>
	/// <value>The size of the page.</value>
	[JsonPropertyName("pageSize")]
	public int PageSize { get; set; } = 20;

	/// <summary>
	/// Gets or sets a value indicating whether to show alert message icon.
	/// </summary>
	/// <value><c>true</c> to show alert message icon; otherwise, <c>false</c>.</value>
	[JsonPropertyName("showAlertMessageIcon")]
	public bool ShowAlertMessageIcon { get; set; } = true;

	/// <summary>
	/// Gets or sets a value indicating whether to show read unread option.
	/// </summary>
	/// <value><c>true</c> to show read unread option; otherwise, <c>false</c>.</value>
	[JsonPropertyName("showReadUnreadOption")]
	public bool ShowReadUnreadOption { get; set; } = true;

	/// <summary>
	/// Gets or sets a value indicating whether to use absolute time.
	/// </summary>
	/// <value><c>true</c> to use absolute time; otherwise, <c>false</c>.</value>
	[JsonPropertyName("useAbsoluteTime")]
	public bool UseAbsoluteTime { get; set; } = true;

	/// <summary>
	/// Gets or sets the date time format.
	/// </summary>
	/// <value>The date time format.</value>
	[JsonPropertyName("dateTimeFormat")]
	public string? DateTimeFormat { get; set; } = "MM/DD/YYYY hh:mm:ss a";

	/// <summary>
	/// Gets or sets a value indicating whether to show history option.
	/// </summary>
	/// <value><c>true</c> to show history option; otherwise, <c>false</c>.</value>
	[JsonPropertyName("showHistoryOption")]
	public bool ShowHistoryOption { get; set; } = true;

	/// <summary>
	/// Gets or sets a value indicating whether to show alerts history popup.
	/// </summary>
	/// <value><c>true</c> to show alerts history popup; otherwise, <c>false</c>.</value>
	[JsonPropertyName("showAlertsHistoryPopup")]
	public bool ShowAlertsHistoryPopup { get; set; } = true;

	/// <summary>
	/// Gets or sets a value indicating whether to show alerts bound to current application.
	/// </summary>
	/// <value><c>true</c> to show alerts for current application; otherwise, <c>false</c>.</value>
	[JsonPropertyName("showAppBoundAlerts")]
	public bool ShowAppBoundAlerts { get; set; } = false;
}