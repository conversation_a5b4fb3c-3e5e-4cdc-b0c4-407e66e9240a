using Apex.Models;
using Dapper.Contrib.Extensions;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace MqChannels.Models;

/// <summary>
/// Enum DeliveryStatus
/// </summary>
public enum DeliveryStatus
{
	/// <summary>
	/// Created
	/// </summary>
	C,

	/// <summary>
	/// Delivered
	/// </summary>
	D,

	/// <summary>
	/// Errored
	/// </summary>
	E
}

/// <summary>
/// Class BaseAlert.
/// </summary>
public partial class BaseAlert : Common.Postgres.TenantAppScopedObjectWithId
{
	/// <summary>
	/// Gets the recipient user id.
	/// </summary>
	/// <value>The recipient roles.</value>
	[JsonPropertyName("userId")]
	public string? user_id { get; set; }

	/// <summary>
	/// Gets the definition identifier.
	/// </summary>
	/// <value>The definition identifier.</value>
	[JsonPropertyName("defId")]
	public string? def_id { get; set; }

	/// <summary>
	/// Gets or sets the priority.
	/// </summary>
	/// <value>The priority.</value>
	[JsonPropertyName("priority")]
	public string? priority { get; set; }

	/// <summary>
	/// Gets or sets a value indicating whether this broadcast alert is read or not.
	/// </summary>
	/// <value><c>true</c> if this broadcast alert is read; otherwise, <c>false</c>.</value>
	[JsonPropertyName("isRead")]
	public bool is_read { get; set; }

	/// <summary>
	/// Gets or sets the delivery status.
	/// </summary>
	/// <value>The delivery status.</value>
	[JsonPropertyName("deliveryStatus")]
	public char delivery_status { get; set; } = 'C';

	/// <summary>
	/// Gets or sets a value indicating whether this instance has error.
	/// </summary>
	/// <value><c>true</c> if this instance has error; otherwise, <c>false</c>.</value>
	[JsonPropertyName("hasError")]
	public bool has_error { get; set; } = false;

	/// <summary>
	/// Gets or sets the error.
	/// </summary>
	/// <value>The error.</value>
	[JsonPropertyName("error")]
	public string? error { get; set; }

	/// <summary>
	/// Gets or sets the relative link URL.
	/// </summary>
	/// <value>The relative link URL.</value>
	[JsonPropertyName("relLinkUrl")]
	public string? rel_link_url { get; set; }

	/// <summary>
	/// Gets or sets the deep link URL.
	/// </summary>
	/// <value>The deep link URL.</value>
	[JsonPropertyName("deepLinkUrl")]
	public string? deep_link_url { get; set; }
}

/// <summary>
/// Class BroadcastAlert.
/// </summary>
[Table("in_app_alerts")]
public partial class InAppAlert : BaseAlert
{
	/// <summary>
	/// Gets the subject.
	/// </summary>
	/// <value>The subject.</value>
	[JsonPropertyName("subject")]
	public string? subject { get; set; }

	/// <summary>
	/// Gets or sets the text body.
	/// </summary>
	/// <value>The textbody.</value>
	[JsonPropertyName("txtBody")]
	public string? txt_body { get; set; }

	/// <summary>
	/// Gets or sets the html body.
	/// </summary>
	/// <value>The htmlbody.</value>
	[JsonPropertyName("htmlBody")]
	public string? html_body { get; set; }

	/// <summary>
	/// Gets the attachments.
	/// </summary>
	/// <value>The attachments.</value>
	[JsonPropertyName("attachments")]
	public string? attachments { get; set; }

	/// <summary>
	/// Gets the data.
	/// </summary>
	/// <value>The data.</value>
	[JsonPropertyName("data")]
	public JsonDocument? data { get; set; }

	/// <summary>
	/// Gets or sets the provider.
	/// </summary>
	/// <value>The provider.</value>
	[JsonPropertyName("provider")]
	public string? provider { get; set; }

	/// <summary>
	/// Gets or sets the provider response.
	/// </summary>
	/// <value>The provider response.</value>
	[JsonPropertyName("providerResponse")]
	public string? provider_response { get; set; }
}

/// <summary>
/// The in app alert view class
/// </summary>
/// <seealso cref="InAppAlert"/>
[Table("in_app_alerts")]
public sealed partial class InAppAlertView : InAppAlert
{
	/// <summary>
	/// Gets the createdOn.
	/// </summary>
	/// <value>The createdOn.</value>
	[JsonPropertyName("createdOn")]
	public DateTime CreatedOn { get; set; }

	/// <summary>
	/// Gets the lastModifiedOn.
	/// </summary>
	/// <value>The lastModifiedOn.</value>
	[JsonPropertyName("lastModifiedOn")]
	public DateTime LastModifiedOn { get; set; }
}

/// <summary>
/// Class EmailAlert.
/// </summary>
[Table("email_alerts")]
public partial class EmailAlert : BaseAlert
{
	/// <summary>
	/// Gets the subject.
	/// </summary>
	/// <value>The subject.</value>
	[JsonPropertyName("subject")]
	public string? subject { get; set; }

	/// <summary>
	/// Gets the txt_body.
	/// </summary>
	/// <value>The txt_body.</value>
	[JsonPropertyName("txtBody")]
	public string? txt_body { get; set; }

	/// <summary>
	/// Gets the html_body.
	/// </summary>
	/// <value>The html_body.</value>
	[JsonPropertyName("htmlBody")]
	public string? html_body { get; set; }

	/// <summary>
	/// Gets the attachments.
	/// </summary>
	/// <value>The attachments.</value>
	[JsonPropertyName("attachments")]
	public string? attachments { get; set; }
}

/// <summary>
/// The in email alert view class
/// </summary>
[Table("email_alerts")]
public sealed partial class EmailAlertView : EmailAlert
{
	/// <summary>
	/// Gets the createdOn.
	/// </summary>
	/// <value>The createdOn.</value>
	[JsonPropertyName("createdOn")]
	public DateTime CreatedOn { get; set; }

	/// <summary>
	/// Gets the lastModifiedOn.
	/// </summary>
	/// <value>The lastModifiedOn.</value>
	[JsonPropertyName("lastModifiedOn")]
	public DateTime LastModifiedOn { get; set; }
}

/// <summary>
/// Class BroadcastAlert.
/// </summary>
[Table("broadcast_alerts")]
public sealed partial class BroadcastAlert : Common.Postgres.TenantAppScopedObjectWithId
{
	/// <summary>
	/// Gets the recipient user id.
	/// </summary>
	/// <value>The recipient roles.</value>
	[JsonPropertyName("userId")]
	public string? user_id { get; set; }

	/// <summary>
	/// Gets the subject.
	/// </summary>
	/// <value>The subject.</value>
	[JsonPropertyName("subject")]
	public string? subject { get; set; }

	/// <summary>
	/// Gets or sets the text body.
	/// </summary>
	/// <value>The textbody.</value>
	[JsonPropertyName("txtBody")]
	public string? txt_body { get; set; }

	/// <summary>
	/// Gets or sets the html body.
	/// </summary>
	/// <value>The htmlbody.</value>
	[JsonPropertyName("htmlBody")]
	public string? html_body { get; set; }

	/// <summary>
	/// Gets the attachments.
	/// </summary>
	/// <value>The attachments.</value>
	[JsonPropertyName("attachments")]
	public string? attachments { get; set; }

	/// <summary>
	/// Gets or sets the priority.
	/// </summary>
	/// <value>The priority.</value>
	[JsonPropertyName("priority")]
	public string? priority { get; set; }


	/// <summary>
	/// Gets or sets the definition.
	/// </summary>
	/// <value>The definition.</value>
	[JsonPropertyName("definition")]
	public AlertDefOverride definition { get; set; }
}
