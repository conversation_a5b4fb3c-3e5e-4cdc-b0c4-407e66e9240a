using Apex.Models;
using Apex.Services;
using MqChannels.Models;

namespace MqChannels.Services;

/// <summary>
/// class BellIconPreferenceService
/// </summary>
public sealed partial class BellIconPreferenceService
{
	/// <summary>
	/// Get org preference.
	/// </summary>
	/// <param name="orgId">The org identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;BellIconSettings&gt; representing the asynchronous operation.</returns>
	public async Task<BellIconSettings?> GetOrgPreferenceAsync(string orgId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
		=> PreferenceToBellIconSettings(await _orgManagementService.GetPreferenceAsync(new PreferenceScope(OrgId: orgId), MakePrefId(orgId), userSecurityInfo, cancellationToken));

	/// <summary>
	/// Upserts the org preference.
	/// </summary>
	/// <param name="orgId">The org identifier.</param>
	/// <param name="bellIconSettings">The bell icon settings.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;Microsoft.AspNetCore.Mvc.IActionResult&gt;.</returns>
	public Task UpsertOrgPreferenceAsync(string orgId, BellIconSettings bellIconSettings, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
		=> _orgManagementService.UpsertPreferenceAsync(new PreferenceScope(OrgId: orgId), BellIconSettingsToPreference(orgId, bellIconSettings), userSecurityInfo, cancellationToken);

	/// <summary>
	/// Deletes the org preference.
	/// </summary>
	/// <param name="orgId">The org identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	public Task DeleteOrgPreferenceAsync(string orgId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
		=> _orgManagementService.DeletePreferenceAsync(new PreferenceScope(OrgId: orgId), MakePrefId(orgId), userSecurityInfo, cancellationToken);

	/// <summary>
	/// Makes the preference identifier.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <returns>System.String.</returns>
	static string MakePrefId(string id) => $"bellicon_{id}";

	/// <summary>
	/// Bell icon settings to Preference.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="source">The source.</param>
	/// <returns>System.String.</returns>
	static Preference BellIconSettingsToPreference(string id, BellIconSettings source) => new(MakePrefId(id), source.ToKeyValuePairs().Select(kv => new PreferenceKeyValue(kv.Key, kv.Value)).ToArray());

	/// <summary>
	/// Preference to bell icon settings.
	/// </summary>
	/// <param name="source">The source.</param>
	/// <returns>Apex.Models.BellIconSettings.</returns>
	static BellIconSettings PreferenceToBellIconSettings(Preference? source) =>
		source?.Preferences?.Select(p => KeyValuePair.Create(p.Key, p.Value)).ToObject<BellIconSettings>() ?? BellIconSettings.Default;
}