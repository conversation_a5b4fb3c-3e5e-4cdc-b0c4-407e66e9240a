using Apex.Models;
using Json.Patch;
using MqChannels.Models;

namespace MqChannels.Services;

/// <summary>
/// Class SendInAppAlertResult. This class cannot be inherited.
/// </summary>
public sealed record SendAlertResponse(string MessageId, string? Id, string? ExternalId, bool Success, string? Error = null);

/// <summary>
/// The one signal provider service interface
/// </summary>

public interface IMessengerProviderService
{
	/// <summary>
	/// Gets the topic for user.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="userId">The user identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;Topic&gt;.</returns>
	Task<Topic> GetTopicForUser(string tenantId, string appId, string userId, CancellationToken cancellationToken = default);

	/// <summary>
	/// Sends the in app alert using the specified message
	/// </summary>
	/// <param name="message">The message</param>
	/// <param name="cancellationToken">The cancellation token</param>
	/// <returns>A task containing the bool message nack</returns>
	Task<SendAlertResponse> SendInAppAlertAsync(InAppAlert message,
		CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the has unseen status using the specified tenant id
	/// </summary>
	/// <param name="tenantId">The tenant id</param>
	/// <param name="appId">The app id</param>
	/// <param name="userId">The user id</param>
	/// <param name="incrementCount">whether to increment the count, if you are fetching value before incr</param>
	/// <param name="cancellationToken">The cancellation token</param>
	/// <returns>A task containing the bool</returns>
	Task<AlertStatus> GetHasUnseenStatus(string tenantId, string appId, string userId,bool incrementCount = false,
		CancellationToken cancellationToken = default);

	/// <summary>
	/// Sets the has unseen status using the specified user security info
	/// </summary>
	/// <param name="userSecurityInfo">The user security info</param>
	/// <param name="cancellationToken">The cancellation token</param>
	/// <returns>A task containing the bool</returns>
	public Task<bool> IncrementUnseenStatus(UserSecurityInfo userSecurityInfo,
		CancellationToken cancellationToken = default);

	/// <summary>
	/// Reset the unseen status.
	/// </summary>
	/// <param name="tenantId">The tenant id</param>
	/// <param name="appId">The app id</param>
	/// <param name="userId">The user id</param>
	/// <param name="alertStatusPatch">The alert status patch.</param>
	/// <param name="cancellationToken">The cancellation token</param>
	/// <returns>A task containing the bool</returns>
	Task<AlertStatus> ResetHasUnseenStatus(string tenantId, string appId, string userId,
		JsonPatch alertStatusPatch, CancellationToken cancellationToken = default);
}