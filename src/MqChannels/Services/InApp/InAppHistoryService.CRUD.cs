using Common.Postgres;
using Dapper;
using MqChannels.Models;

namespace MqChannels.Services;

/// <summary>
/// Class UpsertResult.
/// </summary>
public sealed record UpsertResult(int Op);

/// <summary>
/// Class InAppHistoryService.
/// </summary>
public sealed partial class InAppHistoryService
{
	/// <summary>
	/// Gets the In app alerts.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="filter">The filter.</param>
	/// <param name="deliveryStatus">The delivery status.</param>
	/// <param name="isRead">if set to <c>true</c> [is read].</param>
	/// <param name="isAppSpecific">if set to <c>true</c> [is application specific].</param>
	/// <returns>Task&lt;IEnumerable&lt;MqInAppAlert&gt;&gt;.</returns>
	public Task<List<InAppAlertView?>> GetAllInAppAlertsAsync(string tenantId, string appId,
		uint pageIndex = 1, uint pageSize = 100, string? filter = null, char? deliveryStatus = null, bool? isRead = null, bool? isAppSpecific = null)
		=> GetInAppAlertsAsync(new TenantAppScopedObject { tenant_id = tenantId, app_id = appId }, userId: null, pageIndex, pageSize, filter, deliveryStatus, isRead, isAppSpecific);

	/// <summary>
	/// Gets the In app alerts by type.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="userId">The type.</param>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="filter">The filter.</param>
	/// <param name="deliveryStatus">The delivery status.</param>
	/// <param name="isRead">if set to <c>true</c> [is read].</param>
	/// <param name="isAppSpecific">if set to <c>true</c> [is application specific].</param>
	/// <returns>Task&lt;MqInAppAlert&gt;.</returns>
	public Task<List<InAppAlertView?>> GetInAppAlertsByUserAsync(string tenantId, string appId, string userId,
		uint pageIndex = 1, uint pageSize = 100, string? filter = null, char? deliveryStatus = null, bool? isRead = null, bool? isAppSpecific = null)
		=> GetInAppAlertsAsync(new TenantAppScopedObject { tenant_id = tenantId, app_id = appId }, userId, pageIndex, pageSize, filter, deliveryStatus, isRead, isAppSpecific);

	/// <summary>
	/// Gets the in application alerts.
	/// </summary>
	/// <param name="tasObject">The tas object.</param>
	/// <param name="userId">The user identifier.</param>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="filter">The filter.</param>
	/// <param name="deliveryStatus">The delivery status.</param>
	/// <param name="isRead">The is read.</param>
	/// <param name="isAppSpecific">The is application specific.</param>
	/// <returns>System.Threading.Tasks.Task&lt;System.Collections.Generic.List&lt;MqChannels.Models.InAppAlertView&gt;&gt;.</returns>
	private Task<List<InAppAlertView?>> GetInAppAlertsAsync(TenantAppScopedObject tasObject, string? userId = null,
			uint pageIndex = 1, uint pageSize = 100, string? filter = null, char? deliveryStatus = null,
			bool? isRead = null, bool? isAppSpecific = null)
	{
		var tenantId = tasObject.tenant_id;
		var appId = tasObject.app_id;
		var limit = (int)pageSize;
		var offset = (int)((pageIndex - 1) * pageSize);
		var parameters = new DynamicParameters();
		parameters.AddDynamicParams(
			new
			{
				tenantId,
				appId,
				userId,
				limit,
				offset,
				filter,
				deliveryStatus,
				isRead = isRead.GetValueOrDefault(false),
			}
		);

		var query = $$"""SELECT * FROM in_app_alerts WHERE tenant_id=@tenantId""";
		if (isAppSpecific.GetValueOrDefault(false))
		{
			query += $$""" AND app_id=@appId""";
		}
		if (!userId.IsNullOrWhiteSpace())
		{
			_logger.LogInformation("ALERTS REQUESTED FOR TENANT - {TenantId},  USER - {UserId}",tenantId, userId);
			query += $$""" AND user_id=@userId""";
			parameters.Add(userId);
		}
		if (!filter.IsNullOrWhiteSpace())
		{
			query += $$""" AND search_vector @@ plainto_tsquery('simple', @filter)""";
		}
		if (deliveryStatus is not null)
		{
			query += $$""" AND delivery_status=@deliveryStatus""";
		}
		if (isRead is not null)
		{
			query += $$""" AND is_read=@isRead""";
		}

		query += $$""" ORDER BY created_on DESC LIMIT @limit OFFSET @offset""";
		_logger.LogInformation("EXECUTING QUERY - {Query} , with parameters - {Params}", query, parameters.ToJSON());
		return _pgRepository.QueryAsync<InAppAlertView>(query,
			param: parameters);
	}

	/// <summary>
	/// Gets the In app alert by identifier.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="id">The identifier.</param>
	/// <returns>Task&lt;MqInAppAlert&gt;.</returns>
	public Task<InAppAlertView?> GetInAppAlertByIdAsync(string tenantId, string appId, string id)
		=> _pgRepository.GetByIdAsync<InAppAlertView>(new TenantAppScopedObjectWithId { tenant_id = tenantId, app_id = appId, id = id });

	/// <summary>
	/// Inserts/Updates the In app alert.
	/// </summary>
	/// <param name="instance">The In app alert.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	public async Task<int> UpsertInAppAlertAsync(InAppAlert instance)
	{
		var results = await _pgRepository.QueryAsync<UpsertResult>(UPSERT_INAPPNOTIFICATION, instance);
		return results is { Count: > 0 } && results[0] is UpsertResult result ? result.Op : 0;
	}

	const string UPSERT_INAPPNOTIFICATION =
	"""
	INSERT INTO "in_app_alerts"
	(
		"tenant_id", "app_id", "id", "user_id", "def_id", "subject", "txt_body", "html_body", "attachments", "data", "priority", "is_read", "delivery_status", "provider", "provider_response", "rel_link_url", "deep_link_url"
	)
	VALUES
	(
		@tenant_id, @app_id, @id, @user_id, @def_id, @subject, @txt_body, @html_body, @attachments, @data, @priority, @is_read, @delivery_status, @provider, @provider_response, @rel_link_url, @deep_link_url
	)
	ON CONFLICT ("tenant_id", "app_id", "id")
	DO NOTHING
	RETURNING 1 as "Op";
	""";

	/// <summary>
	/// Updates the In app alert delivery status.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="status">The status.</param>
	/// <param name="error">The error.</param>
	/// <param name="provider">The provider.</param>
	/// <param name="providerResponse">The provider response.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	public Task<int> UpdateInAppAlertDeliveryStatusAsync(TenantAppScopedObjectWithId id, DeliveryStatus status, string? error, string? provider, string? providerResponse)
		=> _pgRepository.ExecuteNonQueryAsync(UPDATE_INAPPNOTIFICATION_STATUS, new
		{
			id.tenant_id,
			id.app_id,
			id.id,
			delivery_status = status.ToString(),
			has_error = !error.IsNullOrEmpty(),
			error,
			provider,
			provider_response = providerResponse
		});

	const string UPDATE_INAPPNOTIFICATION_STATUS =
	"""
	UPDATE "in_app_alerts"
	SET delivery_status=@delivery_status, has_error=@has_error, error=@error, provider=@provider, provider_response=@provider_response
	WHERE tenant_id=@tenant_id AND app_id=@app_id AND id=@id
	""";

	/// <summary>
	/// Updates the In app alert read status.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="isRead">The is read.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	public Task<int> MarkAsReadOrUnreadInAppAlertAsync(TenantAppScopedObjectWithId id, bool isRead = true)
		=> _pgRepository.ExecuteNonQueryAsync(UPDATE_INAPPNOTIFICATION_ISREAD, new
		{
			id.tenant_id,
			id.id,
			is_read = isRead,
		});

	const string UPDATE_INAPPNOTIFICATION_ISREAD =
	"""
	UPDATE "in_app_alerts"
	SET "is_read"=@is_read
	WHERE
		tenant_id=@tenant_id AND id=@id
	""";

	/// <summary>
	/// Updates the In app alert read status as read.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="userId">The user identifier.</param>
	/// <param name="isAppSpecific"></param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	public Task<int> MarkAllAsReadInAppAlertAsync(string tenantId, string appId, string userId,
		bool? isAppSpecific = null)
	{
		var query = UPDATE_INAPPNOTIFICATION_ASREAD;
		if (isAppSpecific.GetValueOrDefault(false))
		{
			query += $$""" AND app_id=@appId""";
		}
		return _pgRepository.ExecuteNonQueryAsync(query, new
		{
			tenant_id = tenantId,
			app_id = appId,
			user_id = userId,
		});
	}

	const string UPDATE_INAPPNOTIFICATION_ASREAD =
	"""
	UPDATE "in_app_alerts"
	SET "is_read"=true
	WHERE
		tenant_id=@tenant_id AND user_id=@user_id AND is_read=false AND delivery_status='D'
	""";



	/// <summary>
	/// Deletes the In app alert.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="id">The identifier.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	public Task<int> DeleteInAppAlertByIdAsync(string tenantId, string appId, string id)
	{
		const string deleteInappAlert = 
			"""
			DELETE FROM "in_app_alerts"
			WHERE tenant_id = @tenant_id AND id = @id
			""";

		return _pgRepository.ExecuteNonQueryAsync(deleteInappAlert, new
		{
			tenant_id = tenantId,
			id = id
		});
	}

	/// <summary>
	/// Deletes the In app alerts in application.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <returns>Task&lt;System.Int32&gt;.</returns>
	public Task<int> DeleteInAppAlertInAppAsync(string tenantId, string appId)
		=> _pgRepository.DeleteAllAsync<InAppAlert>(new TenantAppScopedObject { tenant_id = tenantId, app_id = appId });

	/// <summary>
	/// Deletes alerts for a user.
	/// </summary>
	/// <param name="tenantId"></param>
	/// <param name="userId"></param>
	/// <returns></returns>
	public Task<int> DeleteInAppAlertsByUserAsync(string tenantId, string userId)
	{
		const string deleteInappAlertsByUser = 
			"""
			DELETE FROM "in_app_alerts"
			WHERE tenant_id = @tenant_id AND user_id = @user_id
			""";

		return _pgRepository.ExecuteNonQueryAsync(deleteInappAlertsByUser, new
		{
			tenant_id = tenantId,
			user_id = userId
		});
	}

	/// <summary>
	/// Queries the In app alerts.
	/// </summary>
	/// <param name="sql">The SQL.</param>
	/// <param name="param">The parameter.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	public Task<List<InAppAlert?>> QueryInAppAlertsAsync(string sql, object? param)
		=> _pgRepository.QueryAsync<InAppAlert>(sql, param);
}