using Common.Postgres;
using MqChannels.Models;

namespace MqChannels.Services;

/// <summary>
/// Interface IInAppHistoryService
/// </summary>
public partial interface IInAppHistoryService
{
	/// <summary>
	/// Gets all In app alerts.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="filter">The filter.</param>
	/// <param name="deliveryStatus">The delivery status.</param>
	/// <param name="isRead">if set to <c>true</c> [is read].</param>
	/// <param name="isAppSpecific">if set to <c>true</c> [is application specific].</param>
	/// <returns>Task&lt;List&lt;MqInAppAlert&gt;&gt;.</returns>
	Task<List<InAppAlertView?>> GetAllInAppAlertsAsync(string tenantId, string appId,
		uint pageIndex = 1, uint pageSize = 100, string? filter = null, char? deliveryStatus = null, bool? isRead = null, bool? isAppSpecific = null);

	/// <summary>
	/// Gets the In app alert by identifier.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="id">The identifier.</param>
	/// <returns>Task&lt;MqInAppAlert&gt;.</returns>
	Task<InAppAlertView?> GetInAppAlertByIdAsync(string tenantId, string appId, string id);

	/// <summary>
	/// Gets the In app alerts by type asynchronous.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="userId">The type.</param>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="filter">The filter.</param>
	/// <param name="deliveryStatus">The delivery status.</param>
	/// <param name="isRead">if set to <c>true</c> [is read].</param>
	/// <param name="isAppSpecific">if set to <c>true</c> [is application specific].</param>
	/// <returns>Task&lt;MqInAppAlert&gt;.</returns>
	Task<List<InAppAlertView?>> GetInAppAlertsByUserAsync(string tenantId, string appId, string userId,
		uint pageIndex = 1, uint pageSize = 100, string? filter = null, char? deliveryStatus = null, bool? isRead = null, bool? isAppSpecific = null);

	/// <summary>
	/// Upsert the In app alert.
	/// </summary>
	/// <param name="instance">The instance.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	Task<int> UpsertInAppAlertAsync(InAppAlert instance);

	/// <summary>
	/// Updates the in application alert delivery status.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="status">The status.</param>
	/// <param name="error">The error.</param>
	/// <param name="provider">The provider.</param>
	/// <param name="providerResponse">The provider response.</param>
	/// <returns>Task&lt;System.Int32&gt;.</returns>
	Task<int> UpdateInAppAlertDeliveryStatusAsync(TenantAppScopedObjectWithId id, DeliveryStatus status, string? error, string? provider, string? providerResponse);

	/// <summary>
	/// Marks as read or unread in application alert.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="isRead">if set to <c>true</c> [is read].</param>
	/// <returns>Task&lt;System.Int32&gt;.</returns>
	Task<int> MarkAsReadOrUnreadInAppAlertAsync(TenantAppScopedObjectWithId id, bool isRead = true);

	/// <summary>
	/// Marks all as read in application alert.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="userId">The user identifier.</param>
	/// <param name="isAppSpecific"></param>
	/// <returns>Task&lt;System.Int32&gt;.</returns>
	Task<int> MarkAllAsReadInAppAlertAsync(string tenantId, string appId, string userId, bool? isAppSpecific = null);

	/// <summary>
	/// Deletes the In app alert by identifier.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="id">The identifier.</param>
	/// <returns>Task&lt;System.Int32&gt;.</returns>
	Task<int> DeleteInAppAlertByIdAsync(string tenantId, string appId, string id);

	/// <summary>
	/// Deletes the In app alert in application.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <returns>Task&lt;System.Int32&gt;.</returns>
	Task<int> DeleteInAppAlertInAppAsync(string tenantId, string appId);
	
	/// <summary>
	/// Deletes alerts for a user
	/// </summary>
	/// <param name="tenantId"></param>
	/// <param name="userId"></param>
	/// <returns></returns>
	Task<int> DeleteInAppAlertsByUserAsync(string tenantId, string userId);
	
	/// <summary>
	/// Queries the In app alerts.
	/// </summary>
	/// <param name="sql">The SQL.</param>
	/// <param name="param">The parameter.</param>
	/// <returns>Task&lt;List&lt;MqInAppAlert&gt;&gt;.</returns>
	Task<List<InAppAlert?>> QueryInAppAlertsAsync(string sql, object? param);
}