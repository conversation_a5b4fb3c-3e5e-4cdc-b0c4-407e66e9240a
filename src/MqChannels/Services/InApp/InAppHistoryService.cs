using Common.Postgres;
using Dapper;
using System.Text.Json;

namespace MqChannels.Services;

/// <summary>
/// Class InAppHistoryService.
/// </summary>
public sealed partial class InAppHistoryService(ILogger<InAppHistoryService> _logger, IPgRepository2 _pgRepository) : IInAppHistoryService
{
	/// <summary>
	/// Initializes static members of the <see cref="InAppHistoryService"/> class.
	/// </summary>
	static InAppHistoryService()
	{
		SqlMapper.AddTypeHandler(new JsonTypeHandler<JsonDocument>());
	}
}