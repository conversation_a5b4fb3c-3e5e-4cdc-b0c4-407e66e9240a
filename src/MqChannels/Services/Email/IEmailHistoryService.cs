using Common.Postgres;
using MqChannels.Models;

namespace MqChannels.Services;

/// <summary>
/// Interface IEmailHistoryService
/// </summary>
public partial interface IEmailHistoryService
{
	/// <summary>
	/// Gets all email alerts.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <returns>Task&lt;List&lt;MqEmailAlert&gt;&gt;.</returns>
	Task<List<InAppAlertView?>> GetAllEmailAlertsAsync(string tenantId, string appId);

	/// <summary>
	/// Gets the email alert by identifier.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="id">The identifier.</param>
	/// <returns>Task&lt;MqEmailAlert&gt;.</returns>
	Task<InAppAlertView?> GetEmailAlertByIdAsync(string tenantId, string appId, string id);

	/// <summary>
	/// Gets the email alerts by type asynchronous.
	/// </summary>
	/// <param name="tasObject">The tas object.</param>
	/// <param name="userId">The type.</param>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="filter">The filter.</param>
	/// <param name="deliveryStatus">The delivery status.</param>
	/// <returns>Task&lt;MqEmailAlert&gt;.</returns>
	Task<List<EmailAlertView?>> GetEmailAlertsByUserAsync(TenantAppScopedObject tasObject, string? userId = null,
		uint pageIndex = 1, uint pageSize = 100, string? filter = null, char? deliveryStatus = null);

	/// <summary>
	/// Upsert the email alert.
	/// </summary>
	/// <param name="instance">The instance.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	Task<int> UpsertEmailAlertAsync(EmailAlert instance);

	/// <summary>
	/// Updates the email alert delivery status.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="status">The status.</param>
	/// <param name="error">The error.</param>
	/// <param name="provider">The provider.</param>
	/// <param name="providerResponse">The provider response.</param>
	/// <returns>Task&lt;System.Int32&gt;.</returns>
	Task<int> UpdateEmailAlertDeliveryStatusAsync(TenantAppScopedObjectWithId id, DeliveryStatus status, string? error, string? provider, string? providerResponse);

	/// <summary>
	/// Deletes the email alert by identifier.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="id">The identifier.</param>
	/// <returns>Task&lt;System.Int32&gt;.</returns>
	Task<int> DeleteEmailAlertByIdAsync(string tenantId, string appId, string id);

	/// <summary>
	/// Deletes the email alert email.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <returns>Task&lt;System.Int32&gt;.</returns>
	Task<int> DeleteEmailAlertInAppAsync(string tenantId, string appId);
}