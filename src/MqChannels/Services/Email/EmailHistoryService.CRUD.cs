using Common.Postgres;
using Dapper;
using MqChannels.Models;

namespace MqChannels.Services;

/// <summary>
/// Class EmailHistoryService.
/// </summary>
public sealed partial class EmailHistoryService
{
	/// <summary>
	/// Gets the email alerts.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <returns>Task&lt;IEnumerable&lt;MqEmailAlert&gt;&gt;.</returns>
	public Task<List<InAppAlertView?>> GetAllEmailAlertsAsync(string tenantId, string appId)
		=> _pgRepository.GetAllAsync<InAppAlertView>(new TenantAppScopedObject { tenant_id = tenantId, app_id = appId });

	/// <summary>
	/// Gets the email alerts by user.
	/// </summary>
	/// <param name="tasObject">The tas object.</param>
	/// <param name="userId">The type.</param>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="filter">The filter.</param>
	/// <param name="deliveryStatus">The delivery status.</param>
	/// <returns>Task&lt;MqEmailAlert&gt;.</returns>
	public Task<List<EmailAlertView?>> GetEmailAlertsByUserAsync(TenantAppScopedObject tasObject, string? userId = null,
			uint pageIndex = 1, uint pageSize = 100, string? filter = null, char? deliveryStatus = null)
	{
		var tenantId = tasObject.tenant_id;
		var appId = tasObject.app_id;
		var limit = (int)pageSize;
		var offset = (int)((pageIndex - 1) * pageSize);
		var parameters = new DynamicParameters();
		parameters.AddDynamicParams(
			new
			{
				tenantId,
				appId,
				userId,
				limit,
				offset
			}
		);

		var query = $$"""SELECT * FROM email_alerts WHERE tenant_id=@tenantId AND app_id=@appId""";
		if (!userId.IsNullOrWhiteSpace())
		{
			query += $$""" AND user_id='{{userId}}'""";
			parameters.Add(userId);
		}
		if (!filter.IsNullOrWhiteSpace())
		{
			query += $$""" AND (subject ILIKE '%{{filter}}%' OR txt_body ILIKE '%{{filter}}%')""";
		}
		if (deliveryStatus is not null)
		{
			query += $$""" AND delivery_status='{{deliveryStatus}}'""";
		}

		query += $$""" ORDER BY created_on DESC LIMIT @limit OFFSET @offset""";
		_logger.LogInformation("EXECUTING QUERY - {Query}, with parameters - {Params}", query, parameters.ToJSON());
		return _pgRepository.QueryAsync<EmailAlertView>(query,
			param: parameters);
	}

	/// <summary>
	/// Gets the email alert by identifier.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="id">The identifier.</param>
	/// <returns>Task&lt;MqEmailAlert&gt;.</returns>
	public Task<InAppAlertView?> GetEmailAlertByIdAsync(string tenantId, string appId, string id)
		=> _pgRepository.GetByIdAsync<InAppAlertView>(new TenantAppScopedObjectWithId { tenant_id = tenantId, app_id = appId, id = id });

	/// <summary>
	/// Inserts/Updates the email alert.
	/// </summary>
	/// <param name="instance">The email alert.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	public async Task<int> UpsertEmailAlertAsync(EmailAlert instance)
	{
		var results = await _pgRepository.QueryAsync<UpsertResult>(UPSERT_EMAILNOTIFICATION, instance);
		return results is { Count: > 0 } && results[0] is UpsertResult result ? result.Op : 0;
	}

	const string UPSERT_EMAILNOTIFICATION =
	"""
	INSERT INTO "email_alerts"
	(
		"tenant_id", "app_id", "id", "user_id", "def_id", "subject", "txt_body", "html_body", "attachments", "priority", "is_read", "delivery_status", "provider", "provider_response", "rel_link_url", "deep_link_url"
	)
	VALUES
	(
		@tenant_id, @app_id, @id, @user_id, @def_id, @subject, @txt_body, @html_body, @attachments, @priority, @is_read, @delivery_status, @provider, @provider_response, @rel_link_url, @deep_link_url
	)
	ON CONFLICT ("tenant_id", "app_id", "id")
	DO NOTHING
	RETURNING 1 as "Op";
	""";

	/// <summary>
	/// Updates the email alert delivery status.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="status">The status.</param>
	/// <param name="error">The error.</param>
	/// <param name="provider">The provider.</param>
	/// <param name="providerResponse">The provider response.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	public Task<int> UpdateEmailAlertDeliveryStatusAsync(TenantAppScopedObjectWithId id, DeliveryStatus status, string? error, string? provider, string? providerResponse)
		=> _pgRepository.ExecuteNonQueryAsync(UPDATE_EMAILNOTIFICATION_STATUS, new
		{
			id.tenant_id,
			id.app_id,
			id.id,
			delivery_status = status.ToString(),
			has_error = !error.IsNullOrEmpty(),
			error,
			provider,
			provider_response = providerResponse
		});

	const string UPDATE_EMAILNOTIFICATION_STATUS =
	"""
	UPDATE "email_alerts"
	SET delivery_status=@delivery_status, has_error=@has_error, error=@error, provider=@provider, provider_response=@provider_response
	WHERE tenant_id=@tenant_id AND app_id=@app_id AND id=@id
	""";

	/// <summary>
	/// Deletes the email alert.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="id">The identifier.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	public Task<int> DeleteEmailAlertByIdAsync(string tenantId, string appId, string id)
		=> _pgRepository.DeleteByIdAsync<EmailAlert>(new TenantAppScopedObjectWithId { tenant_id = tenantId, app_id = appId, id = id });

	/// <summary>
	/// Deletes the email alerts in application.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <returns>Task&lt;System.Int32&gt;.</returns>
	public Task<int> DeleteEmailAlertInAppAsync(string tenantId, string appId)
		=> _pgRepository.DeleteAllAsync<EmailAlert>(new TenantAppScopedObject { tenant_id = tenantId, app_id = appId });
}