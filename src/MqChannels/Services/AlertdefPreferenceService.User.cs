using Apex.Models;
using Apex.Services;

namespace MqChannels.Services;

/// <summary>
/// class AlertdefPreferenceService
/// </summary>
public sealed partial class AlertdefPreferenceService
{
	/// <summary>
	/// Get user org preference key values.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;AlertDefOverride&gt; representing the asynchronous operation.</returns>
	public async Task<AlertDefOverride?> GetUserOrgPreferenceAsync(string id, string iamUserId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
		=> PreferenceToAlert(new Preference(id, await _orgManagementService.GetMergedPreferenceKeyValuesAsync(new PreferenceScope(UserId: iamUserId), MakePrefId(id), userSecurityInfo, cancellationToken)));

	/// <summary>
	/// Gets the user preference.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;Apex.Models.AlertDefOverride&gt;.</returns>
	public async Task<AlertDefOverride?> GetUserPreferenceAsync(string id, string iamUserId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
		=> PreferenceToAlert(await _orgManagementService.GetPreferenceAsync(new PreferenceScope(UserId: iamUserId), MakePrefId(id), userSecurityInfo, cancellationToken));

	/// <summary>
	/// Inserts/Updates the user preference.
	/// </summary>
	/// <param name="id">The alert definition identifier.</param>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="alertDef">The alert definition.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	public Task UpsertUserPreferenceAsync(string id, string iamUserId, AlertDefOverride alertDef, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
		=> _orgManagementService.UpsertPreferenceAsync(new PreferenceScope(UserId: iamUserId), AlertToPreference(id, alertDef), userSecurityInfo, cancellationToken);

	/// <summary>
	/// Deletes the user preference.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task.</returns>
	public Task DeleteUserPreferenceAsync(string id, string iamUserId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
		=> _orgManagementService.DeletePreferenceAsync(new PreferenceScope(UserId: iamUserId), MakePrefId(id), userSecurityInfo, cancellationToken);
}