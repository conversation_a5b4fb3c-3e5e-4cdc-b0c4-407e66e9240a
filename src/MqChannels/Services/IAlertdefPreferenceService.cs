using Apex.Models;

namespace MqChannels.Services;

/// <summary>
/// Interface IAlertdefPreferenceService
/// </summary>
public partial interface IAlertdefPreferenceService
{
	/// <summary>
	/// Get org preference.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="orgId">The org identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;AlertDefOverride&gt; representing the asynchronous operation.</returns>
	Task<AlertDefOverride?> GetOrgPreferenceAsync(string id, string orgId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Upserts the org preference.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="orgId">The org identifier.</param>
	/// <param name="alertDef">The alert definition.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;Microsoft.AspNetCore.Mvc.IActionResult&gt;.</returns>
	Task UpsertOrgPreferenceAsync(string id, string orgId, AlertDefOverride alertDef, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Deletes the org preference.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="orgId">The org identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	Task DeleteOrgPreferenceAsync(string id, string orgId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Get user org preference key values.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;AlertDefOverride&gt; representing the asynchronous operation.</returns>
	Task<AlertDefOverride?> GetUserOrgPreferenceAsync(string id, string iamUserId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Gets the user preference.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;Apex.Models.AlertDefOverride&gt;.</returns>
	Task<AlertDefOverride?> GetUserPreferenceAsync(string id, string iamUserId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Inserts/Updates the user preference.
	/// </summary>
	/// <param name="id">The alert definition identifier.</param>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="alertDef">The alert definition.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	Task UpsertUserPreferenceAsync(string id, string iamUserId, AlertDefOverride alertDef, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Deletes the user preference.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task.</returns>
	Task DeleteUserPreferenceAsync(string id, string iamUserId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);
}