using Apex.Options;
using Apex.Services;
using Microsoft.Extensions.Options;

namespace MqChannels.Services;

/// <summary>
/// class WorkflowChannelTypeHandler
/// </summary>
public sealed class WorkflowChannelTypeHandler(ILogger<WorkflowChannelTypeHandler> _logger, IOptions<ServiceOptions> _serviceOptions,
	IJiffyDriveRestService _jiffyDriveService, IOrgManagementService _orgManagementService)
	: BaseChannelTypeHandler(_logger, _serviceOptions, _jiffyDriveService, _orgManagementService)
{
}