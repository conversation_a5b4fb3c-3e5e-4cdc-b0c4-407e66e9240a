using Apex.Models;
using Apex.Options;
using Apex.Services;
using Microsoft.Extensions.Options;

namespace MqChannels.Services;

/// <summary>
/// class BaseChannelTypeHandler
/// </summary>
public abstract class BaseChannelTypeHandler(ILogger _logger, IOptions<ServiceOptions> _serviceOptions, IJiffyDriveRestService _jiffyDriveService, IOrgManagementService _orgManagementService)
	: IChannelTypeHandler
{
	/// <summary>
	/// Processes the message.
	/// </summary>
	/// <param name="message">The message.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.ValueTuple&lt;System.Boolean, MessageNack&gt;&gt;.</returns>
	public virtual async Task<(bool, MessageNack)> ProcessMessageAsync(Message message, CancellationToken cancellationToken = default)
	{
		_logger.LogInformation("Processing channel queue message - Type={Type} MessageId={MessageId}", GetType(), message.Id);
		try
		{
			//var forwardedMessage =

			return MessageAcknowledgement.SUCCESS;
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Processing channel queue failed - Type={Type} MessageId={MessageId} Error={Error}",
				GetType(), message.Id, ex.Message);
			return MessageAcknowledgement.FAIL_REQUEUE;
		}
	}

	/// <summary>
	/// Resolves the expression to user ids asynchronous.
	/// </summary>
	/// <param name="rawMessage">The raw message.</param>
	/// <param name="usersExpression">The users expression.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;System.Collections.Generic.IList&lt;System.String&gt;&gt;.</returns>
	protected async Task<ISet<string>> ResolveExpressionToUserIdsAsync(Message rawMessage, string? usersExpression, CancellationToken cancellationToken = default)
	{
		var result = new HashSet<string>();
		return result;
	}

	/// <summary>
	/// Resolves the orgs to users.
	/// </summary>
	/// <param name="targetOrgs">The target roles.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="messageId">The message identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;HashSet&lt;System.String&gt;&gt;.</returns>
	protected async Task<HashSet<string>> ResolveOrgsToUsersAsync(string[]? targetOrgs, UserSecurityInfo userSecurityInfo, string messageId, CancellationToken cancellationToken)
	{
		HashSet<string> allUserIds = new(StringComparer.OrdinalIgnoreCase);
		if (targetOrgs is not null)
		{
			foreach (var targetOrgId in targetOrgs)
			{
				_logger.LogWarning("InApp template - Getting org users - MessageId={MessageId} OrgId={OrgId}",
					messageId, targetOrgId);

				await foreach (var users in _orgManagementService.GetAllUsersForOrgAsync(targetOrgId, userSecurityInfo, cancellationToken))
				{
					allUserIds.UnionWith(users.Select(u => u.IamUserId));
				}
				_logger.LogWarning("InApp template - Got org users - MessageId={MessageId} OrgId={OrgId} Users#={Users}",
					messageId, targetOrgId, allUserIds.Count);
			}
		}
		return allUserIds;
	}

	/// <summary>
	/// Resolves the roles to users.
	/// </summary>
	/// <param name="targetRoles">The target roles.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="messageId">The message identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;HashSet&lt;System.String&gt;&gt;.</returns>
	protected async Task<HashSet<string>> ResolveRolesToUsersAsync(string[]? targetRoles, UserSecurityInfo userSecurityInfo, string messageId, CancellationToken cancellationToken)
	{
		HashSet<string> allUserIds = new(StringComparer.OrdinalIgnoreCase);
		if (targetRoles is not null)
		{
			foreach (var targetRoleId in targetRoles)
			{
				_logger.LogWarning("InApp template - Getting role users - MessageId={MessageId} TargetRoleId={TargetRoleId}",
					messageId, targetRoleId);

				await foreach (var users in _orgManagementService.GetAllUsersForRoleAsync(targetRoleId, userSecurityInfo, cancellationToken))
				{
					allUserIds.UnionWith(users.Select(u => u.Id));
				}
				_logger.LogWarning("InApp template - Got org users - MessageId={MessageId} TargetRoleId={TargetRoleId} Users#={Users}",
					messageId, targetRoleId, allUserIds.Count);
			}
		}
		return allUserIds;
	}

	/// <summary>
	/// Gets the template information.
	/// </summary>
	/// <param name="templateId">The template identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;MqChannels.Models.TemplateInfo&gt;.</returns>
	/// <exception cref="MqChannelException">template was found, but did not have any contents - TemplateWithFolderPath={templateWithFolderPath}</exception>
	/// <exception cref="MqChannelException">template was found with some contents, but did not look like template info - TemplateWithFolderPath={templateWithFolderPath}</exception>
	protected async Task<TemplateInfo> GetTemplateInfoAsync(string templateId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken)
	{
		var templateWithFolderPath = $"{_jiffyDriveService.ToApplicationPrivateBucketName()}/templates/{templateId}";
		_logger.LogInformation("trying to get the template content - TemplateWithFolderPath={TemplateWithFolderPath}", templateWithFolderPath);

		var templateTransform = await _jiffyDriveService.GetFileAsTextAsync(templateWithFolderPath, userSecurityInfo, cancellationToken);
		if (templateTransform.IsNullOrEmpty())
		{
			throw new MqChannelException($"template was found, but did not have any contents - TemplateWithFolderPath={templateWithFolderPath}");
		}

		return templateTransform.ToObject<TemplateInfo>()
			?? throw new MqChannelException($"template was found with some contents, but did not look like template info - TemplateWithFolderPath={templateWithFolderPath}");
	}
}