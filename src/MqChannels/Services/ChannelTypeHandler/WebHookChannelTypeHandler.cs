using Apex.Options;
using Apex.Services;
using Microsoft.Extensions.Options;

namespace MqChannels.Services;

/// <summary>
/// class WebHookChannelTypeHandler
/// </summary>
public sealed class WebHookChannelTypeHandler(ILogger<WebHookChannelTypeHandler> _logger, IOptions<ServiceOptions> _serviceOptions,
	IJiffyDriveRestService _jiffyDriveService, IOrgManagementService _orgManagementService)
	: BaseChannelTypeHandler(_logger, _serviceOptions, _jiffyDriveService, _orgManagementService)
{
}