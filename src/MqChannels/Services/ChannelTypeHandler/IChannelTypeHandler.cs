using Apex.Models;
using Apex.Services;

namespace MqChannels.Services;

/// <summary>
/// Interface IChannelTypeHandler
/// </summary>
public partial interface IChannelTypeHandler
{
	/// <summary>
	/// Processes the message.
	/// </summary>
	/// <param name="message">The message.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.ValueTuple&lt;System.Boolean, MessageNack&gt;&gt;.</returns>
	Task<(bool, MessageNack)> ProcessMessageAsync(Message message, CancellationToken cancellationToken = default);
}