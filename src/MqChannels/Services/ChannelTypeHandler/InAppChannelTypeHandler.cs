using Apex.Models;
using Apex.Options;
using Apex.Services;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using MqChannels.Models;
using MqChannels.Utils;
using System.Text.Json;
using System.Text.Json.Nodes;

namespace MqChannels.Services;

#pragma warning disable S3776

/// <summary>
/// class InAppChannelTypeHandler
/// </summary>
public sealed class InAppChannelTypeHandler(ILogger<InAppChannelTypeHandler> _logger, IOptions<ServiceOptions> _serviceOptions,
	IMqSendService _mqSendService,
	IOrgManagementService _orgManagementService,
	IAlertdefPreferenceService _alertdefPreferenceService,
	IInAppHistoryService _historyService, IJiffyDriveRestService _jiffyDriveService, ITokenGenerationService _tokenGenerationService,
	ITemplateProcessorRestService _templateProcessor, IAppManagerService _appManagerService, IMemoryCache _memoryCache)
	: BaseChannelTypeHand<PERSON>(_logger, _serviceOptions, _jiffyDriveService, _orgManagementService)
{
	/// <summary>
	/// Processes the message.
	/// </summary>
	/// <param name="message">The message.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.ValueTuple&lt;System.Boolean, MessageNack&gt;&gt;.</returns>
	public override async Task<(bool, MessageNack)> ProcessMessageAsync(Message message, CancellationToken cancellationToken = default)
	{
		var messageId = message.Id;
		_logger.LogInformation("Processing InApp channel queue message - Type={Type} MessageId={MessageId}", GetType(), messageId);

		try
		{
			var ruledMessage = message.Data?.ToObject<AlertAndDef>()
				?? throw new MqChannelException($"unable to read from InApp channel queue message as message+rule, invalid payload");

			var rawMessage = ruledMessage.Message;
			var rawAlertDef = ruledMessage.Def;
			var inappTemplate = rawAlertDef.InAppInfo;
			if (inappTemplate is null)
			{
				_logger.LogInformation("InApp template not found, ignoring the message - Type={Type} MessageId={MessageId}",
					GetType(), messageId);
			}
			else if (!inappTemplate.IsEnabled.GetValueOrDefault(true))
			{
				_logger.LogInformation("InApp template found, but disabled, No Op - Type={Type} MessageId={MessageId}",
					GetType(), messageId);
			}
			else
			{
				// save and publish to specific inapp service provider (one signal)
				await InternalProcessInAppMessageAsync(rawMessage, rawAlertDef, inappTemplate, cancellationToken);
			}

			return MessageAcknowledgement.SUCCESS;
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Processing in InApp channel queue failed - Type={Type} MessageId={MessageId} Error={Error}",
				GetType(), messageId, ex.Message);
			return MessageAcknowledgement.FAIL_REQUEUE;
		}
	}

	/// <summary>
	/// Get application URL as an asynchronous operation.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.String&gt; representing the asynchronous operation.</returns>
	private async Task<string?> GetAppUrlAsync(string tenantId, string appId, CancellationToken cancellationToken)
	{
		var key = $"appurl:{tenantId}:{appId}";
		return await _memoryCache.GetOrCreateAsync<string?>(key, async (cacheEntry) =>
		{
			var appInfo = await _appManagerService.GetAppInstanceInfoAsync(tenantId, appId, cancellationToken);
			var appUrl = appInfo?.Url?.TrimEnd('/');

			// if data is null, dont cache
			if (appUrl is null)
			{
				cacheEntry.Dispose();
				return null;
			}

			return appUrl;
		},
		new MemoryCacheEntryOptions
		{
			SlidingExpiration = TimeSpan.FromHours(1)
		});
	}

	/// <summary>
	/// Helper to process in application message.
	/// </summary>
	/// <param name="rawMessage">The raw message.</param>
	/// <param name="alertDef">The alert definition.</param>
	/// <param name="inappTemplate">The inapp template.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task representing the asynchronous operation.</returns>
	/// <exception cref="MqChannelException">unable to resolve user tenant user information - TenantId={tenantId} AppId={appId}</exception>
	/// <exception cref="MqChannelException">unable to find the root org for the tenant - TenantId={tenantId}</exception>
	/// <exception cref="MqChannelException">unable to find the default template id to render - TenantId={tenantId}</exception>
	private async Task InternalProcessInAppMessageAsync(Message rawMessage, AlertDef alertDef, InAppAlertInfo inappTemplate, CancellationToken cancellationToken)
	{
		var messageId = rawMessage.Id;
		var tenantId = alertDef.TenantId;
		var appId = alertDef.AppId;

		var allUserIds = new HashSet<string>();
		var expressionUserIds = await ResolveExpressionToUserIdsAsync(rawMessage, inappTemplate.UsersExpression, cancellationToken);
		_logger.LogInformation("InApp template resolve to recipients - via expression - MessageId={MessageId} Recipients#={Recipients}",
			messageId, expressionUserIds.Count);
		allUserIds.UnionWith(expressionUserIds);

		var serviceUserId = await _tokenGenerationService.ResolveUserIdFromTenantAppIdAsync(tenantId, appId, cancellationToken: cancellationToken)
			?? throw new MqChannelException($"unable to resolve user tenant user information - TenantId={tenantId} AppId={appId}");
		var userSecurityInfo = new UserSecurityInfo(alertDef.TenantId, alertDef.AppId, serviceUserId, null);

		var roleUserIds = await ResolveRolesToUsersAsync(alertDef.RecipientRoles, userSecurityInfo, messageId, cancellationToken);
		_logger.LogInformation("InApp template resolve to recipients - via roles - MessageId={MessageId} Recipients#={Recipients}",
			messageId, roleUserIds.Count);
		allUserIds.UnionWith(roleUserIds);

		// resolve users using template
		var data = rawMessage.Data;
		var templateId = inappTemplate.DefaultTemplateId;
		if (!templateId.IsNullOrEmpty())
		{
			var templateInfo = await GetTemplateInfoAsync(templateId, userSecurityInfo, cancellationToken);
			var toIds = templateInfo?.To;
			if (!toIds.IsNullOrEmpty())
			{
				var toIdsTransformed = await _templateProcessor.TransformModelAsync(toIds, data, userSecurityInfo, cancellationToken);
				_logger.LogInformation("InApp template - resolved template to - MessageId={MessageId} To={To} ToIdsTransformed={ToIdsTransformed}",
					messageId, toIds, toIdsTransformed);

				if (!toIdsTransformed.IsNullOrEmpty())
				{
					allUserIds.UnionWith(toIdsTransformed.Split(';', ',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries));
				}
			}
		}

		var alertId = alertDef.Id;
		// var rootOrg = await _orgManagementService.GetTenantRootOrgAsync(userSecurityInfo, cancellationToken)
		// 	?? throw new MqChannelException($"unable to find the root org for the tenant - TenantId={tenantId}");
	//	_logger.LogWarning("InApp template - Determined root org - MessageId={MessageId} TemplateId={TemplateId} RootOrg={RootOrg}", messageId, templateId, rootOrg);
	//	var rootOrgId = rootOrg.OrgId.ToString();

	//	var orgAlert = await _alertdefPreferenceService.GetOrgPreferenceAsync(alertId, rootOrgId, userSecurityInfo, cancellationToken);
		// if (orgAlert is not null)
		// {
		// 	_logger.LogWarning("InApp template - Resolving org users - MessageId={MessageId} TemplateId={TemplateId} Orgs={Orgs}", messageId, templateId, orgAlert.RecipientOrgs);
		// 	var orgsUserIds = await ResolveOrgsToUsersAsync(orgAlert.RecipientOrgs, userSecurityInfo, messageId, cancellationToken);
		// 	if (orgsUserIds.Count > 0)
		// 	{
		// 		allUserIds.UnionWith(orgsUserIds);
		// 	}
		//
		// 	_logger.LogWarning("InApp template - Resolving role users - MessageId={MessageId} TemplateId={TemplateId} Roles={Roles}", messageId, templateId, orgAlert.RecipientRoles);
		// 	var rolesUserIds = await ResolveRolesToUsersAsync(orgAlert.RecipientRoles, userSecurityInfo, messageId, cancellationToken);
		// 	if (rolesUserIds.Count > 0)
		// 	{
		// 		allUserIds.UnionWith(rolesUserIds);
		// 	}
		// }
		_logger.LogWarning("InApp template - Resolved target users - MessageId={MessageId} TemplateId={TemplateId} Users#={Users}",
			messageId, templateId, allUserIds.Count);

		// sanity check
		if (allUserIds.Count == 0)
		{
			_logger.LogWarning("InApp template did not resolve to any recipients, ignoring send - TemplateId={TemplateId} MessageId={MessageId}",
				templateId, messageId);
			return;
		}

		// resolve navigation details
		var navigationInfo = await ResolveNavigationInfoAsync(data, alertDef, userSecurityInfo, cancellationToken);

		foreach (var userId in allUserIds)
		{
			_logger.LogWarning("InApp template - processing InApp template for user - MessageId={MessageId} TemplateId={TemplateId} UserId={UserId}",
				messageId, templateId, userId);

			try
			{
				//	var userAlert = await _alertdefPreferenceService.GetUserOrgPreferenceAsync(alertId, userId, userSecurityInfo, cancellationToken);
				//	var isEnabled = userAlert?.IsEnabled ?? orgAlert?.IsEnabled ?? true;
				//	if (!isEnabled)
				//	{
				//		_logger.LogWarning("InApp template - user disabled the alert - MessageId={MessageId} TemplateId={TemplateId} UserId={UserId}",
				//			messageId, templateId, userId);
				//		continue;
				//	}

				//var defaultTemplateId = userAlert?.InAppInfo?.DefaultTemplateId ?? inappTemplate.DefaultTemplateId
				//	?? throw new MqChannelException($"unable to find the default template id to render - TenantId={tenantId}");
				var defaultTemplateId = inappTemplate.DefaultTemplateId
				                        ?? throw new MqChannelException($"unable to find the default template id to render - TenantId={tenantId}");
				var inAppAlert = await RenderInAppTemplateAsync(data, defaultTemplateId, userSecurityInfo, cancellationToken);
				inAppAlert.tenant_id = alertDef.TenantId;
				inAppAlert.app_id = alertDef.AppId;
				inAppAlert.user_id = userId;
				inAppAlert.id = $"inapp:{alertDef.Id}:{messageId}:{userId}";
				inAppAlert.def_id = alertDef.Id;
				inAppAlert.rel_link_url = navigationInfo.relUrl;
				inAppAlert.deep_link_url = navigationInfo.absUrl;

				var noOfInserts = await _historyService.UpsertInAppAlertAsync(inAppAlert);
				if (noOfInserts > 0)
				{
					// inserted message, so push to queue, if already available/processed, No op...
					await _mqSendService.PostMessageAsync(Constants.InappProviders["messenger.inapp"], inAppAlert);
				}
			}
			catch (Exception exAlert)
			{
				_logger.LogError(exAlert, "Unable to send in app template alert due to an error - MessageId={MessageId} Error={Error}",
					messageId, exAlert.Message);
			}
		}
	}

	/// <summary>
	/// Resolve navigation information as an asynchronous operation.
	/// </summary>
	/// <param name="data">The data.</param>
	/// <param name="alertDef">The alert definition.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;System.ValueTuple&gt; representing the asynchronous operation.</returns>
	private async Task<(string? relUrl, string? absUrl)> ResolveNavigationInfoAsync(JsonDocument? data, AlertDef alertDef, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken)
	{
		string? relUrl = null, absUrl = null;

		var navigationPageUrlExpression = alertDef.NavigationPageUrlExpression;
		if (!navigationPageUrlExpression.IsNullOrEmpty())
		{
			var pageUrl = await _templateProcessor.TransformModelAsync(alertDef.NavigationPageUrlExpression, data, userSecurityInfo, cancellationToken);

			if (alertDef.NavigationVariables is List<NavigationVariableInfo> navVariables)
			{
				var navVariablesTransformed = await _templateProcessor.BulkTransformModelAsync
				(
					navVariables.Select(v => v.ValueExpression).ToArray(),
					data, userSecurityInfo, cancellationToken
				);

				var navigationContext = new JsonObject();
				foreach (var variableInfo in navVariables.Zip(navVariablesTransformed))
				{
					navigationContext.Add(variableInfo.First.Name, variableInfo.Second ?? string.Empty);
				}

				var navigationContextString = navigationContext.ToJSON() ?? string.Empty;
				_logger.LogInformation("Resolving page navigation context - AlertDef={AlertDef} PageUrl={PageUrl} NavigationContext={NavigationContext}",
					alertDef.Id, pageUrl, navigationContextString);

				relUrl = $"{pageUrl?.TrimEnd('/')}/{Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(navigationContextString))}";
				if (!IsAbsoluteWebSitePath(relUrl))
				{
					var appUrl = await GetAppUrlAsync(userSecurityInfo.TenantId, userSecurityInfo.AppId, cancellationToken);
					absUrl = $"{appUrl}/{relUrl.TrimStart('/')}";
				}
				else
				{
					absUrl = relUrl;
				}
			}
		}

		return (relUrl, absUrl);
	}

	/// <summary>
	/// Determines whether the given url is absolute web site path
	/// </summary>
	/// <param name="url">The URL.</param>
	/// <returns><c>true</c> if is an absolute web site path; otherwise, <c>false</c>.</returns>
	private static bool IsAbsoluteWebSitePath(string url)
		=> url.StartsWith("http://", StringComparison.OrdinalIgnoreCase)
			|| url.StartsWith("https://", StringComparison.OrdinalIgnoreCase);

	/// <summary>
	/// Render in application template into an in app alert.
	/// </summary>
	/// <param name="data">The data.</param>
	/// <param name="defaultTemplateId">The default template identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;Models.InAppAlert&gt; representing the asynchronous operation.</returns>
	private async Task<Models.InAppAlert> RenderInAppTemplateAsync(JsonDocument? data, string defaultTemplateId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken)
	{
		_logger.LogInformation("transforming template into values - Data={Data} TemplateId={TemplateId}",
			data?.ToJSON(), defaultTemplateId);

		var templateInfo = await GetTemplateInfoAsync(defaultTemplateId, userSecurityInfo, cancellationToken);
		var transformed = (await _templateProcessor.BulkTransformModelAsync
		(
			[
				templateInfo.Subject,
				templateInfo.TextBody,
				templateInfo.HtmlBody,
				templateInfo.Priority,
				templateInfo.Attachments,
			],
			data,
			userSecurityInfo,
			cancellationToken
		)).ToArray();

		var result = new InAppAlert
		{
			subject = transformed[0],
			txt_body = transformed[1],
			html_body = transformed[2],
			priority = transformed[3],
			attachments = transformed[4],
			data = data
		};
		_logger.LogInformation("transformed template into values, making new email object - Data={Data} Transformed={Transformed}",
			data?.ToJSON(), transformed);
		return result;
	}
}

#pragma warning restore S3776