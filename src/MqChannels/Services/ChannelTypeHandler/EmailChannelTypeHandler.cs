using Apex.Models;
using Apex.Options;
using Apex.Services;
using Microsoft.Extensions.Options;
using MqChannels.Models;
using MqChannels.Utils;
using System.Text.Json;
using EmailSender.Models;

namespace MqChannels.Services;

#pragma warning disable S3776

/// <summary>
/// class EmailChannelTypeHandler
/// </summary>
public sealed class EmailChannelTypeHandler(ILogger<EmailChannelTypeHandler> _logger, IOptions<ServiceOptions> _serviceOptions,
	IMqSendService _mqSendService,
	IOrgManagementService _orgManagementService,
	IAlertdefPreferenceService _alertdefPreferenceService,
	IEmailHistoryService _historyService, IJiffyDriveRestService _jiffyDriveService, ITokenGenerationService _tokenGenerationService,
	ITemplateProcessorRestService _templateProcessor)
	: BaseChannelTypeHandler(_logger, _serviceOptions, _jiffyDriveService, _orgManagementService)
{
	/// <summary>
	/// Processes the message.
	/// </summary>
	/// <param name="message">The message.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task&lt;System.ValueTuple&lt;System.Boolean, MessageNack&gt;&gt;.</returns>
	public override async Task<(bool, MessageNack)> ProcessMessageAsync(Message message, CancellationToken cancellationToken = default)
	{
		var messageId = message.Id;
		_logger.LogInformation("Processing Email channel queue message - Type={Type} MessageId={MessageId}", GetType(), messageId);

		try
		{
			var ruledMessage = message.Data?.ToObject<AlertAndDef>()
				?? throw new MqChannelException($"unable to read from Email channel queue message as message+rule, invalid payload");

			var rawMessage = ruledMessage.Message;
			var rawAlertDef = ruledMessage.Def;
			var emailTemplate = rawAlertDef.EmailInfo;
			if (emailTemplate is null)
			{
				_logger.LogInformation("Email template not found, ignoring the message - Type={Type} MessageId={MessageId}",
					GetType(), messageId);
			}
			else if (!emailTemplate.IsEnabled.GetValueOrDefault(true))
			{
				_logger.LogInformation("Email template found, but disabled, No Op - Type={Type} MessageId={MessageId}",
					GetType(), messageId);
			}
			else
			{
				// save and publish to specific email service provider (one signal)
				await InternalProcessEmailMessageAsync(rawMessage, rawAlertDef, emailTemplate, cancellationToken);
			}

			return MessageAcknowledgement.SUCCESS;
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Processing in Email channel queue failed - Type={Type} MessageId={MessageId} Error={Error}",
				GetType(), messageId, ex.Message);
			return MessageAcknowledgement.FAIL_REQUEUE;
		}
	}

	/// <summary>
	/// Helper to process emaillication message.
	/// </summary>
	/// <param name="rawMessage">The raw message.</param>
	/// <param name="alertDef">The alert definition.</param>
	/// <param name="emailTemplate">The email template.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task representing the asynchronous operation.</returns>
	/// <exception cref="MqChannelException">unable to resolve user tenant user information - TenantId={tenantId} AppId={appId}</exception>
	/// <exception cref="MqChannelException">unable to find the root org for the tenant - TenantId={tenantId}</exception>
	/// <exception cref="MqChannelException">unable to find the default template id to render - TenantId={tenantId}</exception>
	private async Task InternalProcessEmailMessageAsync(Message rawMessage, AlertDef alertDef, EmailAlertInfo? emailTemplate, CancellationToken cancellationToken)
	{
		var messageId = rawMessage.Id;
		var tenantId = alertDef.TenantId;
		var appId = alertDef.AppId;

			try
			{
				if (emailTemplate is not null && emailTemplate.IsEnabled == true)
				{

					var emailInfo = new EmailInfoWrapper(tenantId, appId, null, new TemplatedEmail
					{
						ProviderName = emailTemplate.Outbox,
						TemplatePath = emailTemplate.DefaultTemplateId,
						Data = rawMessage?.Data
					});
					// History is not maintained as of now
					await _mqSendService.PostMessageAsync(Constants.InappProviders["emailsender.sendmail"], emailInfo);
				}

			}
			catch (Exception exAlert)
			{
				_logger.LogError(exAlert, "Unable to send email template alert due to an error - MessageId={MessageId} Error={Error}",
					messageId, exAlert.Message);
			}
	}

	/// <summary>
	/// Render email template into an email alert.
	/// </summary>
	/// <param name="data">The data.</param>
	/// <param name="defaultTemplateId">The default template identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;Models.EmailAlert&gt; representing the asynchronous operation.</returns>
	private async Task<IEnumerable<Models.EmailAlert>> RenderEmailTemplateAsync(JsonDocument? data, string defaultTemplateId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken)
	{
		_logger.LogInformation("transforming template into values - Data={Data} TemplateId={TemplateId}",
			data?.ToJSON(), defaultTemplateId);

		var templateInfo = await GetTemplateInfoAsync(defaultTemplateId, userSecurityInfo, cancellationToken);
		var transformed = (await _templateProcessor.BulkTransformModelAsync
		(
			[
				templateInfo.To,
				templateInfo.Cc,
				templateInfo.Bcc,
				templateInfo.Subject,
				templateInfo.TextBody,
				templateInfo.HtmlBody,
				templateInfo.Priority,
				templateInfo.Attachments,
			],
			data,
			userSecurityInfo,
			cancellationToken
		)).ToArray();
		_logger.LogInformation("transformed template into values, making new email object - Data={Data} Transformed={Transformed}",
			data?.ToJSON(), transformed);


		var allUsers = Enumerable.Empty<string>()
			.Concat(SplitMailIds(transformed[0]))
			.Concat(SplitMailIds(transformed[1]))
			.Concat(SplitMailIds(transformed[2]));
		return allUsers.Select(userId => new EmailAlert
		{
			subject = transformed[3],
			txt_body = transformed[4],
			html_body = transformed[5],
			priority = transformed[6],
			attachments = transformed[7]
		});
	}

	/// <summary>
	/// Splits the mail ids.
	/// </summary>
	/// <param name="input">The input.</param>
	/// <returns>System.String[].</returns>
	static string[] SplitMailIds(string? input) => input?.Split(';', ',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries) ?? [];
}

#pragma warning restore S3776