using Apex.Options;
using Apex.Services;
using Microsoft.Extensions.Options;

namespace MqChannels.Services;

/// <summary>
/// class SmsChannelTypeHandler
/// </summary>
public sealed class SmsChannelTypeHandler(ILogger<SmsChannelTypeHandler> _logger, IOptions<ServiceOptions> _serviceOptions,
	IJiffyDriveRestService _jiffyDriveService, IOrgManagementService _orgManagementService)
	: BaseChannelTypeHandler(_logger, _serviceOptions, _jiffyDriveService, _orgManagementService)
{
}