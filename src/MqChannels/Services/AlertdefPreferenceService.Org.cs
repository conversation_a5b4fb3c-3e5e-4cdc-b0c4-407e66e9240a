using Apex.Models;
using Apex.Services;

namespace MqChannels.Services;

/// <summary>
/// class AlertdefPreferenceService
/// </summary>
public sealed partial class AlertdefPreferenceService
{
	/// <summary>
	/// Get org preference.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="orgId">The org identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;AlertDefOverride&gt; representing the asynchronous operation.</returns>
	public async Task<AlertDefOverride?> GetOrgPreferenceAsync(string id, string orgId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
		=> PreferenceToAlert(await _orgManagementService.GetPreferenceAsync(new PreferenceScope(OrgId: orgId), MakePrefId(id), userSecurityInfo, cancellationToken));

	/// <summary>
	/// Upserts the org preference.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="orgId">The org identifier.</param>
	/// <param name="alertDef">The alert definition.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;Microsoft.AspNetCore.Mvc.IActionResult&gt;.</returns>
	public Task UpsertOrgPreferenceAsync(string id, string orgId, AlertDefOverride alertDef, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
		=> _orgManagementService.UpsertPreferenceAsync(new PreferenceScope(OrgId: orgId), AlertToPreference(id, alertDef), userSecurityInfo, cancellationToken);

	/// <summary>
	/// Deletes the org preference.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="orgId">The org identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	public Task DeleteOrgPreferenceAsync(string id, string orgId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
		=> _orgManagementService.DeletePreferenceAsync(new PreferenceScope(OrgId: orgId), MakePrefId(id), userSecurityInfo, cancellationToken);

	/// <summary>
	/// Makes the preference identifier.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <returns>System.String.</returns>
	static string MakePrefId(string id) => $"alert_{id}";

	/// <summary>
	/// Alert to Preference.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="source">The source.</param>
	/// <returns>System.String.</returns>
	static Preference AlertToPreference(string id, AlertDefOverride source) => new(MakePrefId(id), source.ToKeyValuePairs().Select(kv => new PreferenceKeyValue(kv.Key, kv.Value)).ToArray());

	/// <summary>
	/// Preference to alert.
	/// </summary>
	/// <param name="source">The source.</param>
	/// <returns>Apex.Models.AlertDefOverride.</returns>
	static AlertDefOverride? PreferenceToAlert(Preference? source) =>
		source?.Preferences?.Select(p => KeyValuePair.Create(p.Key, p.Value)).ToObject<AlertDefOverride>();
}