namespace MqChannels.Services;

using System.Runtime.Serialization;

/// <summary>
/// Class MqChannelException.
/// Implements the <see cref="System.Exception" />
/// </summary>
/// <seealso cref="System.Exception" />
[Serializable]
public sealed partial class MqChannelException : Exception
{
	/// <summary>
	/// Initializes a new instance of the <see cref="MqChannelException" /> class.
	/// </summary>
	/// <param name="errorMessage">The error message.</param>
	/// <returns>InvalidStudentNameException.</returns>
	public MqChannelException(string errorMessage)
		: base(errorMessage)
	{
	}

	/// <summary>
	/// Initializes a new instance of the <see cref="MqChannelException" /> class.
	/// </summary>
	/// <param name="serializationInfo">The serialization information.</param>
	/// <param name="streamingContext">The streaming context.</param>
	private MqChannelException(SerializationInfo serializationInfo, StreamingContext streamingContext)
		: base(serializationInfo, streamingContext)
	{
	}
}