using Apex.Models;
using MqChannels.Models;

namespace MqChannels.Services;

/// <summary>
/// Interface IBellIconPreferenceService
/// </summary>
public partial interface IBellIconPreferenceService
{
	/// <summary>
	/// Get org preference.
	/// </summary>
	/// <param name="orgId">The org identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;BelIconSettings&gt; representing the asynchronous operation.</returns>
	Task<BellIconSettings?> GetOrgPreferenceAsync(string orgId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Upserts the org preference.
	/// </summary>
	/// <param name="orgId">The org identifier.</param>
	/// <param name="bellIconSettings">The bell icon settings.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>System.Threading.Tasks.Task&lt;Microsoft.AspNetCore.Mvc.IActionResult&gt;.</returns>
	Task UpsertOrgPreferenceAsync(string orgId, BellIconSettings bellIconSettings, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);

	/// <summary>
	/// Deletes the org preference.
	/// </summary>
	/// <param name="orgId">The org identifier.</param>
	/// <param name="userSecurityInfo">The user security information.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>Task.</returns>
	Task DeleteOrgPreferenceAsync(string orgId, UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default);
}