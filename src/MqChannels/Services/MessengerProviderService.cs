using Apex.Models;
using Apex.Services;
using Json.Patch;
using MqChannels.Models;
using MqChannels.Utils;

namespace MqChannels.Services;

/// <summary>
/// The one signal in app publisher service class
/// </summary>
/// <remarks>Initializes a new instance of the <see cref="MessengerProviderService" /> class</remarks>
public sealed class MessengerProviderService(ILogger<MessengerProviderService> _logger,
	IMessengerRestService _messengerRestService) : IMessengerProviderService
{
	/// <summary>
	/// Sends the alert using the specified message
	/// </summary>
	/// <param name="message">The message</param>
	/// <param name="cancellationToken">The cancellation token</param>
	/// <returns>A task containing the bool message nack</returns>
	public async Task<SendAlertResponse> SendInAppAlertAsync(InAppAlert message, CancellationToken cancellationToken = default)
	{
		var messageId = message.id!;
		try
		{
			if (message.user_id.IsNullOrWhiteSpace())
			{
				throw new MqChannelException("In app alert cannot be sent: user id is null or empty.");
			}

			var userSecurityInfo = new UserSecurityInfo(message.tenant_id, message.app_id, message.user_id!, AccessToken: null);

			var topic = $"{message.tenant_id}_{Constants.InAppAlertBaseTopic}_{message.user_id}";
			_logger.LogInformation("Messenger - Alert Sending To - Topic={Topic} MessageId={MessageId} TenantId={TenantId} AppId={AppId}", topic, messageId, message.tenant_id, message.app_id);
			
			//AUF-1958: incrementing the count since we are building message before incr.
			//This was managed by ui earlier. need to check further.
			
			var currentStatus = await GetHasUnseenStatus(message.tenant_id, message.app_id, message.user_id!, incrementCount: true, cancellationToken: cancellationToken);
			var result = await PublishToMessenger(userSecurityInfo,
				new QueueMessage
				{
					Topic = topic,
					IsCrossTenant = true,
					Payload = new WebSocketMessage(message, currentStatus).ToJsonDocument()
				}, cancellationToken);
			_logger.LogInformation("Messenger - Alert Sent - MessageId={MessageId} Response={Response}", messageId, result);

			if (result.Status)
			{
				// set has unseen flag for the user.
				await IncrementUnseenStatus(userSecurityInfo, cancellationToken: cancellationToken);
				_logger.LogInformation("Messenger - Alert Sent - MessageId={MessageId}", messageId);
			}
			return new SendAlertResponse(messageId, null, null, true, result.Error);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Messenger - Alert - Failed to send - MessageId={MessageId} Error={Error}",
				messageId, ex.Message);

			return new SendAlertResponse(messageId, null, null, true, ex.Message);
		}
	}

	/// <summary>
	/// Gets the has unseen status using the specified tenant id
	/// </summary>
	/// <param name="tenantId">The tenant id</param>
	/// <param name="appId">The app id</param>
	/// <param name="userId">The user id</param>
	/// <param name="incrementCount"></param>
	/// <param name="cancellationToken">The cancellation token</param>
	/// <returns>A task containing the bool</returns>
	public async Task<AlertStatus> GetHasUnseenStatus(string tenantId, string appId, string userId,bool incrementCount = false, CancellationToken cancellationToken = default)
	{
		try
		{
			var key = MakeUnseenKey(tenantId, userId);
			var userSecurityInfo = new UserSecurityInfo(tenantId, appId, userId, AccessToken: null);
			var response = await _messengerRestService.GetValueAsync(key, userSecurityInfo, isCrossTenant:true, cancellationToken);
			_logger.LogInformation("Messenger - HasUnSeenStatus - Response={Response}", response);

			response = response?.Trim('"');
			int.TryParse(response, out var unSeenCount);
			if (incrementCount)
			{
				unSeenCount++;
			}

			return new AlertStatus(hasUnseen: unSeenCount > 0, unseenCount: unSeenCount);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Exception occured while reading the unseen flag - Exception={Exception}", ex.Message);
		}

		return new AlertStatus();
	}

	/// <summary>
	/// Resets the has unseen status for the user
	/// </summary>
	/// <param name="tenantId">The tenant id</param>
	/// <param name="appId">The app id</param>
	/// <param name="userId">The user id</param>
	/// <param name="alertStatusPatch">The alert status patch.</param>
	/// <param name="cancellationToken">The cancellation token</param>
	/// <returns>A task containing the bool</returns>
	public async Task<AlertStatus> ResetHasUnseenStatus(string tenantId, string appId, string userId,
		JsonPatch alertStatusPatch, CancellationToken cancellationToken = default)
	{
		var key = MakeUnseenKey(tenantId, userId);
		var userSecurityInfo = new UserSecurityInfo(tenantId, appId, userId, AccessToken: null);

		var alertStatus = new AlertStatus();
		alertStatus = alertStatusPatch.Apply(alertStatus) ?? alertStatus;
		await _messengerRestService.SetValueAsync(key, alertStatus.unseenCount, null, userSecurityInfo, isCrossTenant:true, cancellationToken: cancellationToken);
		
		// //publish to all listeners
		await PublishToMessenger(userSecurityInfo,
			new QueueMessage
			{
				Topic = $"{tenantId}_{Constants.InAppAlertBaseTopic}_{userId}",
				IsCrossTenant = true,
				Payload = new WebSocketMessage(null!, alertStatus).ToJsonDocument()
			}, cancellationToken);
		return alertStatus;
	}

	/// <summary>
	/// Sets the has unseen status using the specified user security info
	/// </summary>
	/// <param name="userSecurityInfo">The user security info</param>
	/// <param name="cancellationToken">The cancellation token</param>
	/// <returns>A task containing the bool</returns>
	public async Task<bool> IncrementUnseenStatus(UserSecurityInfo userSecurityInfo, CancellationToken cancellationToken = default)
	{
		try
		{
			var key = MakeUnseenKey(userSecurityInfo.TenantId, userSecurityInfo.UserId!);
			await _messengerRestService.IncrementValueAsync(key, expiryDuration: null, userSecurityInfo,isCrossTenant:true,  cancellationToken);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "Exception occured while setting the unseen flag - Exception={Exception}", ex.Message);
		}

		return true;
	}

	/// <summary>
	/// Gets the topic for user using the specified tenant id
	/// </summary>
	/// <param name="tenantId">The tenant id</param>
	/// <param name="appId">The app id</param>
	/// <param name="userId">The user id</param>
	/// <param name="cancellationToken">The cancellation token</param>
	/// <returns>A task containing the topic</returns>
	public Task<Topic> GetTopicForUser(string tenantId, string appId, string userId, CancellationToken cancellationToken = default)
		=> Task.FromResult(new Topic($"{tenantId}_{Constants.InAppAlertBaseTopic}_{userId}"));

	/// <summary>
	/// Makes the unseen key.
	/// </summary>
	/// <param name="tenantId"></param>
	/// <param name="userId">The user identifier.</param>
	/// <returns>System.String.</returns>
	private static string MakeUnseenKey(string tenantId,string userId)
		=> $"{tenantId}_{Constants.InAppAlertBaseTopic}_{Constants.HasUnSeenKey}_{userId}";

	/// <summary>
	/// Publishes the to messenger unseen status for the user
	/// </summary>
	/// <param name="userSecurityInfo"></param>
	/// <param name="queueMessage">The queue message</param>
	/// <param name="cancellationToken">The cancellation token</param>
	/// <returns>A task containing the messenger publish response</returns>
	private async Task<MessengerPublishResponse> PublishToMessenger(UserSecurityInfo userSecurityInfo, QueueMessage queueMessage, CancellationToken cancellationToken = default)
	{
		try
		{
			await _messengerRestService.PostNonDurableQueueMessageAsync(queueMessage, userSecurityInfo, cancellationToken);
			return new MessengerPublishResponse(true);
		}
		catch (Exception ex)
		{
			_logger.LogError(ex, "PUBLISH TO MESSENGER ERROR - Exception={Exception}", ex.Message);
			return new MessengerPublishResponse(false, Error: ex.Message);
		}
	}
}

/// <summary>
/// The messenger publish response
/// </summary>
internal record MessengerPublishResponse(bool Status = false, string? Error = null);

/// <summary>
/// The alert status
/// </summary>
public record AlertStatus(bool hasUnseen = false, int unseenCount = 0); // keeping case due to json patch dependency.

/// <summary>
/// The topic
/// </summary>
public record Topic(string name);

/// <summary>
/// The web socket message
/// </summary>
public record WebSocketMessage(InAppAlert Alert, AlertStatus Status);

