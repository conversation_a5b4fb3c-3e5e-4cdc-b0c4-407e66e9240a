using Apex.Models;
using Apex.Services;
using Common.Postgres;
using Dapper;

namespace MqChannels.Services;

/// <summary>
/// Class BroadcastAlertsService.
/// </summary>
public sealed partial class BroadcastAlertsService(IPgRepository2 _pgRepository, IMqSendService _sendService) : IBroadcastAlertsService
{
	/// <summary>
	/// Initializes static members of the <see cref="BroadcastAlertsService"/> class.
	/// </summary>
	static BroadcastAlertsService()
	{
		SqlMapper.AddTypeHandler(new JsonTypeHandler<AlertDefOverride>());
	}
}