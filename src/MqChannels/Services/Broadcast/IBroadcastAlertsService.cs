using MqChannels.Models;

namespace MqChannels.Services;

/// <summary>
/// Interface IBroadcastAlertsService
/// </summary>
public partial interface IBroadcastAlertsService
{
	/// <summary>
	/// Gets all broadcast alerts.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <returns>Task&lt;List&lt;MqBroadcastAlert&gt;&gt;.</returns>
	Task<List<BroadcastAlert?>> GetAllBroadcastAlertsAsync(string tenantId, string appId);

	/// <summary>
	/// Gets the broadcast alert by identifier.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="id">The identifier.</param>
	/// <returns>Task&lt;MqBroadcastAlert&gt;.</returns>
	Task<BroadcastAlert?> GetBroadcastAlertByIdAsync(string tenantId, string appId, string id);

	/// <summary>
	/// Gets the broadcast alerts by type asynchronous.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="userId">The type.</param>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <returns>Task&lt;MqBroadcastAlert&gt;.</returns>
	Task<List<BroadcastAlert?>> GetBroadcastAlertsByUserAsync(string tenantId, string appId, string userId, uint pageIndex = 1, uint pageSize = 100);

	/// <summary>
	/// Upsert the broadcast alert.
	/// </summary>
	/// <param name="instance">The instance.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	Task<int> UpsertBroadcastAlertAsync(BroadcastAlert instance);

	/// <summary>
	/// Deletes the broadcast alert by identifier.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="id">The identifier.</param>
	/// <returns>Task&lt;System.Int32&gt;.</returns>
	Task<int> DeleteBroadcastAlertByIdAsync(string tenantId, string appId, string id);

	/// <summary>
	/// Deletes the broadcast alert in application.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <returns>Task&lt;System.Int32&gt;.</returns>
	Task<int> DeleteBroadcastAlertInAppAsync(string tenantId, string appId);

	/// <summary>
	/// Queries the broadcast alerts.
	/// </summary>
	/// <param name="sql">The SQL.</param>
	/// <param name="param">The parameter.</param>
	/// <returns>Task&lt;List&lt;MqBroadcastAlert&gt;&gt;.</returns>
	Task<List<BroadcastAlert?>> QueryBroadcastAlertsAsync(string sql, object? param);
}