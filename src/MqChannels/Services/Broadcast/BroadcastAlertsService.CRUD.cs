using Common.Postgres;
using MqChannels.Models;

namespace MqChannels.Services;

/// <summary>
/// Class BroadcastAlertsService.
/// </summary>
public sealed partial class BroadcastAlertsService
{
	/// <summary>
	/// Gets all broadcast alerts.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <returns>Task&lt;IEnumerable&lt;MqBroadcastAlert&gt;&gt;.</returns>
	public Task<List<BroadcastAlert?>> GetAllBroadcastAlertsAsync(string tenantId, string appId)
		=> _pgRepository.GetAllAsync<BroadcastAlert>(new TenantAppScopedObject { tenant_id = tenantId, app_id = appId });

	/// <summary>
	/// Gets the broadcast alerts by type asynchronous.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="userId">The type.</param>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <returns>Task&lt;MqBroadcastAlert&gt;.</returns>
	public Task<List<BroadcastAlert?>> GetBroadcastAlertsByUserAsync(string tenantId, string appId, string userId, uint pageIndex = 1, uint pageSize = 100)
	{
		var limit = (int)pageSize;
		var offset = (int)((pageIndex - 1) * pageSize);
		return _pgRepository.QueryAsync<BroadcastAlert>(
$$"""
SELECT * FROM broadcast_alerts WHERE tenant_id=@tenantId AND app_id=@appId AND user_id=@userId
ORDER BY created_on DESC
LIMIT @limit OFFSET @offset
""",
				param: new
				{
					tenantId,
					appId,
					userId,
					limit,
					offset
				});
	}

	/// <summary>
	/// Gets the broadcast alert by identifier.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="id">The identifier.</param>
	/// <returns>Task&lt;MqBroadcastAlert&gt;.</returns>
	public Task<BroadcastAlert?> GetBroadcastAlertByIdAsync(string tenantId, string appId, string id)
		=> _pgRepository.GetByIdAsync<BroadcastAlert>(new TenantAppScopedObjectWithId { tenant_id = tenantId, app_id = appId, id = id });

	/// <summary>
	/// Inserts/Updates the broadcast alert.
	/// </summary>
	/// <param name="instance">The broadcast alert.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	public async Task<int> UpsertBroadcastAlertAsync(BroadcastAlert instance)
	{
		var results = await _pgRepository.QueryAsync<UpsertResult>(UPSERT_INAPPNOTIFICATION, instance);
		return results is { Count: > 0 } && results[0] is UpsertResult result ? result.Op : 0;
	}

	const string UPSERT_INAPPNOTIFICATION =
	"""
	INSERT INTO "broadcast_alerts"
	(
		"tenant_id", "app_id", "id", "user_id", "def_id", "subject", "txt_body", "html_body", "attachments", "data", "priority", "is_read", "delivery_status", "provider", "provider_response"
	)
	VALUES
	(
		@tenant_id, @app_id, @id, @user_id, @def_id, @subject, @txt_body, @html_body, @attachments, @data, @priority, @is_read, @delivery_status, @provider, @provider_response
	)
	ON CONFLICT ("tenant_id", "app_id", "id")
	DO NOTHING
	RETURNING 1 as "Op";
	""";

	/// <summary>
	/// Updates the broadcast alert delivery status.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="status">The status.</param>
	/// <param name="error">The error.</param>
	/// <param name="provider">The provider.</param>
	/// <param name="providerResponse">The provider response.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	public Task<int> UpdateBroadcastAlertDeliveryStatusAsync(TenantAppScopedObjectWithId id, DeliveryStatus status, string? error, string? provider, string? providerResponse)
		=> _pgRepository.ExecuteNonQueryAsync(UPDATE_INAPPNOTIFICATION_STATUS, new
		{
			id.tenant_id,
			id.app_id,
			id.id,
			delivery_status = status.ToString(),
			has_error = !error.IsNullOrEmpty(),
			error,
			provider,
			provider_response = providerResponse
		});

	const string UPDATE_INAPPNOTIFICATION_STATUS =
	"""
	UPDATE "broadcast_alerts"
	SET delivery_status=@delivery_status, has_error=@has_error, error=@error, provider=@provider, provider_response=@provider_response
	WHERE tenant_id=@tenant_id AND app_id=@app_id AND id=@id
	""";

	/// <summary>
	/// Deletes the broadcast alert.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <param name="id">The identifier.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	public Task<int> DeleteBroadcastAlertByIdAsync(string tenantId, string appId, string id)
		=> _pgRepository.DeleteByIdAsync<BroadcastAlert>(new TenantAppScopedObjectWithId { tenant_id = tenantId, app_id = appId, id = id });

	/// <summary>
	/// Deletes the broadcast alerts in application.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <returns>Task&lt;System.Int32&gt;.</returns>
	public Task<int> DeleteBroadcastAlertInAppAsync(string tenantId, string appId)
		=> _pgRepository.DeleteAllAsync<BroadcastAlert>(new TenantAppScopedObject { tenant_id = tenantId, app_id = appId });

	/// <summary>
	/// Queries the broadcast alerts.
	/// </summary>
	/// <param name="sql">The SQL.</param>
	/// <param name="param">The parameter.</param>
	/// <returns>Task&lt;System.Boolean&gt;.</returns>
	public Task<List<BroadcastAlert?>> QueryBroadcastAlertsAsync(string sql, object? param)
		=> _pgRepository.QueryAsync<BroadcastAlert>(sql, param);
}