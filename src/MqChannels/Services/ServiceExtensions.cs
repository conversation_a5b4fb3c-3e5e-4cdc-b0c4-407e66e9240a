using MqChannels.Options;

namespace System;

using Apex.Options;
using Apex.Services;
using Common.Postgres;
using Common.Postgres.Options;
using MqChannels.Services;

/// <summary>
/// class ServiceExtensions
/// </summary>
public static class ServiceExtensions
{
	/// <summary>
	/// Adds the mqchannels needed services.
	/// </summary>
	/// <param name="services">The services.</param>
	/// <param name="configuration">The configuration.</param>
	/// <returns>Task&lt;System.Nullable&lt;MqChannelsResponse&gt;&gt;.</returns>
	public static IServiceCollection AddMqChannelsServices(this IServiceCollection services, IConfiguration configuration)
	{
		services.AddOptions<IamOptions>().Bind(configuration.GetRequiredSection("IAM"));
		services.AddOptions<SecureIamOptions>().Bind(configuration);
		services.AddOptions<DeploymentOptions>().Bind(configuration.GetRequiredSection("DeploymentOptions"));

		services.AddOptions<ServiceOptions>().Bind(configuration.GetRequiredSection("ServiceOptions"));
		services.AddOptions<SecurePgOptions>().Bind(configuration);
		services.AddOptions<SecureOneSignalOptions>().Bind(configuration);

		services.AddMemoryCache();
		services.AddWebConnectionExtensions();
		services.AddSingleton<ITokenGenerationService, PlatformTokenGenerationService>();
		services.AddSingleton<IJiffyDriveRestService, JiffyDriveRestService>();
		services.AddSingleton<ITemplateProcessorRestService, TemplateProcessorRestService>();
		services.AddSingleton<IOrgManagementService, OrgManagementService>();
		services.AddSingleton<IMessengerRestService, MessengerRestService>();
		services.AddSingleton<IAppManagerService, AppManagerService>();

		services.AddSingleton<IPgRepository2, PgRepository2>();
		services.AddSingleton<IBellIconPreferenceService, BellIconPreferenceService>();
		services.AddSingleton<IAlertdefPreferenceService, AlertdefPreferenceService>();
		services.AddSingleton<IInAppHistoryService, InAppHistoryService>();
		services.AddSingleton<IEmailHistoryService, EmailHistoryService>();
		services.AddSingleton<IMessengerProviderService, MessengerProviderService>();

		// ordering matters
		// schema migration
		services.AddHostedService<SchemaCreatorBackgroundService>();

		// listener per channel
		foreach (var channel in CHANNELS)
		{
			services.AddSingleton<IHostedService>(sp => new ChannelBackgroundService(sp.GetRequiredService<ILogger<ChannelBackgroundService>>(), sp, channel.Key));
			services.AddKeyedScoped(typeof(IChannelTypeHandler), channel.Key, channel.Value);
		}

		// providers
		foreach (var provider in PROVIDERS)
		{
			services.AddTransient(typeof(IHostedService), provider.Value);
		}

		return services.AddMq(configuration);
	}

	/// <summary>
	/// The channels
	/// </summary>
	static readonly Dictionary<string, Type> CHANNELS = new()
	{
		{ "inappchannel", typeof(InAppChannelTypeHandler) },
		{ "emailchannel", typeof(EmailChannelTypeHandler) },
		{ "smschannel", typeof(SmsChannelTypeHandler) },
		{ "whchannel", typeof(WebHookChannelTypeHandler) },
		{ "wfchannel", typeof(WorkflowChannelTypeHandler) },
	};

	/// <summary>
	/// The providers
	/// </summary>
	static readonly Dictionary<string, Type> PROVIDERS = new()
	{
		//{ "onesignal.inapp", typeof(MessengerInAppChannelProviderBackgroundService) },
		//{ "apex.email", typeof(EmailChannelProviderBackgroundService) },
		{ "messenger.inapp", typeof(MessengerInAppChannelProviderBackgroundService) },
	};
}