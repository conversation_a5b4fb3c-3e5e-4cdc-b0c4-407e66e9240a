using Apex.Services;
using Microsoft.AspNetCore.Mvc;

namespace MqChannels.Controllers;


/// <summary>
/// Class GenericPreferencesController.
/// </summary>
public sealed partial class GenericPreferencesController
{
	/// <summary>
	/// Gets the org preference.
	/// </summary>
	/// <param name="preferenceGroupName">Name of the preference group.</param>
	/// <param name="orgId">The iam user identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("{preferenceGroupName}/org/{orgId}")]
	[ProducesResponseType(200, Type = typeof(Preference))]
	public async Task<IActionResult> GetOrgPreferenceAsync([FromRoute] string preferenceGroupName, [FromRoute] string orgId, CancellationToken cancellationToken = default)
		=> Ok(await _orgManagementService.GetPreferenceAsync(new PreferenceScope(OrgId: orgId), preferenceGroupName, this.GetUserSecurityInfo(), cancellationToken));

	/// <summary>
	/// Inserts/Updates the org preference.
	/// </summary>
	/// <param name="orgId">The iam user identifier.</param>
	/// <param name="preference">The preference.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpPost("org/{orgId}")]
	[HttpPut("org/{orgId}")]
	[ProducesResponseType(204)]
	public async Task<IActionResult> UpsertOrgPreferenceAsync([FromRoute] string orgId, [FromBody] Preference preference, CancellationToken cancellationToken = default)
	{
		await _orgManagementService.UpsertPreferenceAsync(new PreferenceScope(OrgId: orgId), preference, this.GetUserSecurityInfo(), cancellationToken);
		return NoContent();
	}

	/// <summary>
	/// Deletes the org preference.
	/// </summary>
	/// <param name="preferenceGroupName">Name of the preference group.</param>
	/// <param name="orgId">The iam user identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpDelete("{preferenceGroupName}/org/{orgId}")]
	[ProducesResponseType(204)]
	public async Task<IActionResult> DeleteOrgPreferenceAsync([FromRoute] string preferenceGroupName, [FromRoute] string orgId, CancellationToken cancellationToken = default)
	{
		await _orgManagementService.DeletePreferenceAsync(new PreferenceScope(OrgId: orgId), preferenceGroupName, this.GetUserSecurityInfo(), cancellationToken);
		return NoContent();
	}
}