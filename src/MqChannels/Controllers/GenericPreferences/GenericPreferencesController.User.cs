using Apex.Services;
using Microsoft.AspNetCore.Mvc;

namespace MqChannels.Controllers;


/// <summary>
/// Class GenericPreferencesController.
/// </summary>
public sealed partial class GenericPreferencesController
{
	/// <summary>
	/// Gets the user orgs.
	/// </summary>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("user/{iamUserId}/orgs")]
	[ProducesResponseType(200, Type = typeof(IList<Org>))]
	public async Task<IActionResult> GetUserOrgsInfoAsync([FromRoute] string iamUserId, CancellationToken cancellationToken = default)
		=> Ok(await _orgManagementService.GetUserOrgsAsync(iamUserId, this.GetUserSecurityInfo(), cancellationToken));

	/// <summary>
	/// Gets the user/org aggregated preference.
	/// </summary>
	/// <param name="preferenceGroupName">Name of the preference group.</param>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("{preferenceGroupName}/user/{iamUserId}/aggregated")]
	[ProducesResponseType(200, Type = typeof(PreferenceKeyValue))]
	public async Task<IActionResult> GetUserOrgPreferenceKeyValuesAsync([FromRoute] string preferenceGroupName, [FromRoute] string iamUserId, CancellationToken cancellationToken = default)
		=> Ok(await _orgManagementService.GetMergedPreferenceKeyValuesAsync(new PreferenceScope(UserId: iamUserId), preferenceGroupName, this.GetUserSecurityInfo(), cancellationToken));

	/// <summary>
	/// Gets the user preference.
	/// </summary>
	/// <param name="preferenceGroupName">Name of the preference group.</param>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("{preferenceGroupName}/user/{iamUserId}")]
	[ProducesResponseType(200, Type = typeof(Preference))]
	public async Task<IActionResult> GetUserPreferenceAsync([FromRoute] string preferenceGroupName, [FromRoute] string iamUserId, CancellationToken cancellationToken = default)
		=> Ok(await _orgManagementService.GetPreferenceAsync(new PreferenceScope(UserId: iamUserId), preferenceGroupName, this.GetUserSecurityInfo(), cancellationToken));

	/// <summary>
	/// Inserts/Updates the user preference.
	/// </summary>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="preference">The preference.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpPost("user/{iamUserId}")]
	[HttpPut("user/{iamUserId}")]
	[ProducesResponseType(204)]
	public async Task<IActionResult> UpserttUserPreferenceAsync([FromRoute] string iamUserId, [FromBody] Preference preference, CancellationToken cancellationToken = default)
	{
		await _orgManagementService.UpsertPreferenceAsync(new PreferenceScope(UserId: iamUserId), preference, this.GetUserSecurityInfo(), cancellationToken);
		return NoContent();
	}

	/// <summary>
	/// Deletes the user preference.
	/// </summary>
	/// <param name="preferenceGroupName">Name of the preference group.</param>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpDelete("{preferenceGroupName}/user/{iamUserId}")]
	[ProducesResponseType(204)]
	public async Task<IActionResult> DeleteUserPreferenceAsync([FromRoute] string preferenceGroupName, [FromRoute] string iamUserId, CancellationToken cancellationToken = default)
	{
		await _orgManagementService.DeletePreferenceAsync(new PreferenceScope(UserId: iamUserId), preferenceGroupName, this.GetUserSecurityInfo(), cancellationToken);
		return NoContent();
	}
}