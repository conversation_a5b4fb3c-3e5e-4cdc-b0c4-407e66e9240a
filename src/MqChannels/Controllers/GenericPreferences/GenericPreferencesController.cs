using Apex.Service.AuthHandlers.Scheme;
using Apex.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace MqChannels.Controllers;

/// <summary>
/// Class GenericPreferencesController.
/// </summary>
/// <remarks>Initializes a new instance of the <see cref="GenericPreferencesController" /> class.</remarks>
[Authorize(AuthenticationSchemes = JiffyAuthSchemeConstants.JiffyAuthScheme)]
[ApiController]
[Route("v{version:apiVersion}/prefs")]
[Produces("application/json")]
[ProducesResponseType(typeof(ProblemDetails), 500)]
[ApiExplorerSettings(GroupName = "Generic Preferences Management")]
public sealed partial class GenericPreferencesController(IOrgManagementService _orgManagementService) : ControllerBase
{
}