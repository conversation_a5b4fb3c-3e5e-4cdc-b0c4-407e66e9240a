using Microsoft.AspNetCore.Mvc;
using MqChannels.Models;

namespace MqChannels.Controllers;


/// <summary>
/// Class BellIconPreferencesController.
/// </summary>
public sealed partial class BellIconPreferencesController
{
	/// <summary>
	/// Gets the org preference.
	/// </summary>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("prefs/org")]
	[ProducesResponseType(200, Type = typeof(BellIconSettings))]
	public async Task<IActionResult> GetOrgPreferenceAsync(CancellationToken cancellationToken = default)
	{
		var org = await _orgManagementService.GetTenantRootOrgAsync(this.GetUserSecurityInfo(), cancellationToken);
		if (org is null)
		{
			return Ok(new BellIconSettings()
			{
				DateTimeFormat = "MM/DD/YYYY hh:mm:ss a",
				PageSize = 5,
				Pagination = false,
				ShowAlertMessageIcon = false,
				ShowAlertsHistoryPopup = false,
				ShowAppBoundAlerts = false,
				ShowHistoryOption = false,
				ShowReadUnreadOption = false,
				UseAbsoluteTime = true
			});
		}
		return Ok(await _bellIconManagementService.GetOrgPreferenceAsync(org?.OrgId!, this.GetUserSecurityInfo(), cancellationToken));
	}

	/// <summary>
	/// Inserts/Updates the org preference.
	/// </summary>
	/// <param name="bellIconSettings">The alert definition.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpPost("prefs/org")]
	[HttpPut("prefs/org")]
	[ProducesResponseType(204)]
	public async Task<IActionResult> UpsertOrgPreferenceAsync([FromBody] BellIconSettings bellIconSettings, CancellationToken cancellationToken = default)
	{
		var org = await _orgManagementService.GetTenantRootOrgAsync(this.GetUserSecurityInfo(), cancellationToken);
		await _bellIconManagementService.UpsertOrgPreferenceAsync(org.OrgId.ToString(), bellIconSettings, this.GetUserSecurityInfo(), cancellationToken);
		return NoContent();
	}

	/// <summary>
	/// Deletes the org preference.
	/// </summary>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpDelete("prefs/org")]
	[ProducesResponseType(204)]
	public async Task<IActionResult> DeleteOrgPreferenceAsync(CancellationToken cancellationToken = default)
	{
		var org = await _orgManagementService.GetTenantRootOrgAsync(this.GetUserSecurityInfo(), cancellationToken);
		await _bellIconManagementService.DeleteOrgPreferenceAsync(org.OrgId.ToString(), this.GetUserSecurityInfo(), cancellationToken);
		return NoContent();
	}
}