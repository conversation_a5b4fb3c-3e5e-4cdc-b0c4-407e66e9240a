using Apex.Service.AuthHandlers.Scheme;
using Apex.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MqChannels.Services;

namespace MqChannels.Controllers;

/// <summary>
/// Class BellIconPreferencesController.
/// </summary>
/// <remarks>Initializes a new instance of the <see cref="BellIconPreferencesController" /> class.</remarks>
[Authorize(AuthenticationSchemes = JiffyAuthSchemeConstants.JiffyAuthScheme)]
[ApiController]
[Route("v{version:apiVersion}/bellicon")]
[Produces("application/json")]
[ProducesResponseType(typeof(ProblemDetails), 500)]
[ApiExplorerSettings(GroupName = "Bell Icon Preferences Management")]
public sealed partial class BellIconPreferencesController(IOrgManagementService _orgManagementService, IBellIconPreferenceService _bellIconManagementService) : ControllerBase
{
}