using Apex.Services;
using Microsoft.AspNetCore.Mvc;

namespace MqChannels.Controllers;


/// <summary>
/// Class OrgController.
/// </summary>
public sealed partial class OrgController
{
	/// <summary>
	/// Gets the root org in the tenant.
	/// </summary>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("orgs/root")]
	[ProducesResponseType(200, Type = typeof(Org))]
	public async Task<IActionResult> GetTenantRootOrgAsync(CancellationToken cancellationToken = default)
		=> Ok(await _orgManagementService.GetTenantRootOrgAsync(this.GetUserSecurityInfo(), cancellationToken: cancellationToken));

	/// <summary>
	/// Gets all the orgs in the tenant.
	/// </summary>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("orgs/all")]
	[ProducesResponseType(200, Type = typeof(IList<Org>))]
	public async Task<IActionResult> GetTenantOrgsAsync([FromQuery] int pageIndex = 0, [FromQuery] int pageSize = 100, CancellationToken cancellationToken = default)
		=> Ok(await _orgManagementService.GetTenantOrgsAsync(this.GetUserSecurityInfo(), pageIndex: pageIndex, pageSize: pageSize, cancellationToken));

	/// <summary>
	/// Gets the user orgs.
	/// </summary>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("users/{iamUserId}/orgs")]
	[ProducesResponseType(200, Type = typeof(IList<Org>))]
	public async Task<IActionResult> GetUserOrgsInfoAsync([FromRoute] string iamUserId, CancellationToken cancellationToken = default)
		=> Ok(await _orgManagementService.GetUserOrgsAsync(iamUserId, this.GetUserSecurityInfo(), cancellationToken));

	/// <summary>
	/// Gets the groups.
	/// </summary>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("iam/groups")]
	[ProducesResponseType(200, Type = typeof(IList<Group>))]
	public async Task<IActionResult> GetGroupsInfoAsync(CancellationToken cancellationToken = default)
		=> Ok(await _orgManagementService.GetGroupsAsync(this.GetUserSecurityInfo(), cancellationToken));

	/// <summary>
	/// Gets the roles.
	/// </summary>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("iam/roles")]
	[ProducesResponseType(200, Type = typeof(IList<Role>))]
	public async Task<IActionResult> GetRolesInfoAsync(CancellationToken cancellationToken = default)
		=> Ok(await _orgManagementService.GetRolesAsync(this.GetUserSecurityInfo(), cancellationToken));

	/// <summary>
	/// Gets the user given the id.
	/// </summary>
	/// <param name="userId">The user identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("iam/users/{userId}")]
	[ProducesResponseType(200, Type = typeof(User))]
	public async Task<IActionResult> GetUserByIdAsync([FromRoute] string userId, CancellationToken cancellationToken = default)
		=> Ok(await _orgManagementService.GetUserByIdAsync(userId, this.GetUserSecurityInfo(), cancellationToken));

	/// <summary>
	/// Gets the users for role.
	/// </summary>
	/// <param name="roleId">Id of the role.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("iam/roles/{roleId}/users")]
	[ProducesResponseType(200, Type = typeof(IList<User>))]
	public async Task<IActionResult> GetUsersForRoleAsync([FromRoute] string roleId, CancellationToken cancellationToken = default)
	{
		var userSecurityInfo = this.GetUserSecurityInfo();
		var users = new List<User>();
		await foreach (var userBatch in _orgManagementService.GetAllUsersForRoleAsync(roleId, userSecurityInfo, cancellationToken))
		{
			users.AddRange(userBatch);
		}
		return Ok(users);
	}
}