using Apex.Service.AuthHandlers.Scheme;
using Apex.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace MqChannels.Controllers;

/// <summary>
/// Class OrgController.
/// </summary>
/// <remarks>Initializes a new instance of the <see cref="OrgController" /> class.</remarks>
[Authorize(AuthenticationSchemes = JiffyAuthSchemeConstants.JiffyAuthScheme)]
[ApiController]
[Route("v{version:apiVersion}/om")]
[Produces("application/json")]
[ProducesResponseType(typeof(ProblemDetails), 500)]
[ApiExplorerSettings(GroupName = "Org Management")]
public sealed partial class OrgController(IOrgManagementService _orgManagementService) : ControllerBase
{
}