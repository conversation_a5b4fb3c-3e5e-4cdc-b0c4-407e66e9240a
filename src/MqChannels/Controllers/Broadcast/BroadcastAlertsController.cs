using Apex.Service.AuthHandlers.Scheme;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MqChannels.Services;

namespace MqChannels.Controllers;

/// <summary>
/// Class BroadcastAlertsController.
/// </summary>
/// <remarks>Initializes a new instance of the <see cref="BroadcastAlertsController" /> class.</remarks>
[Authorize(AuthenticationSchemes = JiffyAuthSchemeConstants.JiffyAuthScheme)]
[ApiController]
[Route("v{version:apiVersion}")]
[Produces("application/json")]
[ProducesResponseType(typeof(ProblemDetails), 500)]
[ApiExplorerSettings(GroupName = "Broadcast alerts")]
public sealed partial class BroadcastAlertsController(ILogger<BroadcastAlertsController> _logger, IBroadcastAlertsService _inAppAlertsService) : ControllerBase
{
}