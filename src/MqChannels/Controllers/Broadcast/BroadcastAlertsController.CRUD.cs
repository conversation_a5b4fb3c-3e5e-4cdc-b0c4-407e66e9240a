using Microsoft.AspNetCore.Mvc;
using MqChannels.Models;

namespace MqChannels.Controllers;

/// <summary>
/// Class BroadcastAlertsController.
/// </summary>
public sealed partial class BroadcastAlertsController
{
	/// <summary>
	/// Gets the broadcast alerts.
	/// </summary>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("broadcastalert")]
	[ProducesResponseType(200, Type = typeof(IEnumerable<BroadcastAlert>))]
	public async Task<IActionResult> GetAlertsAsync([FromQuery] uint pageIndex = 1, [FromQuery] uint pageSize = 100)
		=> Ok(await _inAppAlertsService.GetBroadcastAlertsByUserAsync(this.GetCurrentTenantId(), this.GetCurrentAppId(), this.GetCurrentUserId(), pageIndex, pageSize));

	/// <summary>
	/// Gets the broadcast alert by id.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("broadcastalert/{id}")]
	[ProducesResponseType(200, Type = typeof(IEnumerable<BroadcastAlert>))]
	public async Task<IActionResult> GetAlertByIdAsync(string id)
		=> Ok(await _inAppAlertsService.GetBroadcastAlertByIdAsync(this.GetCurrentTenantId(), this.GetCurrentAppId(), id));

	/// <summary>
	/// Insert/Updates a broadcast alert.
	/// </summary>
	/// <param name="alert">The alert.</param>
	/// <returns>IActionResult.</returns>
	[HttpPost("broadcastalert")]
	[HttpPut("broadcastalert")]
	[ProducesResponseType(200, Type = typeof(int))]
	public async Task<IActionResult> UpsertAlertAsync([FromBody] BroadcastAlert alert)
	{
		_logger.LogDebug("UpsertAlertAsync - BroadcastAlert={BroadcastAlert}", alert);
		var instance = new BroadcastAlert
		{
			tenant_id = this.GetCurrentTenantId(),
			app_id = this.GetCurrentAppId(),
			id = alert.id,
			definition = alert.definition,
			user_id = alert.user_id,
			subject = alert.subject,
			txt_body = alert.txt_body,
			html_body = alert.html_body,
			attachments = alert.attachments,
			priority = alert.priority,
		};
		return Ok(await _inAppAlertsService.UpsertBroadcastAlertAsync(instance));
	}

	/// <summary>
	/// Delete broadcast alert by id.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <returns>IActionResult.</returns>
	[HttpDelete("broadcastalert/{id}")]
	[ProducesResponseType(200, Type = typeof(bool))]
	public async Task<IActionResult> DeleteAlertByIdAsync([FromRoute] string id)
	{
		_logger.LogDebug("DeleteAlertById - Id={Id}", id);
		return Ok(await _inAppAlertsService.DeleteBroadcastAlertByIdAsync(this.GetCurrentTenantId(), this.GetCurrentAppId(), id));
	}
}