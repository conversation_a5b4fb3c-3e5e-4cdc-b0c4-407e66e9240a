using Apex.Models;
using Microsoft.AspNetCore.Mvc;

namespace MqChannels.Controllers;


/// <summary>
/// Class AlertPreferencesController.
/// </summary>
public sealed partial class AlertDefPreferencesController
{
	/// <summary>
	/// Gets the org preference.
	/// </summary>
	/// <param name="id">The alert definition identifier.</param>
	/// <param name="orgId">The iam user identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("{id}/prefs/org/{orgId}")]
	[ProducesResponseType(200, Type = typeof(AlertDefOverride))]
	public async Task<IActionResult> GetOrgPreferenceAsync([FromRoute] string id, [FromRoute] string orgId, CancellationToken cancellationToken = default)
		=> Ok(await _orgManagementService.GetOrgPreferenceAsync(id, orgId, this.GetUserSecurityInfo(), cancellationToken));

	/// <summary>
	/// Inserts/Updates the org preference.
	/// </summary>
	/// <param name="id">The alert definition identifier.</param>
	/// <param name="orgId">The iam user identifier.</param>
	/// <param name="alertDef">The alert definition.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpPost("{id}/prefs/org/{orgId}")]
	[HttpPut("{id}/prefs/org/{orgId}")]
	[ProducesResponseType(204)]
	public async Task<IActionResult> UpsertOrgPreferenceAsync([FromRoute] string id, [FromRoute] string orgId, [FromBody] AlertDefOverride alertDef, CancellationToken cancellationToken = default)
	{
		await _orgManagementService.UpsertOrgPreferenceAsync(id, orgId, alertDef, this.GetUserSecurityInfo(), cancellationToken);
		return NoContent();
	}

	/// <summary>
	/// Deletes the org preference.
	/// </summary>
	/// <param name="id">The alert definition identifier.</param>
	/// <param name="orgId">The iam user identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpDelete("{id}/prefs/org/{orgId}")]
	[ProducesResponseType(204)]
	public async Task<IActionResult> DeleteOrgPreferenceAsync([FromRoute] string id, [FromRoute] string orgId, CancellationToken cancellationToken = default)
	{
		await _orgManagementService.DeleteOrgPreferenceAsync(id, orgId, this.GetUserSecurityInfo(), cancellationToken);
		return NoContent();
	}
}