using Apex.Models;
using Apex.Services;
using Microsoft.AspNetCore.Mvc;

namespace MqChannels.Controllers;

/// <summary>
/// Class AlertPreferencesController.
/// </summary>
public sealed partial class AlertDefPreferencesController
{
	/// <summary>
	/// Gets the user/org aggregated preference.
	/// </summary>
	/// <param name="id">The alert definition identifier.</param>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("{id}/prefs/user/{iamUserId}/aggregated")]
	[ProducesResponseType(200, Type = typeof(AlertDefOverride))]
	public async Task<IActionResult> GetUserOrgPreferenceAsync([FromRoute] string id, [FromRoute] string iamUserId, CancellationToken cancellationToken = default)
		=> Ok(await _orgManagementService.GetUserOrgPreferenceAsync(id, iamUserId, this.GetUserSecurityInfo(), cancellationToken));

	/// <summary>
	/// Gets the user preference.
	/// </summary>
	/// <param name="id">The alert definition identifier.</param>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("{id}/prefs/user/{iamUserId}")]
	[ProducesResponseType(200, Type = typeof(Preference))]
	public async Task<IActionResult> GetUserPreferenceAsync([FromRoute] string id, [FromRoute] string iamUserId, CancellationToken cancellationToken = default)
		=> Ok(await _orgManagementService.GetUserPreferenceAsync(id, iamUserId, this.GetUserSecurityInfo(), cancellationToken));

	/// <summary>
	/// Inserts/Updates the user preference.
	/// </summary>
	/// <param name="id">The alert definition identifier.</param>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="alertDef">The alert definition.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpPost("{id}/prefs/user/{iamUserId}")]
	[HttpPut("{id}/prefs/user/{iamUserId}")]
	[ProducesResponseType(204)]
	public async Task<IActionResult> UpsertUserPreferenceAsync([FromRoute] string id, [FromRoute] string iamUserId, [FromBody] AlertDefOverride alertDef, CancellationToken cancellationToken = default)
	{
		await _orgManagementService.UpsertUserPreferenceAsync(id, iamUserId, alertDef, this.GetUserSecurityInfo(), cancellationToken);
		return NoContent();
	}

	/// <summary>
	/// Deletes the user preference.
	/// </summary>
	/// <param name="id">The alert definition identifier.</param>
	/// <param name="iamUserId">The iam user identifier.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpDelete("{id}/prefs/user/{iamUserId}")]
	[ProducesResponseType(204)]
	public async Task<IActionResult> DeleteUserPreferenceAsync([FromRoute] string id, [FromRoute] string iamUserId, CancellationToken cancellationToken = default)
	{
		await _orgManagementService.DeleteUserPreferenceAsync(id, iamUserId, this.GetUserSecurityInfo(), cancellationToken);
		return NoContent();
	}
}