using Apex.Service.AuthHandlers.Scheme;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MqChannels.Services;

namespace MqChannels.Controllers;

/// <summary>
/// Class AlertDefPreferencesController.
/// </summary>
/// <remarks>Initializes a new instance of the <see cref="AlertDefPreferencesController" /> class.</remarks>
[Authorize(AuthenticationSchemes = JiffyAuthSchemeConstants.JiffyAuthScheme)]
[ApiController]
[Route("v{version:apiVersion}/alertdefs")]
[Produces("application/json")]
[ProducesResponseType(typeof(ProblemDetails), 500)]
[ApiExplorerSettings(GroupName = "Alert Preferences Management")]
public sealed partial class AlertDefPreferencesController(IAlertdefPreferenceService _orgManagementService) : ControllerBase
{
}