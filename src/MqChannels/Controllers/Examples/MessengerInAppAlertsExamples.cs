using Json.Patch;
using Json.Pointer;
using Swashbuckle.AspNetCore.Filters;
using System.Text.Json.Nodes;

namespace Mq.Controllers.Examples;

/// <summary>
/// Class MessengerInAppAlertsExamples. This class cannot be inherited.
/// </summary>
public sealed class MessengerInAppAlertsExamples : IMultipleExamplesProvider<JsonPatch>
{
	/// <summary>
	/// Gets the examples
	/// </summary>
	/// <returns>The new event</returns>
	public IEnumerable<SwaggerExample<JsonPatch>> GetExamples()
	{
		return
		[
			new SwaggerExample<JsonPatch>
			{
				Name = "Reset1",
				Value = new JsonPatch
				(
					PatchOperation.Replace(JsonPointer.Create("hasUnseen"), JsonNode.Parse("false")),
					PatchOperation.Replace(JsonPointer.Create("unseenCount"), JsonNode.Parse("0"))
				)
			}
		];
	}
}