using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;
using RI = System.Runtime.InteropServices.RuntimeInformation;

namespace MqChannels.Controllers;

/// <summary>
/// Class RuntimeInfo. This class cannot be inherited.
/// </summary>
internal sealed record RuntimeInfo
{
	public string? Id { get; } = RI.RuntimeIdentifier;
	public string? Framework { get; } = RI.FrameworkDescription;
}

/// <summary>
/// Class VersionInfo.
/// </summary>
internal sealed record VersionInfo(string? BuildTimestamp)
{
	public RuntimeInfo? RuntimeInfo { get; } = new();
}

/// <summary>
/// Class VersionController.
/// Implements the <see cref="ControllerBase" />
/// </summary>
/// <seealso cref="ControllerBase" />
[ApiController]
[AllowAnonymous]
[ApiVersionNeutral]
[Route("version")]
[Produces("application/json")]
[ApiExplorerSettings(GroupName = "Version")]
public sealed partial class VersionController : ControllerBase
{
	/// <summary>
	/// Gets the version.
	/// </summary>
	/// <returns>IActionResult.</returns>
	[HttpGet]
	[ProducesResponseType(typeof(VersionInfo), 200)]
	public IActionResult GetVersion()
		=> Ok(new VersionInfo(typeof(VersionController).Assembly.GetCustomAttribute<AssemblyInformationalVersionAttribute>()!.InformationalVersion));
}