using Json.Patch;
using Microsoft.AspNetCore.Mvc;
using Mq.Controllers.Examples;
using MqChannels.Models;
using MqChannels.Services;
using Swashbuckle.AspNetCore.Filters;

namespace MqChannels.Controllers;

/// <summary>
/// Class MessengerInAppAlertsController.
/// </summary>
public sealed partial class MessengerInAppAlertsController
{
	/// <summary>
	/// Send inapp alert.
	/// </summary>
	/// <param name="messengerProvider">The one signal provider.</param>
	/// <param name="alert">The alert</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A task containing the action result</returns>
	[HttpPost("alert")]
	[ProducesResponseType(200, Type = typeof(SendAlertResponse))]
	public async Task<IActionResult> SendInAppAlertAsync([FromServices] IMessengerProviderService messengerProvider,
		[FromBody] InAppAlert alert, CancellationToken cancellationToken = default)
	{
		alert.tenant_id = this.GetCurrentTenantId();
		alert.app_id = this.GetCurrentTargetAppIdOrAppId();
		alert.user_id = this.GetCurrentUserId();
		return Ok(await messengerProvider.SendInAppAlertAsync(alert, cancellationToken));
	}

	/// <summary>
	/// Send in application alert.
	/// </summary>
	/// <param name="messengerProvider">The messenger provider.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>A Task&lt;IActionResult&gt; representing the asynchronous operation.</returns>
	[HttpGet("topic")]
	[ProducesResponseType(200, Type = typeof(Topic))]
	public async Task<IActionResult> SendInAppAlertAsync([FromServices] IMessengerProviderService messengerProvider,
		 CancellationToken cancellationToken = default)
	{
		return Ok(await messengerProvider.GetTopicForUser(this.GetCurrentTenantId(), this.GetCurrentTargetAppIdOrAppId(), this.GetCurrentUserId(), cancellationToken));
	}

	/// <summary>
	/// Gets the has unseen alert status.
	/// </summary>
	/// <param name="messengerProvider">The messenger provider.</param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("status")]
	[ProducesResponseType(200, Type = typeof(AlertStatus))]
	public async Task<IActionResult> GetHasUnseenAlertStatus([FromServices] IMessengerProviderService messengerProvider,
		CancellationToken cancellationToken = default)
	{
		return Ok(await messengerProvider.GetHasUnseenStatus(this.GetCurrentTenantId(), this.GetCurrentTargetAppIdOrAppId(), this.GetCurrentUserId(),cancellationToken: cancellationToken));
	}

	/// <summary>
	/// Resets the has unseen alert status.
	/// </summary>
	/// <param name="messengerProvider">The messenger provider.</param>
	/// <param name="alertStatusPatch"></param>
	/// <param name="cancellationToken">The cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
	/// <returns>IActionResult.</returns>
	[HttpPatch("status")]
	[ProducesResponseType(200, Type = typeof(AlertStatus))]
	[SwaggerRequestExample(typeof(JsonPatch), typeof(MessengerInAppAlertsExamples))]
	public async Task<IActionResult> PatchHasUnseenAlertStatus([FromServices] IMessengerProviderService messengerProvider,
		[FromBody] JsonPatch alertStatusPatch, CancellationToken cancellationToken = default)
	{
		return Ok(await messengerProvider.ResetHasUnseenStatus(this.GetCurrentTenantId(), this.GetCurrentTargetAppIdOrAppId(), this.GetCurrentUserId(), alertStatusPatch, cancellationToken));
	}
}