using Apex.Service.AuthHandlers.Scheme;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace MqChannels.Controllers;

/// <summary>
/// Class MessengerInAppAlertsController.
/// </summary>
/// <remarks>Initializes a new instance of the <see cref="MessengerInAppAlertsController" /> class.</remarks>
[Authorize(AuthenticationSchemes = JiffyAuthSchemeConstants.JiffyAuthScheme)]
[ApiController]
[Route("v{version:apiVersion}/channels/inapp")]
[Produces("application/json")]
[ProducesResponseType(typeof(ProblemDetails), 500)]
[ApiExplorerSettings(GroupName = "Channel - Inapp alerts")]
public sealed partial class MessengerInAppAlertsController : ControllerBase
{
}