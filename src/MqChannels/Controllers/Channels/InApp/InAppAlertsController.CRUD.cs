using Microsoft.AspNetCore.Mvc;
using MqChannels.Models;

namespace MqChannels.Controllers;

/// <summary>
/// Enum AlertReadStatus
/// </summary>
public enum AlertReadStatus
{
	/// <summary>
	/// The read
	/// </summary>
	Read,

	/// <summary>
	/// The unread
	/// </summary>
	Unread
}

/// <summary>
/// Class InAppAlertsController.
/// </summary>
public sealed partial class InAppAlertsController
{
	/// <summary>
	/// Gets the alerts.
	/// </summary>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="filter">The filter.</param>
	/// <param name="deliveryStatus">The delivery status.</param>
	/// <param name="isRead">if set to <c>true</c> [is read].</param>
	/// <param name="isAppSpecific">if set to <c>true</c> [is application specific].</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("inapp")]
	[ProducesResponseType(200, Type = typeof(IEnumerable<InAppAlertView>))]
	public async Task<IActionResult> GetAlertsAsync([FromQuery] uint pageIndex = 1, [FromQuery] uint pageSize = 100, [FromQuery] string? filter = null, [FromQuery] char? deliveryStatus = null, [FromQuery] bool? isRead = null, [FromQuery] bool? isAppSpecific = null)
		=> Ok(await _inAppAlertsService.GetInAppAlertsByUserAsync(this.GetCurrentTenantId(), this.GetCurrentTargetAppIdOrAppId(), this.GetCurrentUserId(), pageIndex, pageSize, filter, deliveryStatus, isRead, isAppSpecific));

	/// <summary>
	/// Gets the alerts.
	/// </summary>
	/// <param name="pageIndex">Index of the page.</param>
	/// <param name="pageSize">Size of the page.</param>
	/// <param name="filter">The filter.</param>
	/// <param name="deliveryStatus">The delivery status.</param>
	/// <param name="isRead">if set to <c>true</c> [is read].</param>
	/// <param name="isAppSpecific">if set to <c>true</c> [is application specific].</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("inapp/all")]
	[ProducesResponseType(200, Type = typeof(IEnumerable<InAppAlertView>))]
	public async Task<IActionResult> GetAllAlertsAsync([FromQuery] uint pageIndex = 1, [FromQuery] uint pageSize = 100, [FromQuery] string? filter = null, [FromQuery] char? deliveryStatus = null, [FromQuery] bool? isRead = null, [FromQuery] bool? isAppSpecific = null)
		=> Ok(await _inAppAlertsService.GetAllInAppAlertsAsync(this.GetCurrentTenantId(), this.GetCurrentTargetAppIdOrAppId(), pageIndex, pageSize, filter, deliveryStatus, isRead, isAppSpecific));

	/// <summary>
	/// Gets the alert by id.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("inapp/{id}")]
	[ProducesResponseType(200, Type = typeof(IEnumerable<InAppAlertView>))]
	public async Task<IActionResult> GetAlertByIdAsync(string id)
		=> Ok(await _inAppAlertsService.GetInAppAlertByIdAsync(this.GetCurrentTenantId(), this.GetCurrentTargetAppIdOrAppId(), id));

	/// <summary>
	/// Insert/Updates a alert.
	/// </summary>
	/// <param name="alert">The alert.</param>
	/// <returns>IActionResult.</returns>
	[HttpPost("inapp")]
	[HttpPut("inapp")]
	[ProducesResponseType(200, Type = typeof(int))]
	public async Task<IActionResult> UpsertAlertAsync([FromBody] InAppAlert alert)
	{
		_logger.LogDebug("UpsertAlertAsync - InAppAlert={InAppAlert}", alert);
		var instance = new InAppAlert
		{
			tenant_id = this.GetCurrentTenantId(),
			app_id = this.GetCurrentTargetAppIdOrAppId(),
			id = alert.id,
			def_id = alert.def_id,
			user_id = alert.user_id,
			subject = alert.subject,
			txt_body = alert.txt_body,
			html_body = alert.html_body,
			attachments = alert.attachments,
			priority = alert.priority,
			delivery_status = alert.delivery_status,
			is_read = alert.is_read,
			deep_link_url = alert.deep_link_url,
		};
		return Ok(await _inAppAlertsService.UpsertInAppAlertAsync(instance));
	}

	/// <summary>
	/// Mark alert as read/unread.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <param name="readStatus">The readstatus.</param>
	/// <returns>A Task&lt;IActionResult&gt; representing the asynchronous operation.</returns>
	[HttpPost("inapp/{id}/markasread")]
	[ProducesResponseType(200, Type = typeof(int))]
	public async Task<IActionResult> MarkAlertAsReadUnreadAsync([FromRoute] string id, [FromQuery] AlertReadStatus readStatus = AlertReadStatus.Read)
	{
		_logger.LogDebug("MarkAlertAsReadUnreadAsync - Id={Id} ReadStatus={ReadStatus}", id, readStatus);
		return Ok(await _inAppAlertsService.MarkAsReadOrUnreadInAppAlertAsync(new Common.Postgres.TenantAppScopedObjectWithId
		{
			tenant_id = this.GetCurrentTenantId(),
			app_id = this.GetCurrentTargetAppIdOrAppId(),
			id = id
		}, readStatus == AlertReadStatus.Read));
	}

	/// <summary>
	/// Mark all alerts as read.
	/// </summary>
	/// <returns>A Task&lt;IActionResult&gt; representing the asynchronous operation.</returns>
	[HttpPost("inapp/markallasread")]
	[ProducesResponseType(200, Type = typeof(int))]
	public async Task<IActionResult> MarkAllAlertAsReadUnreadAsync([FromQuery] bool? isAppSpecific = null)
	{
		_logger.LogDebug("MarkAllAlertAsReadUnreadAsync");
		return Ok(await _inAppAlertsService.MarkAllAsReadInAppAlertAsync(this.GetCurrentTenantId(), this.GetCurrentTargetAppIdOrAppId(), this.GetCurrentUserId(), isAppSpecific));
	}

	/// <summary>
	/// Delete alert by id.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <returns>IActionResult.</returns>
	[HttpDelete("inapp/{id}")]
	[ProducesResponseType(200, Type = typeof(bool))]
	public async Task<IActionResult> DeleteAlertByIdAsync([FromRoute] string id)
	{
		_logger.LogDebug("DeleteAlertById - Id={Id}", id);
		return Ok(await _inAppAlertsService.DeleteInAppAlertByIdAsync(this.GetCurrentTenantId(), this.GetCurrentTargetAppIdOrAppId(), id));
	}
	
	/// <summary>
	/// Delete alert by id.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <returns>IActionResult.</returns>
	[HttpDelete("inapp/user/all")]
	[ProducesResponseType(200, Type = typeof(bool))]
	public async Task<IActionResult> DeleteAlertByUserAsync()
	{
		var userId = this.GetCurrentUserId();
		_logger.LogDebug("DeleteAlertByUser - UserId={UserId}", userId);
		return Ok(await _inAppAlertsService.DeleteInAppAlertsByUserAsync(this.GetCurrentTenantId(), userId));
	}
}