using Apex.Service.AuthHandlers.Scheme;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MqChannels.Services;
using MqChannels.Utils;

namespace MqChannels.Controllers;

/// <summary>
/// Class InAppAlertsController.
/// </summary>
/// <remarks>Initializes a new instance of the <see cref="InAppAlertsController" /> class.</remarks>
[Authorize(AuthenticationSchemes = JiffyAuthSchemeConstants.JiffyAuthScheme)]
[ApiController]
[Route("v{version:apiVersion}/channels")]
[Produces("application/json")]
[ProducesResponseType(typeof(ProblemDetails), 500)]
[ApiExplorerSettings(GroupName = "Channel - Inapp alerts")]
[JiffyDateTimeValueFormatter]
public sealed partial class InAppAlertsController(ILogger<InAppAlertsController> _logger, IInAppHistoryService _inAppAlertsService) : ControllerBase
{
}