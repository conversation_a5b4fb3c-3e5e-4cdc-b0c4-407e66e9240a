using Apex.Service.AuthHandlers.Scheme;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MqChannels.Services;
using MqChannels.Utils;

namespace MqChannels.Controllers;

/// <summary>
/// Class EmailAlertsController.
/// </summary>
/// <remarks>Initializes a new instance of the <see cref="EmailAlertsController" /> class.</remarks>
[Authorize(AuthenticationSchemes = JiffyAuthSchemeConstants.JiffyAuthScheme)]
[ApiController]
[Route("v{version:apiVersion}/channels")]
[Produces("application/json")]
[ProducesResponseType(typeof(ProblemDetails), 500)]
[ApiExplorerSettings(GroupName = "Channel - Email alerts")]
[JiffyDateTimeValueFormatter]
public sealed partial class EmailAlertsController(ILogger<EmailAlertsController> _logger, IEmailHistoryService _emailAlertsService) : ControllerBase
{
}