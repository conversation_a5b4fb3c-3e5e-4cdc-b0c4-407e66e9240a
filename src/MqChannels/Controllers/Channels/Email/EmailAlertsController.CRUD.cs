using Microsoft.AspNetCore.Mvc;
using MqChannels.Models;

namespace MqChannels.Controllers;

/// <summary>
/// Class EmailAlertsController.
/// </summary>
public sealed partial class EmailAlertsController
{
	/// <summary>
	/// Gets the email alerts.
	/// </summary>
	/// <returns>IActionResult.</returns>
	[HttpGet("email")]
	[ProducesResponseType(200, Type = typeof(IEnumerable<EmailAlert>))]
	public async Task<IActionResult> GetAlertsAsync([FromQuery] uint pageIndex = 1, [FromQuery] uint pageSize = 100)
		=> Ok(await _emailAlertsService.GetEmailAlertsByUserAsync(new Common.Postgres.TenantAppScopedObject
		{
			tenant_id = this.GetCurrentTenantId(),
			app_id = this.GetCurrentAppId()
		}, this.GetCurrentUserId(), pageIndex, pageSize));

	/// <summary>
	/// Gets the email alert by id.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <returns>IActionResult.</returns>
	[HttpGet("email/{id}")]
	[ProducesResponseType(200, Type = typeof(IEnumerable<EmailAlert>))]
	public async Task<IActionResult> GetAlertByIdAsync(string id)
		=> Ok(await _emailAlertsService.GetEmailAlertByIdAsync(this.GetCurrentTenantId(), this.GetCurrentAppId(), id));

	/// <summary>
	/// Insert/Updates the email alert.
	/// </summary>
	/// <param name="alert">The alert.</param>
	/// <returns>IActionResult.</returns>
	[HttpPost("email")]
	[HttpPut("email")]
	[ProducesResponseType(200, Type = typeof(int))]
	public async Task<IActionResult> UpsertAlertAsync([FromBody] EmailAlert alert)
	{
		_logger.LogDebug("UpsertAlertAsync - EmailAlert={EmailAlert}", alert);
		var instance = new EmailAlert
		{
			tenant_id = this.GetCurrentTenantId(),
			app_id = this.GetCurrentAppId(),
			id = alert.id,
			def_id = alert.def_id,
			user_id = alert.user_id,
			subject = alert.subject,
			txt_body = alert.txt_body,
			html_body = alert.html_body,
			attachments = alert.attachments,
			priority = alert.priority,
			delivery_status = alert.delivery_status,
			is_read = alert.is_read,
		};
		return Ok(await _emailAlertsService.UpsertEmailAlertAsync(instance));
	}

	/// <summary>
	/// Delete email alert by id.
	/// </summary>
	/// <param name="id">The identifier.</param>
	/// <returns>IActionResult.</returns>
	[HttpDelete("email/{id}")]
	[ProducesResponseType(200, Type = typeof(bool))]
	public async Task<IActionResult> DeleteAlertByIdAsync([FromRoute] string id)
	{
		_logger.LogDebug("DeleteAlertById - Id={Id}", id);
		return Ok(await _emailAlertsService.DeleteEmailAlertByIdAsync(this.GetCurrentTenantId(), this.GetCurrentAppId(), id));
	}
}