using Apex.Services;
using MqChannels.Utils;

namespace MqChannels.Services;

/// <summary>
/// Class OneSignalInAppChannelProviderBackgroundService.
/// </summary>
public sealed partial class MessengerInAppChannelProviderBackgroundService(ILogger<MessengerInAppChannelProviderBackgroundService> _logger,
	IServiceProvider _serviceProvider) : BackgroundService
{
	/// <summary>
	/// Execute as an asynchronous operation.
	/// </summary>
	/// <param name="stoppingToken">Triggered when <see cref="M:Microsoft.Extensions.Hosting.IHostedService.StopAsync(System.Threading.CancellationToken)" /> is called.</param>
	/// <returns>A Task representing the asynchronous operation.</returns>
	/// <remarks>See <see href="https://docs.microsoft.com/dotnet/core/extensions/workers">Worker Services in .NET</see> for implementation guidelines.</remarks>
	protected override async Task ExecuteAsync(CancellationToken stoppingToken)
	{
		_logger.LogInformation("starting messenger inapp channel queue listen");

		// let other services take over and then this start...
		await Task.Yield();

		// create an scoped instance...
		using var scopedServicesCollection = _serviceProvider.CreateScope();

		var scopedServiceProvider = scopedServicesCollection.ServiceProvider;
		var _mqReceiveService = scopedServiceProvider.GetRequiredService<IMqReceiveService>();
		var _oneSignalProviderService = scopedServiceProvider.GetRequiredService<IMessengerProviderService>();
		var _historyService = scopedServiceProvider.GetRequiredService<IInAppHistoryService>();

		await using var _queueConsumer = await _mqReceiveService.StartReceiveAsync<Models.InAppAlert>(Constants.InappProviders["messenger.inapp"], true, async message =>
		{
			_logger.LogInformation("RECEIVED IN MESSENGER INAPP PROVIDER - Message={Message}", message);
			var messageId = message.id;
			var hasError = false;
			string? error = null;
			SendAlertResponse? result = null;

			try
			{

				result = await _oneSignalProviderService.SendInAppAlertAsync(message, cancellationToken: default);
				hasError = !result.Success;
			}
			catch (Exception exAlert)
			{
				_logger.LogError(exAlert, "ERROR PROCESSING IN MESSENGER INAPP provider - MessageId={Id} Error={Error}",
					messageId, exAlert.Message);

				hasError = true;
				error = exAlert.Message;
			}

			var delivery = hasError ? Models.DeliveryStatus.E : Models.DeliveryStatus.D;
			await _historyService.UpdateInAppAlertDeliveryStatusAsync(new Common.Postgres.TenantAppScopedObjectWithId
			{
				tenant_id = message.tenant_id,
				app_id = message.app_id,
				id = message.id
			}, delivery, error, "messenger", result?.ToJSON());

			return hasError ? MessageAcknowledgement.FAIL_REQUEUE : MessageAcknowledgement.SUCCESS;
		});

#if DEBUG
		var tsDelay = TimeSpan.FromMinutes(5);
#else
		var tsDelay = TimeSpan.FromSeconds(5);
#endif
		while (!stoppingToken.IsCancellationRequested)
		{
			await Task.Delay(tsDelay, stoppingToken);
		}

		_logger.LogInformation("STOPPING MESSENGER INAPP queue listen");
	}
}