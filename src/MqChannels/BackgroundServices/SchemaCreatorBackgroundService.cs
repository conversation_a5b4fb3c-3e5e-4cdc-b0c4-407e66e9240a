using Common.Postgres;

namespace MqChannels.Services;

/// <summary>
/// Class SchemaCreatorBackgroundService.
/// </summary>
public sealed partial class SchemaCreatorBackgroundService(ILogger<SchemaCreatorBackgroundService> _logger, IPgRepository2 _repository) : BackgroundService
{
	/// <summary>
	/// Execute as an operation.
	/// </summary>
	/// <param name="stoppingToken">Triggered when
	/// <see cref="M:Microsoft.Extensions.Hosting.IHostedService.StopAsync(System.Threading.CancellationToken)" /> is
	/// called.</param>
	/// <returns>A Task representing the operation.</returns>
	/// <remarks>See <see href="https://docs.microsoft.com/dotnet/core/extensions/workers">Worker Services in .NET</see> for implementation guidelines.</remarks>
	protected override async Task ExecuteAsync(CancellationToken stoppingToken)
	{
		if (!stoppingToken.IsCancellationRequested)
		{
			_logger.LogInformation("PG upgrade script in progress...");
			var result = await _repository.ExecuteNonQueryAsync(MqChannels.Properties.Resources.MqChannelsSchemaScript);
			_logger.LogInformation("PG upgrade script completed - Result={Result}", result);
		}
	}
}