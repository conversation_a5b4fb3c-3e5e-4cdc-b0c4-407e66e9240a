using Apex.Models;
using Apex.Services;

namespace MqChannels.Services;

/// <summary>
/// Class ChannelBackgroundService.
/// </summary>
public sealed partial class ChannelBackgroundService(ILogger _logger, IServiceProvider _serviceProvider, string _channelType) : BackgroundService
{
	/// <summary>
	/// Execute.
	/// </summary>
	/// <param name="stoppingToken">Triggered when <see cref="M:Microsoft.Extensions.Hosting.IHostedService.StopAsync(System.Threading.CancellationToken)" /> is called.</param>
	/// <returns>A Task representing the asynchronous operation.</returns>
	/// <remarks>See <see href="https://docs.microsoft.com/dotnet/core/extensions/workers">Worker Services in .NET</see> for implementation guidelines.</remarks>
	protected override async Task ExecuteAsync(CancellationToken stoppingToken)
	{
		_logger.LogInformation("starting channel queue listen - ChannelType={ChannelType}", _channelType);

		// let other services take over and then this start...
		await Task.Yield();

		// create an scoped instance...
		using var scopedServicesCollection = _serviceProvider.CreateScope();

		var scopedServiceProvider = scopedServicesCollection.ServiceProvider;
		var _mqReceiveService = scopedServiceProvider.GetRequiredService<IMqReceiveService>();
		await using var _queueConsumer = await _mqReceiveService.StartReceiveAsync<Message>(_channelType, true, async message =>
		{
			_logger.LogInformation("RECEIVED IN CHANNEL - ChannelType={ChannelType} Message={Message}", _channelType, message);
			try
			{
				var ctHandler = scopedServiceProvider.GetRequiredKeyedService<IChannelTypeHandler>(_channelType);
				await ctHandler.ProcessMessageAsync(message);
			}
			catch (Exception exAlert)
			{
				_logger.LogError(exAlert, "ERROR PROCESSING - in channel - Message={ChannelType}/{Id} Error={Error}",
					_channelType, message.Id, exAlert.Message);
			}

			return MessageAcknowledgement.SUCCESS;
		});

#if DEBUG
		var tsDelay = TimeSpan.FromMinutes(5);
#else
		var tsDelay = TimeSpan.FromSeconds(5);
#endif
		while (!stoppingToken.IsCancellationRequested)
		{
			await Task.Delay(tsDelay, stoppingToken);
		}

		_logger.LogInformation("STOPPING channel queue listen - ChannelType={ChannelType}", _channelType);
	}
}