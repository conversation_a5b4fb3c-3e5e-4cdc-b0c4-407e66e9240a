namespace MqChannels;

using Apex;
using Asp.Versioning.ApiExplorer;
using System;

/// <summary>
/// Class Startup.
/// </summary>
public sealed partial class Startup : BaseServiceStartup<Startup>
{
	/// <summary>
	/// Initializes a new instance of the <see cref="Startup" /> class.
	/// </summary>
	/// <param name="configuration">The configuration.</param>
	/// <param name="webHostEnvironment">The web host environment.</param>
	public Startup(IConfiguration configuration, IHostEnvironment webHostEnvironment)
		: base(configuration, webHostEnvironment, new ServiceStartupInfo
		(
			"MqChannels",
			"MqChannels - Enables platform components to manage publishing and receive messages from a queue",
			"*MqChannels.xml",
			"/mq/channels",
			TelemetryInfo: new OpenTelemetryInfo
			(
				ServiceName: "mqchannels"
			)
		))
	{
	}

	/// <summary>
	/// Configures the services.
	/// </summary>
	/// <param name="services">The services.</param>
	public void ConfigureServices(IServiceCollection services)
	{
		BaseConfigureServices(services);
		services.AddMqChannelsServices(Configuration);
	}

	/// <summary>
	/// Configures the specified application.
	/// </summary>
	/// <param name="app">The application.</param>
	/// <param name="provider">The provider.</param>
	public void Configure(IApplicationBuilder app, IApiVersionDescriptionProvider provider)
	{
		BaseConfigure(app, provider);
	}
}