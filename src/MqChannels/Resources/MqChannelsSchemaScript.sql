-- CREATE USER IF NOT EXISTS postgresuser;
-- CREATE DATABASE mqchannels;
-- GRANT ALL PRIVILEGES ON DATABASE postgres TO postgresuser;

-- one signal mapping table
CREATE TABLE IF NOT EXISTS "tenant_onesignal_app"
(
    "tenant_id" TEXT NOT NULL,
    "onesignal_app_id" TEXT NOT NULL,
    PRIMARY KEY ("tenant_id")
);

CREATE TABLE IF NOT EXISTS "in_app_alerts"
(
    "tenant_id" TEXT NOT NULL,
    "app_id" TEXT NOT NULL,
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "def_id" TEXT NOT NULL,
    "subject" TEXT NULL,
    "txt_body" TEXT NULL,
    "html_body" TEXT NULL,
    "attachments" TEXT NULL,
    "data" TEXT NULL,
    "priority" TEXT NULL,
    "provider" TEXT NULL,
    "provider_response" TEXT NULL,
    "rel_link_url" TEXT NULL,
    "deep_link_url" TEXT NULL,
    "created_on" TIMESTAMP NOT NULL,
    "last_modified_on" TIMESTAMP NOT NULL,
    "is_read" BOOLEAN NOT NULL,
    "delivery_status" CHAR NOT NULL DEFAULT 'C', -- C=created, D=delivered, E=errored
    "has_error" BOOLEAN NOT NULL DEFAULT FALSE,
    "error" TEXT NULL,
    PRIMARY KEY ("tenant_id", "app_id", "id")
);

ALTER TABLE "in_app_alerts" ADD COLUMN IF NOT EXISTS "data" TEXT NULL;
ALTER TABLE "in_app_alerts" ADD COLUMN IF NOT EXISTS "provider" TEXT NULL;
ALTER TABLE "in_app_alerts" ADD COLUMN IF NOT EXISTS "provider_response" TEXT NULL;
ALTER TABLE "in_app_alerts" ADD COLUMN IF NOT EXISTS "rel_link_url" TEXT NULL;
ALTER TABLE "in_app_alerts" ADD COLUMN IF NOT EXISTS "deep_link_url" TEXT NULL;
ALTER TABLE "in_app_alerts" ADD COLUMN IF NOT EXISTS "has_error" BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE "in_app_alerts" ADD COLUMN IF NOT EXISTS "error" TEXT NULL;

CREATE INDEX IF NOT EXISTS IDX_in_app_alerts_read ON "in_app_alerts" ("tenant_id", "app_id", "user_id", "is_read", "delivery_status");
CREATE INDEX IF NOT EXISTS IDX_in_app_alerts_foruser ON "in_app_alerts" ("tenant_id", "app_id", "user_id", "delivery_status");

CREATE TABLE IF NOT EXISTS "email_alerts"
(
    "tenant_id" TEXT NOT NULL,
    "app_id" TEXT NOT NULL,
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "def_id" TEXT NOT NULL,
    "subject" TEXT NULL,
    "txt_body" TEXT NULL,
    "html_body" TEXT NULL,
    "attachments" TEXT NULL,
    "priority" TEXT NULL,
    "provider" TEXT NULL,
    "provider_response" TEXT NULL,
    "rel_link_url" TEXT NULL,
    "deep_link_url" TEXT NULL,
    "created_on" TIMESTAMP NOT NULL,
    "last_modified_on" TIMESTAMP NOT NULL,
    "is_read" BOOLEAN NOT NULL,
    "delivery_status" CHAR NOT NULL DEFAULT 'C', -- C=created, D=delivered, E=errored
    "has_error" BOOLEAN NOT NULL DEFAULT FALSE,
    "error" TEXT NULL,
    PRIMARY KEY ("tenant_id", "app_id", "id")
);

ALTER TABLE "email_alerts" ADD COLUMN IF NOT EXISTS "data" TEXT NULL;
ALTER TABLE "email_alerts" ADD COLUMN IF NOT EXISTS "provider" TEXT NULL;
ALTER TABLE "email_alerts" ADD COLUMN IF NOT EXISTS "provider_response" TEXT NULL;
ALTER TABLE "email_alerts" ADD COLUMN IF NOT EXISTS "rel_link_url" TEXT NULL;
ALTER TABLE "email_alerts" ADD COLUMN IF NOT EXISTS "deep_link_url" TEXT NULL;
ALTER TABLE "email_alerts" ADD COLUMN IF NOT EXISTS "has_error" BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE "email_alerts" ADD COLUMN IF NOT EXISTS "error" TEXT NULL;

CREATE TABLE IF NOT EXISTS "broadcast_alerts"
(
    "tenant_id" TEXT NOT NULL,
    "app_id" TEXT NOT NULL,
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "definition" TEXT NOT NULL,
    "subject" TEXT NULL,
    "txt_body" TEXT NULL,
    "html_body" TEXT NULL,
    "attachments" TEXT NULL,
    "priority" TEXT NULL,
    "created_on" TIMESTAMP NOT NULL,
    "last_modified_on" TIMESTAMP NOT NULL,
    PRIMARY KEY ("tenant_id", "app_id", "id")
);

SELECT pg_advisory_xact_lock(floor(random() * 100000 + 1)::int);
-- NOTE: NOTE: NOTE: NOTE: all functions definition goes below here

CREATE OR REPLACE FUNCTION update_dt() RETURNS trigger AS $$
BEGIN
    IF NEW.created_on IS NULL THEN
        NEW.created_on := NOW();
    END IF;

    NEW.last_modified_on := NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER in_app_alerts_set_lastmod
BEFORE INSERT OR UPDATE ON "in_app_alerts"
FOR EACH ROW EXECUTE PROCEDURE update_dt();

CREATE OR REPLACE TRIGGER email_alerts_set_lastmod
BEFORE INSERT OR UPDATE ON "email_alerts"
FOR EACH ROW EXECUTE PROCEDURE update_dt();

CREATE OR REPLACE TRIGGER broadcast_alerts_set_lastmod
BEFORE INSERT OR UPDATE ON "broadcast_alerts"
FOR EACH ROW EXECUTE PROCEDURE update_dt();

ALTER TABLE "in_app_alerts" ADD COLUMN IF NOT EXISTS "search_vector" tsvector;

-- UPDATE "in_app_alerts"
-- SET "search_vector" = to_tsvector('simple', coalesce("subject", '') || ' ' || coalesce("txt_body", '') || ' ' || coalesce("html_body", ''));

CREATE INDEX IF NOT EXISTS IDX_in_app_alerts_search_vector ON "in_app_alerts" USING GIN ("search_vector");

CREATE OR REPLACE FUNCTION update_search_vector() RETURNS trigger AS $$
BEGIN
    NEW.search_vector := to_tsvector('simple', coalesce(NEW.subject, '') || ' ' || coalesce(NEW.txt_body, '') || ' ' || coalesce(NEW.html_body, ''));
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER set_search_vector
    BEFORE INSERT OR UPDATE ON "in_app_alerts"
    FOR EACH ROW EXECUTE FUNCTION update_search_vector();
