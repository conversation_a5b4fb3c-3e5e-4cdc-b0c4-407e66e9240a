
var userProfileFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), "secrets-store");
await Apex.BaseProgram<MqChannels.Startup>.Main(
	new Apex.BaseProgramStartupInfo
	{
		Args = args,
#if DEBUG
		ListenPort = 5002,
		HealthPort = 19014,
		PrometheusPort = 9014,
#endif
		AppConfigurationCallback = (cb) =>
		cb
			.AddPropertiesFile(Path.Combine(userProfileFolder, "db.properties"))
			.AddPropertiesFile(Path.Combine(userProfileFolder, "principal.properties"))
			.AddPropertiesFile(Path.Combine(userProfileFolder, "extra.properties"))
	})
	.RunAsync();