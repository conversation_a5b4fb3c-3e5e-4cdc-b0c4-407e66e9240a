---
Serilog:
  MinimumLevel:
    Default: Debug
    Override:
      Microsoft: Information
      System: Information
      MqChannels: Debug
      Apex.Services: Debug
DeploymentOptions:
  ReleaseNamespace: dev-workflow
  OtlpExporterUrl: http://jaeger-collector.tracing.svc.cluster.local:4317
ServiceOptions:
  JiffyDriveUrl: https://dev-workflow.cluster.jiffy.ai/drive
  MessengerUrl: https://dev-workflow.cluster.jiffy.ai/messenger
  WorkflowUrl: https://dev-workflow.cluster.jiffy.ai
  TemplateProcessorUrl: https://dev-workflow.cluster.jiffy.ai/tp
  OmsUrl: https://dev-workflow.cluster.jiffy.ai/oms
  ClsUrl: https://dev-workflow.cluster.jiffy.ai/cls
  ConfigManagerUrl: https://dev-workflow.cluster.jiffy.ai/config-management
  PamUrl: https://dev-workflow.cluster.jiffy.ai/pam
  IamUrl: https://dev-workflow.cluster.jiffy.ai/apexiam
  ModelRepoUrl: https://dev-workflow.cluster.jiffy.ai/model-repo
MqOptions:
IAM:
  tenantId: jiffy
  tenantName: jiffy
  graceTimeForTokenExpireInSeconds: 120
  url: https://dev-workflow.cluster.jiffy.ai
  retryPolicyClientName: defaultWithLargeRetry