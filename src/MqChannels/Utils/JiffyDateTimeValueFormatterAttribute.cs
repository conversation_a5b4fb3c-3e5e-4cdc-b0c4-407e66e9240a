using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc.Formatters;
using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Json.Serialization.Metadata;

namespace MqChannels.Utils;

/// <summary>
/// The JiffyDateTimeValueFormatterAttribute class
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true, Inherited = true)]
public sealed class JiffyDateTimeValueFormatterAttribute : ActionFilterAttribute
{
	/// <summary>
	/// Called when [action executed].
	/// </summary>
	/// <param name="context">The context.</param>
	/// <inheritdoc />
	public override void OnActionExecuted(ActionExecutedContext context)
	{
		if (context.Result is ObjectResult objectResult)
		{
			objectResult.Formatters.Add(CustomFormatter);
		}
	}

#pragma warning disable S3963
	/// <summary>
	/// Initializes static members of the <see cref="JiffyDateTimeValueFormatterAttribute"/> class.
	/// </summary>
	static JiffyDateTimeValueFormatterAttribute()
	{
		var settings = new JsonSerializerOptions(SerializationExtensions._SERIALIZEROPTIONS)
		{
			TypeInfoResolver = new DefaultJsonTypeInfoResolver()
		};
		settings.Converters.Add(new JiffyDateTimeOffsetConverter());
		CustomFormatter = new SystemTextJsonOutputFormatter(settings);
	}
#pragma warning restore S3963

	/// <summary>
	/// The custom formatter
	/// </summary>
	private static readonly SystemTextJsonOutputFormatter CustomFormatter;
}

/// <summary>
/// Class JiffyDateTimeOffsetConverter. This class cannot be inherited.
/// </summary>
internal sealed class JiffyDateTimeOffsetConverter(string DateTimeFormat = "yyyy-MM-ddTHH:mm:ss.fffffff+00:00") : JsonConverter<DateTime>
{
	/// <summary>
	/// Writes the specified writer.
	/// </summary>
	/// <param name="writer">The writer.</param>
	/// <param name="date">The date.</param>
	/// <param name="options">The options.</param>
	public override void Write(Utf8JsonWriter writer, DateTime date, JsonSerializerOptions options)
		=> writer.WriteStringValue(date.ToString(DateTimeFormat));

	/// <summary>
	/// Reads the specified reader.
	/// </summary>
	/// <param name="reader">The reader.</param>
	/// <param name="typeToConvert">The type to convert.</param>
	/// <param name="options">The options.</param>
	/// <returns>DateTimeOffset.</returns>
	public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
		=> DateTime.ParseExact(reader.GetString()!, DateTimeFormat, CultureInfo.InvariantCulture);
}