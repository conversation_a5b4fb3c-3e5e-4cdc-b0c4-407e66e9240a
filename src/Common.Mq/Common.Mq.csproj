<Project Sdk="Microsoft.NET.Sdk.Web">
	<Import Project="../Build.Common.properties" />

	<PropertyGroup>
		<AssemblyName>Net.Common.Mq</AssemblyName>
		<RootNamespace>Apex</RootNamespace>
		<NoDefaultLaunchSettingsFile>true</NoDefaultLaunchSettingsFile>
	</PropertyGroup>

	<PropertyGroup>
		<IsPackable>true</IsPackable>
		<GeneratePackageOnBuild>True</GeneratePackageOnBuild>
		<Description>library for bootstraping and helpers for services/microservices</Description>
		<Copyright>Jiffy.AI</Copyright>
		<RepositoryUrl>https://bitbucket.org/jiffy_bb_admin/net-service-common</RepositoryUrl>
		<RepositoryType>git</RepositoryType>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="RabbitMq.Client" Version="7.1.2" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Net.Common" Version="3.0.2" />
		<!--<ProjectReference Include="..\..\..\..\Apex2\net-common\src\Common\Common.csproj" />-->
		<PackageReference Include="Net.Service.Common" Version="3.0.40" />
	</ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="Common.MqTest" />
	</ItemGroup>

	<ItemGroup>
		<Compile Update="Model\Message.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Model\RouteModels.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Options\MqOptions.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Options\SecureMqOptions.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>

		<Compile Update="Services\IMqMgmtService.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\IMqReceiveService.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\IMqSendService.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\MqMgmtService.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\MqReceiveService.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\MqSendService.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
		<Compile Update="Services\MqServiceExtensions.cs">
			<SonarQubeExclude>true</SonarQubeExclude>
		</Compile>
	</ItemGroup>

</Project>
