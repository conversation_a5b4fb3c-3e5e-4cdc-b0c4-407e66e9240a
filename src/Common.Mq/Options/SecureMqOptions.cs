namespace Apex.Options;

/// <summary>
/// Class SecureMqOptions. This class cannot be inherited.
/// </summary>
public sealed class SecureMqOptions
{
	/// <summary>
	/// Gets or sets the value of the host name
	/// </summary>
	/// <value>The URL.</value>
	[ConfigurationKeyName("MQ_DB_URL")]
	public string? Url { get; set; }

	/// <summary>
	/// Gets or sets the value of the username
	/// </summary>
	/// <value>The username.</value>
	[ConfigurationKey<PERSON>ame("MQ_DB_USERNAME")]
	public string? Username { get; set; }

	/// <summary>
	/// Gets or sets the value of the password
	/// </summary>
	/// <value>The password.</value>
	[ConfigurationKeyName("MQ_DB_PASSWORD")]
	public string? Password { get; set; }
}
