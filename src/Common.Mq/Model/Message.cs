namespace Apex.Models;

using System.Text.Json;

/// <summary>
/// Enum MessagePriority
/// </summary>
public enum MessagePriority
{
	/// <summary>
	/// The low
	/// </summary>
	Low,

	/// <summary>
	/// The normal
	/// </summary>
	Normal,

	/// <summary>
	/// The high
	/// </summary>
	High,

	/// <summary>
	/// The critical
	/// </summary>
	Critical,

	/// <summary>
	/// The maximum
	/// </summary>
	Maximum = Critical
}

/// <summary>
/// Class Auth. This class cannot be inherited.
/// </summary>
public sealed record Auth(string TenantId, string AppId);

/// <summary>
/// Class MessageMetadata. This class cannot be inherited.
/// </summary>
public sealed record Metadata(int? Hops = null, string? SourceSystem = null, DateTime? CreatedOn = null, int? TtlSeconds = null, string? SourceId = null, MessagePriority? Priority = null, string? B3TraceId = null);

/// <summary>
/// class IdObject
/// </summary>
public abstract record IdObject
{
	/// <summary>
	/// Initializes a new instance of the <see cref="IdObject"/> class.
	/// </summary>
	/// <param name="id">The identifier.</param>
	protected IdObject(string? id) => Id = id;

	/// <summary>
	/// Gets or sets the identifier.
	/// </summary>
	/// <value>The identifier.</value>
	public string? Id { get; set; }
}

/// <summary>
/// Class Message. This class cannot be inherited.
/// </summary>
public sealed record Message(string? Id, string Type, JsonDocument? Data, Auth Auth, JsonDocument? Additional = null, Metadata? Metadata = null, string? Domain = null) : IdObject(Id);

/// <summary>
/// Class AlertAndDef. This class cannot be inherited.
/// </summary>
public sealed record AlertAndDef(Message Message, AlertDef Def);

/// <summary>
/// Class MessageAndBroadcastAlert. This class cannot be inherited.
/// </summary>
public sealed record BroadcastAlertAndDef(Message Message, AlertDefOverride Def);
