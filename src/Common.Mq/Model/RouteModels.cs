namespace Apex.Models;

using System.Text.Json;
using System.Text.Json.Serialization;

/// <summary>
/// Class TemplateInfo - template info stored as a bunch of handler bars template.
/// </summary>
public sealed partial record TemplateInfo
{
	/// <summary>
	/// Gets or sets to - template.
	/// </summary>
	/// <value>To.</value>
	public string? To { get; init; }

	/// <summary>
	/// Gets or sets the cc - template.
	/// </summary>
	/// <value>The cc.</value>
	public string? Cc { get; init; }

	/// <summary>
	/// Gets or sets the BCC - template.
	/// </summary>
	/// <value>The BCC.</value>
	public string? Bcc { get; init; }

	/// <summary>
	/// Gets or sets the subject - template.
	/// </summary>
	/// <value>The subject.</value>
	public string? Subject { get; init; }

	/// <summary>
	/// Gets or sets the text body - template.
	/// </summary>
	/// <value>The text body.</value>
	public string? TextBody { get; init; }

	/// <summary>
	/// Gets or sets the HTML body - template.
	/// </summary>
	/// <value>The HTML body.</value>
	public string? HtmlBody { get; init; }

	/// <summary>
	/// Gets or sets the priority - template.
	/// </summary>
	/// <value>The priority.</value>
	public string? Priority { get; init; }

	/// <summary>
	/// Gets the attachments.
	/// </summary>
	/// <value>The attachments.</value>
	public string? Attachments { get; init; }
}

/// <summary>
/// Class BaseAlertInfo.
/// </summary>
[JsonPolymorphic]
[JsonDerivedType(typeof(InAppAlertInfo), "inapp")]
[JsonDerivedType(typeof(SmsAlertInfo), "sms")]
[JsonDerivedType(typeof(EmailAlertInfo), "email")]
[JsonDerivedType(typeof(WebhookAlertInfo), "webhook")]
[JsonDerivedType(typeof(WorkflowAlertInfo), "workflow")]
public abstract record BaseAlertInfo
{
	public string? Name { get; set; }
	public bool? IsEnabled { get; set; }
};

/// <summary>
/// Class WorkflowAlertInfo.
/// </summary>
public sealed record WorkflowAlertInfo : BaseAlertInfo
{
	public string? WorkflowDefId { get; set; }
	public string? Variable { get; set; }
}

/// <summary>
/// Class InAppAlertInfo.
/// </summary>
public sealed record InAppAlertInfo : BaseAlertInfo
{
	public string? DefaultTemplateId { get; set; }
	public string? UsersExpression { get; set; }
	public TemplateInfo? InlineTemplate { get; set; }
}

/// <summary>
/// Class SmsAlertInfo.
/// </summary>
public sealed record SmsAlertInfo : BaseAlertInfo
{
	public string? Outbox { get; set; }
	public string? DefaultTemplateId { get; set; }
	public TemplateInfo? InlineTemplate { get; set; }
}

/// <summary>
/// Class EmailAlertInfo.
/// </summary>
public sealed record EmailAlertInfo : BaseAlertInfo
{
	public string? Outbox { get; set; }
	public string? DefaultTemplateId { get; set; }
	public TemplateInfo? InlineTemplate { get; set; }
}

/// <summary>
/// Class WebhookAlertInfo.
/// </summary>
public sealed record WebhookAlertInfo : BaseAlertInfo
{
	public string? Url { get; set; }
	public Dictionary<string, string>? Headers { get; set; }
}

/// <summary>
/// Class ConditionParameter. This class cannot be inherited.
/// </summary>
public sealed record ConditionParameter
(
	[property: JsonRequired] string Name,
	string? DefaultValue = null
);

/// <summary>
/// Class NavigationVariableInfo. This class cannot be inherited.
/// </summary>
public sealed record NavigationVariableInfo
(
	string Name,
	string? ValueExpression
);

/// <summary>
/// Class AlertDef.
/// </summary>
public sealed partial class AlertDef
{
	/// <summary>
	/// Gets or inits the identifier.
	/// </summary>
	/// <value>The identifier.</value>
	[JsonRequired]
	public required string Id { get; init; }

	/// <summary>
	/// Gets or inits the tenant identifier.
	/// </summary>
	/// <value>The tenant identifier.</value>
	[JsonRequired]
	public required string TenantId { get; init; }

	/// <summary>
	/// Gets or inits the application identifier.
	/// </summary>
	/// <value>The application identifier.</value>
	[JsonRequired]
	public required string AppId { get; init; }

	/// <summary>
	/// Gets the name.
	/// </summary>
	/// <value>The name.</value>
	[JsonRequired]
	public required string Name { get; init; }

	/// <summary>
	/// Gets the type.
	/// </summary>
	/// <value>The type.</value>
	[JsonRequired]
	public required string Type { get; init; }

	/// <summary>
	/// Gets the description.
	/// </summary>
	/// <value>The description.</value>
	public string? Description { get; init; }

	/// <summary>
	/// Gets the category.
	/// </summary>
	/// <value>The category.</value>
	public string? Category { get; init; }

	/// <summary>
	/// Gets the tags.
	/// </summary>
	/// <value>The tags.</value>
	public List<string>? Tags { get; init; }

	/// <summary>
	/// Gets or inits a value indicating whether this instance is enabled.
	/// </summary>
	/// <value>
	///   <c>true</c> if this instance is enabled; otherwise, <c>false</c>.</value>
	public bool? IsEnabled { get; init; }

	/// <summary>
	/// Gets the enable condition.
	/// </summary>
	/// <value>The enable expression.</value>
	public string? EnableCondition { get; init; }

	/// <summary>
	/// Gets the condition parameters.
	/// </summary>
	/// <value>The condition parameters.</value>
	public List<ConditionParameter>? ConditionParameters { get; init; }

	/// <summary>
	/// Gets the json schema.
	/// </summary>
	/// <value>The json schema.</value>
	public JsonDocument? JsonSchema { get; init; }

	/// <summary>
	/// Gets the sample JSON.
	/// </summary>
	/// <value>The sample.</value>
	public JsonDocument? Sample { get; init; }

	/// <summary>
	/// Gets the inapp alert.
	/// </summary>
	/// <value>The in application information.</value>
	public InAppAlertInfo? InAppInfo { get; init; }

	/// <summary>
	/// Gets the email alert.
	/// </summary>
	/// <value>The email application.</value>
	public EmailAlertInfo? EmailInfo { get; init; }

	/// <summary>
	/// Gets the sms alert.
	/// </summary>
	/// <value>The SMS information.</value>
	public SmsAlertInfo? SmsInfo { get; init; }

	/// <summary>
	/// Gets the workflow alert.
	/// </summary>
	/// <value>The workflow information.</value>
	public WorkflowAlertInfo? WorkflowInfo { get; init; }

	/// <summary>
	/// Gets the webhook alert.
	/// </summary>
	/// <value>The webhook information.</value>
	public WebhookAlertInfo? WebhookInfo { get; init; }

	/// <summary>
	/// Gets or sets the recipient roles.
	/// </summary>
	/// <value>The recipient roles.</value>
	public string[]? RecipientRoles { get; set; }

	/// <summary>
	/// Gets or sets the can opt out.
	/// </summary>
	/// <value>The can opt out.</value>
	public bool? CanOptOut { get; set; }

	/// <summary>
	/// Gets the navigation variables.
	/// </summary>
	/// <value>The navigation variables.</value>
	public List<NavigationVariableInfo>? NavigationVariables { get; init; }

	/// <summary>
	/// Gets the navigation page url expression.
	/// </summary>
	/// <value>The navigation pageurlexpression.</value>
	public string? NavigationPageUrlExpression { get; init; }

	/// <summary>
	/// Gets the created on.
	/// </summary>
	/// <value>The created on.</value>
	public DateTime? CreatedOn { get; init; }

	/// <summary>
	/// Gets the last modified on.
	/// </summary>
	/// <value>The last modified on.</value>
	public DateTime? LastModifiedOn { get; init; }
}

/// <summary>
/// Class AlertDef override.
/// </summary>
public sealed partial class AlertDefOverride
{
	/// <summary>
	/// Gets or inits a value indicating whether this instance is enabled.
	/// </summary>
	/// <value>
	///   <c>true</c> if this instance is enabled; otherwise, <c>false</c>.</value>
	public bool? IsEnabled { get; init; }

	/// <summary>
	/// Gets the condition parameters.
	/// </summary>
	/// <value>The condition parameters.</value>
	public List<ConditionParameter>? ConditionParameters { get; init; }

	/// <summary>
	/// Gets the inapp alert.
	/// </summary>
	/// <value>The in application information.</value>
	public InAppAlertInfo? InAppInfo { get; init; }

	/// <summary>
	/// Gets the email alert.
	/// </summary>
	/// <value>The email application.</value>
	public EmailAlertInfo? EmailInfo { get; init; }

	/// <summary>
	/// Gets the sms alert.
	/// </summary>
	/// <value>The SMS information.</value>
	public SmsAlertInfo? SmsInfo { get; init; }

	/// <summary>
	/// Gets the workflow alert.
	/// </summary>
	/// <value>The workflow information.</value>
	public WorkflowAlertInfo? WorkflowInfo { get; init; }

	/// <summary>
	/// Gets the webhook alert.
	/// </summary>
	/// <value>The webhook information.</value>
	public WebhookAlertInfo? WebhookInfo { get; init; }

	/// <summary>
	/// Gets or sets the recipient roles.
	/// </summary>
	/// <value>The recipient roles.</value>
	public string[]? RecipientRoles { get; set; }

	/// <summary>
	/// Gets or sets the recipient orgs.
	/// </summary>
	/// <value>The recipient orgs.</value>
	public string[]? RecipientOrgs { get; set; }

	/// <summary>
	/// </summary>
	/// <value>The recipient orgs.</value>
	public string[]? RecipientOverides { get; set; }
}