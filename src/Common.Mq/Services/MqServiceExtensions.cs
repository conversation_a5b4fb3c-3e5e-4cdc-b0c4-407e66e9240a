namespace System;
using Apex.Options;
using Apex.Services;
using Microsoft.Extensions.Options;
using RabbitMQ.Client;

/// <summary>
/// class MqServiceExtensions
/// </summary>
public static class MqServiceExtensions
{
	/// <summary>
	/// Adds the mq related services.
	/// </summary>
	/// <param name="services">The services.</param>
	/// <param name="configuration">The configuration.</param>
	/// <returns>IServiceCollection.</returns>
	public static IServiceCollection AddMq(this IServiceCollection services, IConfiguration configuration)
	{
		services.AddOptions<MqOptions>().Bind(configuration.GetRequiredSection("MqOptions"));
		services.AddOptions<SecureMqOptions>().Bind(configuration)
			.Validate(mqOptions => !mqOptions.Url.IsNullOrEmpty() && !mqOptions.Username.IsNullOrEmpty() && !mqOptions.Password.IsNullOrEmpty())
			.ValidateOnStart();

		services.AddSingleton<IConnectionFactory>(sp =>
		{
			var mqOptions = sp.GetRequiredService<IOptions<SecureMqOptions>>().Value;
			return new ConnectionFactory
			{
				Uri = new Uri(mqOptions.Url!),
				UserName = mqOptions.Username,
				Password = mqOptions.Password,
			};
		});
		services.AddSingleton(sp => sp.GetRequiredService<IConnectionFactory>().CreateConnectionAsync().Result);
		services.AddScoped(sp => sp.GetRequiredService<IConnection>().CreateChannelAsync().Result);
		services.AddScoped<IMqMgmtService, MqMgmtService>();
		services.AddScoped<IMqSendService, MqSendService>();
		services.AddScoped<IMqReceiveService, MqReceiveService>();
		return services;
	}
}