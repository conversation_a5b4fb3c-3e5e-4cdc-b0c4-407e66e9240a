namespace Apex.Services;

using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System.Text.Json;

/// <summary>
/// Class MqReceiveService.
/// </summary>
/// <remarks>Initializes a new instance of the <see cref="MqReceiveService" /> class.</remarks>
public sealed partial class MqReceiveService(ILogger<MqReceiveService> _logger, IMqMgmtService _mqMgmtService, IChannel _channel) : IMqReceiveService
{
	/// <summary>
	/// Starts to receive the messages.
	/// </summary>
	/// <param name="topicName">Name of the topic.</param>
	/// <param name="isDurable">if set to <c>true</c> [is durable].</param>
	/// <param name="processMessage">The process message.</param>
	/// <returns>A Task representing the asynchronous operation.</returns>
	public async Task<IAsyncDisposable> StartReceiveAsync<T>(string topicName, bool isDurable, Func<T, Task<(bool, MessageNack)>> processMessage) where T : class
	{
		var nTopicName = await _mqMgmtService.CreateQueueAsync(topicName, isDurable);
		_logger.LogDebug("Receive - Upsert topic - TopicName={TopicName}", nTopicName);

		var consumer = new AsyncEventingBasicConsumer(_channel);
		consumer.ReceivedAsync += async (model, ea) =>
		{
			var dtag = ea.DeliveryTag;
			_logger.LogDebug("Receive - received message - TopicName={TopicName} DTag={DTag}", nTopicName, dtag);

			try
			{
				var body = ea.Body.ToArray();
				var message = JsonSerializer.Deserialize<T>(body, SerializationExtensions._SERIALIZEROPTIONS);

				try
				{
					_logger.LogDebug("Receive - processing message - TopicName={TopicName} DTag={DTag}", nTopicName, dtag);
					var (success, nack) = await processMessage(message!);
					if (success)
					{
						_logger.LogDebug("Receive - processing message - ACK - TopicName={TopicName} DTag={DTag}", nTopicName, dtag);
						await _channel.BasicAckAsync(dtag, false);
					}
					else
					{
						var multiple = (nack & MessageNack.Multiple) == MessageNack.Multiple;
						var requeue = (nack & MessageNack.Requeue) == MessageNack.Requeue;
						_logger.LogDebug("Receive - processing message - NACK - TopicName={TopicName} DTag={DTag} Multiple={Multiple} Requeue={Requeue}",
							nTopicName, dtag, multiple, requeue);
						await _channel.BasicNackAsync(dtag, multiple, requeue);
					}
				}
				catch (Exception ex)
				{
					_logger.LogError(ex, "Receive - Exception while processing message, implicit NACK - DeliveryTag={DeliveryTag} Message={Message}", dtag, message);
					await _channel.BasicNackAsync(dtag, false, true);
				}
			}
			catch (Exception exDeserialize)
			{
				_logger.LogError(exDeserialize, "Receive - Exception while deserializing message, implicit NACK - DeliveryTag={DeliveryTag} Message={Message}", dtag, exDeserialize.Message);

				// dont requeue... serialization error will continue to happen
				await _channel.BasicNackAsync(dtag, false, false);
			}
		};

		// start the listener and consumer handles it
		_logger.LogDebug("Receive - starting listen - TopicName={TopicName}", nTopicName);
		var tag = await _channel.BasicConsumeAsync(queue: nTopicName, autoAck: false, consumer: consumer);

		// return disposable, so client can trigger stop listen
		return new CancelConsumer(_logger, _channel, nTopicName, tag);
	}

	/// <summary>
	/// Class CancelConsumer.
	/// Implements the <see cref="System.IDisposable" />
	/// </summary>
	/// <seealso cref="System.IDisposable" />
	internal sealed class CancelConsumer(ILogger _logger, IChannel? _channel, string _topicName, string _tag) : IAsyncDisposable
	{
		/// <summary>
		/// Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
		/// </summary>
		public async ValueTask DisposeAsync()
		{
			_logger.LogDebug("Receive:CloseChannel - TopicName={TopicName} Tag={Tag}", _topicName, _tag);
			if (_channel != null)
			{
				await _channel.BasicCancelAsync(_tag);
				_channel = null;
			}
		}
	}
}