namespace Apex.Services;

/// <summary>
/// Enum MessageNack
/// </summary>
[Flags]
public enum MessageNack
{
	/// <summary>
	/// The none
	/// </summary>
	None,

	/// <summary>
	/// The nack requeue
	/// </summary>
	Requeue,

	/// <summary>
	/// The nack multiple
	/// </summary>
	Multiple,
}

/// <summary>
/// Class MessageAcknowledgement. This class cannot be inherited.
/// </summary>
public static class MessageAcknowledgement
{
	/// <summary>
	/// The success
	/// </summary>
	public static readonly (bool Success, MessageNack Nack) SUCCESS = (true, MessageNack.None);

	/// <summary>
	/// The failure - requeue failed
	/// </summary>
	public static readonly (bool Success, MessageNack Nack) FAIL_REQUEUE = (false, MessageNack.Requeue);
}

/// <summary>
/// Interface IMqReceiveService
/// </summary>
public partial interface IMqReceiveService
{
	/// <summary>
	/// Starts to receive the messages.
	/// </summary>
	/// <param name="topicName">Name of the topic.</param>
	/// <param name="isDurable">if set to <c>true</c> [is durable].</param>
	/// <param name="processMessage">The process message.</param>
	/// <returns>Task&lt;System.Nullable&lt;MqResponse&gt;&gt;.</returns>
	Task<IAsyncDisposable> StartReceiveAsync<T>(string topicName, bool isDurable, Func<T, Task<(bool, MessageNack)>> processMessage)
		where T : class;
}