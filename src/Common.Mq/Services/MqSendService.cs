namespace Apex.Services;

using Apex.Models;
using RabbitMQ.Client;
using System.Text;

/// <summary>
/// Class MqSendService.
/// </summary>
/// <remarks>Initializes a new instance of the <see cref="MqSendService" /> class.</remarks>
public sealed partial class MqSendService(ILogger<MqSendService> _logger, IMqMgmtService _mqMgmtService, IChannel _channel) : IMqSendService
{
	/// <summary>
	/// Posts the message to the specific topic/exchange/queue.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="topicName">Name of the topic.</param>
	/// <param name="message">The message.</param>
	/// <param name="isDurable">if set to <c>true</c> [is durable].</param>
	/// <returns>System.Threading.Tasks.Task&lt;T&gt;.</returns>
	public async Task<T> PostMessageAsync<T>(string topicName, T message, bool isDurable = true)
		where T : class
	{
		// ensure queue exists
		var namespacedTopic = await _mqMgmtService.CreateQueueAsync(topicName, isDurable);
		_logger.LogDebug("Mq - Publish - Topic={Topic} Message={Message}", namespacedTopic, message);

		// mark message as persisted if needed
		var messageProperties = new BasicProperties
		{
			Persistent = isDurable
		};

		// setup Id
		if (message is IdObject idObject && idObject.Id.IsNullOrEmpty())
		{
			idObject.Id = Guid.NewGuid().ToString("n");
		}

		// setup TTL
		if (message is Message { Metadata: Metadata metadata })
		{
			if (metadata.TtlSeconds is int ttlSeconds)
			{
				messageProperties.Expiration = $"{ttlSeconds * 1000}"; // in MS
			}

			if (metadata.Priority is MessagePriority priority)
			{
				messageProperties.Priority = (byte)priority;
			}
		}

		var messageBytes = Encoding.UTF8.GetBytes(message.ToJSON()!);
		await _channel.BasicPublishAsync(exchange: string.Empty, routingKey: namespacedTopic, mandatory: true, basicProperties: messageProperties,
			body: messageBytes);
		_logger.LogDebug("Mq - Publish Successful - Topic={Topic} Message={Message}", namespacedTopic, message);
		return message;
	}
}