namespace Apex.Services;

/// <summary>
/// Interface IMqSendService
/// </summary>
public partial interface IMqSendService
{
	/// <summary>
	/// Posts the message to the specific topic/exchange/queue.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="topicName">Name of the topic.</param>
	/// <param name="message">The message.</param>
	/// <param name="isDurable">if set to <c>true</c> [is durable].</param>
	/// <returns>Task&lt;T&gt;.</returns>
	Task<T> PostMessageAsync<T>(string topicName, T message, bool isDurable = true)
		where T : class;
}