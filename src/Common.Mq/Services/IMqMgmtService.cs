namespace Apex.Services;

/// <summary>
/// Interface IMqMgmtService
/// </summary>
public partial interface IMqMgmtService
{
	/// <summary>
	/// Creates the queue/topic.
	/// </summary>
	/// <param name="topicName">Name of the topic.</param>
	/// <param name="isDurable">if set to <c>true</c> [is durable].</param>
	/// <returns>Task.</returns>
	Task<string> CreateQueueAsync(string topicName, bool isDurable);
}