namespace Apex.Services;

using Apex.Models;
using Apex.Options;
using Microsoft.Extensions.Options;
using RabbitMQ.Client;

/// <summary>
/// Class Mq Mgmt Service.
/// </summary>
/// <remarks>Initializes a new instance of the <see cref="MqMgmtService" /> class.</remarks>
public sealed partial class MqMgmtService(ILogger<MqMgmtService> _logger, IOptions<DeploymentOptions> _deploymentOptions, IChannel _channel) : IMqMgmtService
{
	/// <summary>
	/// Creates the queue/topic.
	/// </summary>
	/// <param name="topicName">Name of the topic.</param>
	/// <param name="isDurable">if set to <c>true</c> [is durable].</param>
	/// <returns>A Task representing the asynchronous operation.</returns>
	public async Task<string> CreateQueueAsync(string topicName, bool isDurable)
	{
		var namespacedTopic = _deploymentOptions.Value.MakeQueueName(topicName);
		_logger.LogDebug("Mq - queue create - Topic={Topic} IsDurable={IsDurable}", namespacedTopic, isDurable);
		if (_channel.IsClosed)
		{
			_logger.LogError("RabbitMq channel is closed. Message not sent.");
			throw new InvalidOperationException("RabbitMq channel is closed.");
		}

		// fix priority to max value...
		var props = new Dictionary<string, object>
		{
			["x-max-priority"] = (int)MessagePriority.Maximum
		};

		// idempotent create - ignore if exists
		await _channel.QueueDeclareAsync(queue: namespacedTopic, durable: isDurable, exclusive: false, autoDelete: false, arguments: props);
		_logger.LogDebug("Mq - queue create successful - Topic={Topic}", namespacedTopic);
		return namespacedTopic;
	}
}