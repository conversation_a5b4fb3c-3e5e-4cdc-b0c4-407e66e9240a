using Mq.Controllers.Examples;
using Swashbuckle.AspNetCore.Filters;

namespace Mq.Controllers;

using Microsoft.AspNetCore.Mvc;
using MQM = Mq.Models;

/// <summary>
/// Class MqController.
/// </summary>
public sealed partial class MqController
{
	/// <summary>
	/// Posts the message into the specific topic/queue.
	/// </summary>
	/// <param name="message">The message.</param>
	/// <param name="topicName">Name of the topic.</param>
	/// <returns>IActionResult.</returns>
	[HttpPost("durable/{topicName}")]
	[HttpPut("durable/{topicName}")]
	[ProducesResponseType(200, Type = typeof(MQM.Message))]
	[SwaggerRequestExample(typeof(MQM.Message), typeof(PublishNewEventExample))]
	public async Task<IActionResult> PostDurableMessage([FromBody] MQM.Message message, [FromRoute] string topicName = "default")
	{
		var newMessage = message.ToMessage(this.GetCurrentTenantId(), this.GetCurrentAppId());
		var result = await _mqSendService.PostMessageAsync(topicName, newMessage, isDurable: true);
		return Ok(result);
	}

	/// <summary>
	/// Posts the message into the specific topic/queue.
	/// </summary>
	/// <param name="topicName">Name of the topic.</param>
	/// <param name="message">The message.</param>
	/// <returns>IActionResult.</returns>
	[HttpPost("nondurable/{topicName}")]
	[HttpPut("nondurable/{topicName}")]
	[ProducesResponseType(203)]
	[SwaggerRequestExample(typeof(MQM.Message), typeof(PublishNewEventExample))]
	public async Task<IActionResult> PostNonDurableMessage([FromBody] MQM.Message message, [FromRoute] string topicName = "default")
	{
		var newMessage = message.ToMessage(this.GetCurrentTenantId(), this.GetCurrentAppId());
		await _mqSendService.PostMessageAsync(topicName, newMessage, isDurable: false);
		return NoContent();
	}
}