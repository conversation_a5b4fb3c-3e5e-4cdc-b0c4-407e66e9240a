namespace Mq.Controllers;

using Apex.Service.AuthHandlers.Scheme;
using Apex.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

/// <summary>
/// Class MqController.
/// </summary>
[Authorize(AuthenticationSchemes = JiffyAuthSchemeConstants.JiffyAuthScheme)]
[ApiController]
[Route("v{version:apiVersion}")]
[Produces("application/json")]
[ProducesResponseType(typeof(ProblemDetails), 500)]
[ApiExplorerSettings(GroupName = "Message Post")]
public sealed partial class MqController(IMqSendService _mqSendService) : ControllerBase
{
}