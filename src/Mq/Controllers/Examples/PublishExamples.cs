using Apex.Models;
using Swashbuckle.AspNetCore.Filters;
using MQM = Mq.Models;

namespace Mq.Controllers.Examples;

/// <summary>
/// Class PublishNewEventExample. This class cannot be inherited.
/// </summary>
public sealed class PublishNewEventExample : IMultipleExamplesProvider<MQM.Message>
{
	/// <summary>
	/// Gets the examples
	/// </summary>
	/// <returns>The new event</returns>
	public IEnumerable<SwaggerExample<MQM.Message>> GetExamples()
	{
		MQM.Message[] samples =
		[
			new MQM.Message
			(
				null,
				"invoice.created",
				"""
				{
					"invoice": {
						"invoice": "inv111",
						"subtotal": 2000.0,
						"tax": 100.0,
						"total": 2100.0,
						"items": [
							{ "id": "item111", "quantity": 20.0, "itemprice": 100.0, "total": 2000.0 },
						]
					}
				}
				""".ToJsonDocument(),
				"""
				{
					"messageId": "msg123"
				}
				""".ToJsonDocument(),
				new Metadata
				(
					CreatedOn: DateTime.UtcNow,
					SourceSystem: "Email"
				)
			),

			new MQM.Message
			(
				"invoice1",
				"invoice.created",
				"""
				{
					"invoice": {
						"invoice": "inv111",
						"subtotal": 2000.0,
						"tax": 100.0,
						"total": 2100.0,
						"items": [
							{ "id": "item111", "quantity": 20.0, "itemprice": 100.0, "total": 2000.0 },
						]
					}
				}
				""".ToJsonDocument(),
				"""
				{
					"messageId": "msg123"
				}
				""".ToJsonDocument(),
				new Metadata
				(
					CreatedOn: DateTime.UtcNow,
					SourceSystem: "Email"
				)
			),

			new MQM.Message
			(
				"invoice2",
				"invoice.created",
				"""
				{
					"invoice": {
						"invoice": "inv111",
						"subtotal": 200000.0,
						"tax": 100.0,
						"total": 200100.0,
						"items": [
							{ "id": "item111", "quantity": 10.0, "itemprice": 10000.0, "total": 100000.0 },
							{ "id": "item112", "quantity": 1.0, "itemprice": 100000.0, "total": 100000.0 }
						]
					}
				}
				""".ToJsonDocument(),
				"""
				{
					"messageId": "msg123"
				}
				""".ToJsonDocument(),
				new Metadata
				(
					CreatedOn: DateTime.UtcNow,
					SourceSystem: "Email"
				)
			),
		];

		return samples.Select(e => new SwaggerExample<MQM.Message>
		{
			Name = $"{e.Id}-{e.Type}",
			Value = e
		});
	}
}