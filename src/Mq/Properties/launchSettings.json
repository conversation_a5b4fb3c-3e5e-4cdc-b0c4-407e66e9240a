{"$schema": "https://json.schemastore.org/launchsettings.json", "profiles": {"Mq": {"commandName": "Project", "launchBrowser": false, "launchUrl": "tp/swagger-ui", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "http://localhost:5000", "dotnetRunMessages": true}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/mq/swagger-ui", "publishAllPorts": true}}}