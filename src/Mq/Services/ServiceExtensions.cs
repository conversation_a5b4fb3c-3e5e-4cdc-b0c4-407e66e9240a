namespace System;

using Apex.Options;
using Apex.Services;

/// <summary>
/// class ServiceExtensions
/// </summary>
public static class ServiceExtensions
{
	/// <summary>
	/// Adds the mq needed services.
	/// </summary>
	/// <param name="services">The services.</param>
	/// <param name="configuration">The configuration.</param>
	/// <returns>Task&lt;System.Nullable&lt;MqResponse&gt;&gt;.</returns>
	public static IServiceCollection AddMqServices(this IServiceCollection services, IConfiguration configuration)
	{
		services.AddOptions<IamOptions>().Bind(configuration.GetRequiredSection("IAM"));
		services.AddOptions<SecureIamOptions>().Bind(configuration);
		services.AddOptions<DeploymentOptions>().Bind(configuration.GetRequiredSection("DeploymentOptions"));

		services.AddOptions<ServiceOptions>().Bind(configuration.GetRequiredSection("ServiceOptions"));

		services.AddWebConnectionExtensions();
		services.AddSingleton<ITokenGenerationService, PlatformTokenGenerationService>();

		return services.AddMq(configuration);
	}
}