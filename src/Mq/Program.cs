var listenPort = 5000;

var userProfileFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), "secrets-store");
await Apex.BaseProgram<Mq.Startup>.Main(
	new Apex.BaseProgramStartupInfo
	{
		Args = args,
		ListenPort = listenPort,
		AppConfigurationCallback = (cb) =>
			cb.AddPropertiesFile(Path.Combine(userProfileFolder, "principal.properties"))
				.AddPropertiesFile(Path.Combine(userProfileFolder, "extra.properties"))
	})
	.RunAsync();