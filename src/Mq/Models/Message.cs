namespace Mq.Models;

using System.Text.Json;
using AM = Apex.Models;

/// <summary>
/// Class Message. This class cannot be inherited.
/// </summary>
public sealed record Message(string? Id, string Type, JsonDocument? Data, JsonDocument? Additional = null, AM.Metadata? Metadata = null, string? Domain = null)
{
	/// <summary>
	/// Converts to message to persisted message.
	/// </summary>
	/// <param name="tenantId">The tenant identifier.</param>
	/// <param name="appId">The application identifier.</param>
	/// <returns>Message.</returns>
	public AM.Message ToMessage(string tenantId, string appId)
	{
		var m = this;
		var auth = new AM.Auth(tenantId, appId);
		return new AM.Message(m.Id, m.Type, m.Data, auth, m.Additional, m.Metadata, m.Domain);
	}
}
