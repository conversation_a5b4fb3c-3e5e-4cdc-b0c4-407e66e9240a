<Project Sdk="Microsoft.NET.Sdk.Web">
	<Import Project="../Build.Executable.properties" />

	<PropertyGroup>
		<AssemblyName>Net.Service.Mq</AssemblyName>
		<RootNamespace>Mq</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Asp.Versioning.Mvc" Version="8.1.0" />
		<PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
		<PackageReference Include="RabbitMq.Client" Version="7.1.2" />
		<PackageReference Include="Net.Common.Mq" Version="3.0.14" />
		<PackageReference Include="Net.Service.Common" Version="3.0.40" />
		<!--<ProjectReference Include="..\..\..\net-service-common\src\Service.Common\Service.Common.csproj" />-->
	</ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="MqTest" />
	</ItemGroup>

	<ItemGroup>
		<None Update="appsettings.yaml">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="appsettings.Development.yaml" Condition="$(Configuration) == 'Debug'">
			<DependentUpon>appsettings.yaml</DependentUpon>
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>
	<ItemGroup Condition="$(Configuration) == 'Debug'">
		<None Update="up.cmd">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<Target Name="CopyToSecretsFolder" AfterTargets="AfterBuild" Condition="$(Configuration) == 'Debug'">
		<Message Importance="high" Text="copying principal.properties =&gt; $(USERPROFILE)\secrets-store" />
		<Copy SourceFiles="principal.properties" DestinationFolder="$(USERPROFILE)\secrets-store" SkipUnchangedFiles="true" ContinueOnError="false" />
		<Copy SourceFiles="extra.properties" DestinationFolder="$(USERPROFILE)\secrets-store" SkipUnchangedFiles="true" ContinueOnError="false" />
	</Target>

</Project>
