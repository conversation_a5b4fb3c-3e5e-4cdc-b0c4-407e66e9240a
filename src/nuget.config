<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<!-- defaultPushSource key works like the 'defaultPushSource' key of NuGet.Config files. -->
	<!-- This can be used by administrators to prevent accidental publishing of packages to nuget.org. -->
	<config>
		<add key="defaultPushSource" value="https://repo.jiffy.ai/repository/mq/" />
	</config>

	<!-- Default Package Sources; works like the 'packageSources' section of NuGet.Config files. -->
	<!-- This collection cannot be deleted or modified but can be disabled/enabled by users. -->
	<packageSources>
		<add key="Jiffy Package Source" value="https://repo.jiffy.ai/repository/nuget-group-v3/index.json" />
		<add key="nuget.org" value="https://api.nuget.org/v3/index.json" />
	</packageSources>

	<packageSourceCredentials>
		<Jiffy_x0020_Package_x0020_Source>
			<add key="Username" value="%NEXUS_NUGET_READ_USERNAME%" />
			<add key="ClearTextPassword" value="%NEXUS_NUGET_READ_PASSWORD%" />
		</Jiffy_x0020_Package_x0020_Source>
	</packageSourceCredentials>

	<!-- Default Package Sources that are disabled by default. -->
	<!-- Works like the 'disabledPackageSources' section of NuGet.Config files. -->
	<!-- Sources cannot be modified or deleted either but can be enabled/disabled by users. -->
	<disabledPackageSources>
		<!--<add key="nuget.org" value="true" />-->
	</disabledPackageSources>
</configuration>