# usage:
#
# ------------- to start all services and databases
# docker compose -p mq -f docker-compose-localsetup.yaml up --detach
#

volumes:
  rabbitmq:
  pg-data:
  
services:
  rabbitmq:
    image: rabbitmq:3-management-alpine
    restart: always
    ports:
        - 5672:5672
        - 15672:15672
    volumes:
        - rabbitmq:/data/:/var/lib/rabbitmq/
        - rabbitmq:/log/:/var/log/rabbitmq/

  postgres:
    image: postgres:latest
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - "pg-data:/var/lib/postgresql/data"

  adminer:
    image: adminer
    restart: always
    ports:
      - 8003:8080