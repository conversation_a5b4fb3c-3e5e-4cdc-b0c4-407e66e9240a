FROM registry.jiffy.ai/jiffy/jiffybase:aspnetcoreruntime9.0-25.08.01 AS base

ENV DOTNET_RUNNING_IN_CONTAINER=1
WORKDIR /home/<USER>/app
EXPOSE 5000

FROM registry.jiffy.ai/jiffy/jiffybase:dotnetsdk-9.0-25.08.01 AS build

ENV DOTNET_CLI_TELEMETRY_OPTOUT=1
ENV DOTNET_SKIP_FIRST_TIME_EXPERIENCE=1
ENV DOTNET_NOLOGO=1

USER root
WORKDIR "/home/<USER>/src"
COPY ["Mq/Mq.csproj", "Mq/"]
COPY ["*.properties", "./"]
COPY ["nuget.config", "./"]

ARG NEXUS_NUGET_READ_USERNAME
ENV NEXUS_NUGET_READ_USERNAME=$NEXUS_NUGET_READ_USERNAME
ARG NEXUS_NUGET_READ_PASSWORD
ENV NEXUS_NUGET_READ_PASSWORD=$NEXUS_NUGET_READ_PASSWORD
ARG PackageVersion
ENV PackageVersion=$PackageVersion
ARG GitCommitHash
ENV GitCommitHash=$GitCommitHash

RUN dotnet restore "Mq/Mq.csproj" --runtime linux-x64
COPY . .
USER jiffy

FROM build AS publish
USER root
RUN dotnet publish "Mq/Mq.csproj" --no-restore --no-self-contained -c Release --runtime linux-x64 -o /home/<USER>/publish 
USER jiffy

FROM base AS final
ARG commitId
ARG componentVersion
LABEL GITCOMMITID="${commitId}"
LABEL COMPONENTVERSION="${componentVersion}"
COPY --from=publish /home/<USER>/publish .
ENTRYPOINT ["/home/<USER>/app/Net.Service.Mq"]

#Below line is used for debugging any image issues, comment above line, uncomment below line, start - docker run --it --rm -p "5000:5000" <imagename>
#ENTRYPOINT ["/bin/sh"]
