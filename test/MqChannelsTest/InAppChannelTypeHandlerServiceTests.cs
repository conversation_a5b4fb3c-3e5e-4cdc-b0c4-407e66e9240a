using Apex.Models;
using Apex.Services;
using Microsoft.Extensions.Logging;
using Moq;
using MqChannels.Services;

namespace MqChannels.Tests;

/// <summary>
/// The in app channel type handler tests class
/// </summary>
public sealed class InAppChannelTypeHandlerTests
{
	/// <summary>
	/// The logger mock
	/// </summary>
	private readonly Mock<ILogger<InAppChannelTypeHandler>> _loggerMock;

	private readonly Mock<IInAppHistoryService> _inappNotificationService = new();

	/// <summary>
	/// The handler
	/// </summary>
	private readonly InAppChannelTypeHandler _handler;

	/// <summary>
	/// Initializes a new instance of the <see cref="InAppChannelTypeHandlerTests"/> class
	/// </summary>
	public InAppChannelTypeHandlerTests()
	{
		_loggerMock = new Mock<ILogger<InAppChannelTypeHandler>>();
		var _serviceOptions = Microsoft.Extensions.Options.Options.Create(new Apex.Options.ServiceOptions());
		_handler = new InAppChannelTypeHandler(_loggerMock.Object, _serviceOptions, _inappNotificationService!.Object);
	}

	/// <summary>
	/// Tests that process message async returns true and none
	/// </summary>
	[Fact]
	public async Task ProcessMessageAsync_ReturnsTrueAndNone()
	{

		var message = new Message("1", null, null, null, null);
		var result = await _handler.ProcessMessageAsync(message);
		Assert.True(result.Item1);
		Assert.Equal(MessageNack.None, result.Item2);

	}
}
