using Apex.Options;
using Apex.Services;
using Common.Postgres;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Moq;
using MqChannels.Services;
using System.Data;
using Options = Microsoft.Extensions.Options.Options;

namespace MqChannelsTest;

/// <summary>
/// Class ProgramTests.
/// </summary>
public sealed partial class ProgramTests : IClassFixture<TestFixure>
{
	/// <summary>
	/// Gets the mocked web application.
	/// </summary>
	/// <param name="callback">The callback.</param>
	/// <returns>WebApplicationFactory&lt;Program&gt;.</returns>
	internal static WebApplicationFactory<Program> GetMockedWebApplication(Action<IWebHostBuilder>? callback = null)
	{
		return GetWebApplication((builder) =>
		{
			builder.ConfigureServices((context, services) =>
			{
				//var subscriptionMock = new Mock<IAsyncSubscription>();
				//subscriptionMock.Setup(x => x.Start());

				//var connectionMock = new Mock<IConnection>();
				//connectionMock.Setup(x => x.SubscribeAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<EventHandler<MsgHandlerEventArgs>>()))
				//	.Returns(subscriptionMock.Object);

				//var connectionFactoryMock = new Mock<IConnectionFactory>();
				//connectionFactoryMock.Setup(x => x.CreateConnection(It.IsAny<string>()))
				//	.Returns(connectionMock.Object);

				//services.AddTransient((sp) => connectionFactoryMock.Object);

				var pgRepositoryMock = new Mock<IPgRepository2>();
				pgRepositoryMock.Setup(x => x.ExecuteNonQueryAsync(It.IsAny<string>(), It.IsAny<object?>(), It.IsAny<IDbTransaction>()))
					.ReturnsAsync(() => 1);
				services.AddTransient(sp => pgRepositoryMock.Object);

				var disposableMock = new Mock<IAsyncDisposable>();
				disposableMock.Setup(x => x.DisposeAsync());

				var receiveService = new Mock<IMqReceiveService>();
				receiveService.Setup(x => x.StartReceiveAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<Func<Apex.Models.Message, Task<(bool, MessageNack)>>>()))
					.ReturnsAsync(() => disposableMock.Object);
				services.AddTransient(sp => receiveService.Object);

				services.AddSingleton(sp => Options.Create(new SecureMqOptions
				{
				}));
			});

			callback?.Invoke(builder);
		});
	}

	/// <summary>
	/// Get web application.
	/// </summary>
	/// <param name="cb">The cb.</param>
	/// <returns>A Task&lt;WebApplicationFactory`1&gt; representing the asynchronous operation.</returns>
	internal static WebApplicationFactory<Program> GetWebApplication(Action<IWebHostBuilder>? cb = null)
	{
		var application = new WebApplicationFactory<Program>()
		.WithWebHostBuilder(builder =>
		{
			builder.UseSetting("ASPNETCORE_ENVIRONMENT", "Development");

			cb?.Invoke(builder);
		});
		return application;
	}

	/// <summary>
	/// Defines the test method TestRootEndpoint.
	/// </summary>
	[Fact]
	public async Task TestRootEndpoint()
	{
		await using var application = GetMockedWebApplication();
		using var client = application.CreateClient();
		Assert.NotNull(client);
	}

	/// <summary>
	/// Defines the test method TestRootEndpoint_BuildVersion.
	/// </summary>
	[Fact]
	public async Task TestRootEndpoint_BuildVersion()
	{
		await using var application = GetMockedWebApplication();
		using var client = application.CreateClient();
		var result = client.GetStringAsync("/version");
		Assert.NotNull(result);
	}

	// /// <summary>
	// /// Defines the test method Test_InAppChannelBackgroundService.
	// /// </summary>
	// [Fact]
	// public async Task Test_InAppChannelBackgroundService()
	// {
	// 	await using var application = GetMockedWebApplication();
	// 	using var client = application.CreateClient();
	// 	await Task.Delay(TimeSpan.FromSeconds(2));
	//
	// 	var inappChannelService = application.Services.GetServices<IHostedService>().FirstOrDefault(s => s is InAppChannelBackgroundService);
	// 	Assert.NotNull(inappChannelService);
	//
	// 	using var source = new CancellationTokenSource();
	// 	var cancellationToken = source.Token;
	// 	await inappChannelService.StartAsync(cancellationToken);
	//
	// 	// wait for processing
	// 	await Task.Delay(TimeSpan.FromSeconds(10));
	//
	// 	await source.CancelAsync();
	// 	await inappChannelService.StopAsync(cancellationToken);
	// }

	/// <summary>
	/// Defines the test method Test_SchemaCreatorBackgroundService.
	/// </summary>
	[Fact]
	public async Task Test_SchemaCreatorBackgroundService()
	{
		await using var application = GetMockedWebApplication();
		using var client = application.CreateClient();
		await Task.Delay(TimeSpan.FromSeconds(2));

		var inappChannelService = application.Services.GetServices<IHostedService>().FirstOrDefault(s => s is SchemaCreatorBackgroundService);
		Assert.NotNull(inappChannelService);

		using var source = new CancellationTokenSource();
		var cancellationToken = source.Token;
		await inappChannelService.StartAsync(cancellationToken);

		// wait for processing
		await Task.Delay(TimeSpan.FromSeconds(10));

		await source.CancelAsync();
		await inappChannelService.StopAsync(cancellationToken);
	}
}