using Apex.Service.AuthHandlers.Scheme;
using System.Security.Claims;

namespace MqChannelsTest;

/// <summary>
/// Class Utils.
/// </summary>
public sealed class Utils
{
	/// <summary>
	/// The user{CC2D43FA-BBC4-448A-9D0B-7B57ADF2655C}
	/// </summary>
	public static ClaimsPrincipal user = new(new ClaimsIdentity
	([
		new Claim(JiffyAuthSchemeConstants.CLAIM_Jiffy_User_ID, "testUserId"),
		new Claim(JiffyAuthSchemeConstants.CLAIM_Jiffy_Tenant_ID, "testTenantId"),
		new Claim(JiffyAuthSchemeConstants.CLAIM_Jiffy_App_ID, "testAppId"),
	]));
}