using Common.Postgres;
using Microsoft.Extensions.Logging;
using Moq;
using System.Data;
using MqChannels.Services;

namespace MqChannelsTest;

/// <summary>
/// Tests for SQL schema creation and validation
/// </summary>
public sealed class SqlSchemaTests : IClassFixture<TestFixure>
{
    private readonly Mock<IPgRepository2> _repositoryMock;
    private readonly Mock<ILogger<SchemaCreatorBackgroundService>> _loggerMock;

    public SqlSchemaTests()
    {
        _repositoryMock = new Mock<IPgRepository2>();
        _loggerMock = new Mock<ILogger<SchemaCreatorBackgroundService>>();
    }

    /// <summary>
    /// Tests that schema creation executes without errors
    /// </summary>
    [Fact]
    public async Task SchemaCreatorBackgroundService_ShouldExecuteSchemaScript_Successfully()
    {
        // Arrange
        _repositoryMock.Setup(x => x.ExecuteNonQueryAsync(It.IsAny<string>(), It.IsAny<object?>(), It.IsAny<IDbTransaction>()))
            .ReturnsAsync(1);

        var service = new SchemaCreatorBackgroundService(_loggerMock.Object, _repositoryMock.Object);

        // Act
        await service.StartAsync(CancellationToken.None);
        await Task.Delay(100); // Give it time to execute
        await service.StopAsync(CancellationToken.None);

        // Assert
        _repositoryMock.Verify(x => x.ExecuteNonQueryAsync(It.IsAny<string>(), It.IsAny<object?>(), It.IsAny<IDbTransaction>()), Times.Once);
    }

    /// <summary>
    /// Tests that schema script contains expected indexes
    /// </summary>
    [Fact]
    public void SchemaScript_ShouldContainExpectedIndexes()
    {
        // Arrange
        var schemaScript = MqChannels.Properties.Resources.MqChannelsSchemaScript;

        // Assert
        Assert.Contains("CREATE INDEX IF NOT EXISTS IDX_in_app_alerts_read", schemaScript);
        Assert.Contains("CREATE INDEX IF NOT EXISTS IDX_in_app_alerts_foruser", schemaScript);
        Assert.Contains("CREATE INDEX IF NOT EXISTS IDX_in_app_alerts_search_vector", schemaScript);
    }

    /// <summary>
    /// Tests that schema script contains expected triggers
    /// </summary>
    [Fact]
    public void SchemaScript_ShouldContainExpectedTriggers()
    {
        // Arrange
        var schemaScript = MqChannels.Properties.Resources.MqChannelsSchemaScript;

        // Assert
        Assert.Contains("CREATE OR REPLACE TRIGGER in_app_alerts_set_lastmod", schemaScript);
        Assert.Contains("CREATE OR REPLACE TRIGGER email_alerts_set_lastmod", schemaScript);
        Assert.Contains("CREATE OR REPLACE TRIGGER broadcast_alerts_set_lastmod", schemaScript);
        Assert.Contains("CREATE OR REPLACE TRIGGER set_search_vector", schemaScript);
    }

    /// <summary>
    /// Tests that schema script contains expected functions
    /// </summary>
    [Fact]
    public void SchemaScript_ShouldContainExpectedFunctions()
    {
        // Arrange
        var schemaScript = MqChannels.Properties.Resources.MqChannelsSchemaScript;

        // Assert
        Assert.Contains("CREATE OR REPLACE FUNCTION update_dt()", schemaScript);
        Assert.Contains("CREATE OR REPLACE FUNCTION update_search_vector()", schemaScript);
    }
}
