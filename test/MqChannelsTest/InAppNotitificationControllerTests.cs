using Apex.Service.AuthHandlers.Scheme;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using MqChannels.Controllers;
using MqChannels.Models;
using MqChannels.Services;

namespace MqChannelsTest;

/// <summary>
/// The in app notifications controller tests class
/// </summary>
public sealed class InAppAlertsControllerTests
{
	/// <summary>
	/// The logger mock
	/// </summary>
	private readonly Mock<ILogger<InAppAlertsController>> _loggerMock;
	/// <summary>
	/// The service mock
	/// </summary>
	private readonly Mock<IInAppHistoryService> _serviceMock;
	/// <summary>
	/// The controller
	/// </summary>
	private readonly InAppAlertsController _controller;

	/// <summary>
	/// Initializes a new instance of the <see cref="InAppAlertsControllerTests"/> class
	/// </summary>
	public InAppAlertsControllerTests()
	{
		_loggerMock = new Mock<ILogger<InAppAlertsController>>();
		_serviceMock = new Mock<IInAppHistoryService>();

		_controller = new InAppAlertsController(_loggerMock.Object, _serviceMock.Object);

		_controller.ControllerContext = new ControllerContext();
		_controller.ControllerContext.HttpContext = new DefaultHttpContext();
		_controller.ControllerContext.HttpContext.User = Utils.user;
		var headers = _controller.ControllerContext.HttpContext.Request.Headers;
		headers[JiffyAuthSchemeConstants.HEADER_X_Jiffy_App_ID] = "1";
		headers[JiffyAuthSchemeConstants.HEADER_X_Jiffy_Tenant_ID] = "1";
		headers[JiffyAuthSchemeConstants.HEADER_X_Jiffy_User_ID] = "user1";
	}

	/// <summary>
	/// Tests that get notifications async returns ok result with notifications
	/// </summary>
	[Fact]
	public async Task GetAlertsAsync_ReturnsOkResultWithAlerts()
	{
		var notifications = new List<InAppAlertView>
			{
				new InAppAlertView { id = "1", subject = "Alert1", user_id = "testUserId" },
				new InAppAlertView { id = "2", subject = "Alert2", user_id = "testUserId" }
			};

		_serviceMock.Setup(service => service.GetInAppAlertsByUserAsync("testTenantId", "testAppId", "testUserId", 1, 100, null, null, null, null))!
			.ReturnsAsync(notifications);

		var result = await _controller.GetAlertsAsync();

		var okResult = Assert.IsType<OkObjectResult>(result);
		var returnValue = Assert.IsAssignableFrom<IEnumerable<InAppAlert>>(okResult.Value);
		Assert.Equal(notifications.Count, returnValue.Count());
	}

	/// <summary>
	/// Tests that get notification by id async returns ok result with notification
	/// </summary>
	[Fact]
	public async Task GetAlertByIdAsync_ReturnsOkResultWithAlert()
	{
		var notificationId = "1";
		var notification = new InAppAlertView { id = notificationId, subject = "Alert1", user_id = "testUserId" };

		_serviceMock.Setup(service => service.GetInAppAlertByIdAsync("testTenantId", "testAppId", notificationId))
			.ReturnsAsync(notification);

		var result = await _controller.GetAlertByIdAsync(notificationId);

		var okResult = Assert.IsType<OkObjectResult>(result);
		var returnValue = Assert.IsAssignableFrom<InAppAlert>(okResult.Value);
		Assert.Equal(notificationId, returnValue.id);
	}

	/// <summary>
	/// Tests that insert notification async returns ok result
	/// </summary>
	[Fact]
	public async Task InsertAlertAsync_ReturnsOkResult()
	{
		var notification = new InAppAlert
		{
			id = "1",
			subject = "Alert1",
		};

		_serviceMock.Setup(service => service.UpsertInAppAlertAsync(It.IsAny<InAppAlert>()))
			.ReturnsAsync(1);

		var result = await _controller.UpsertAlertAsync(notification);

		var okResult = Assert.IsType<OkObjectResult>(result);
		var returnValue = Assert.IsType<int>(okResult.Value);
		Assert.Equal(1, returnValue);
	}

	/// <summary>
	/// Tests that update notification async returns ok result
	/// </summary>
	[Fact]
	public async Task UpdateAlertAsync_ReturnsOkResult()
	{
		var notification = new InAppAlert
		{
			id = "1",
			subject = "UpdatedAlert",
		};

		_serviceMock.Setup(service => service.UpsertInAppAlertAsync(notification))
			.ReturnsAsync(1);


		var result = await _controller.UpsertAlertAsync(notification);

		var okResult = Assert.IsType<OkObjectResult>(result);
		var returnValue = Assert.IsType<int>(okResult.Value);
		Assert.Equal(0, returnValue);
	}

	/// <summary>
	/// Tests that delete notification by id async returns ok result
	/// </summary>
	[Fact]
	public async Task DeleteAlertByIdAsync_ReturnsOkResult()
	{
		var notificationId = "1";

		_serviceMock.Setup(service => service.DeleteInAppAlertByIdAsync("testTenantId", "testAppId", notificationId))
			.ReturnsAsync(1);

		var result = await _controller.DeleteAlertByIdAsync(notificationId);

		var okResult = Assert.IsType<OkObjectResult>(result);
		var returnValue = Assert.IsType<int>(okResult.Value);
		Assert.True(returnValue == 1);
	}
}

/// <summary>
/// The controller extensions class
/// </summary>
public static class ControllerExtensions
{
	/// <summary>
	/// Gets the current app id using the specified controller
	/// </summary>
	/// <param name="controller">The controller</param>
	/// <returns>The string</returns>
	public static string GetCurrentAppId(this ControllerBase controller)
	{
		return "testAppId";
	}

	/// <summary>
	/// Gets the current tenant id using the specified controller
	/// </summary>
	/// <param name="controller">The controller</param>
	/// <returns>The string</returns>
	public static string GetCurrentTenantId(this ControllerBase controller)
	{
		return "testTenantId";
	}

	/// <summary>
	/// Gets the current user id using the specified controller
	/// </summary>
	/// <param name="controller">The controller</param>
	/// <returns>The string</returns>
	public static string GetCurrentUserId(this ControllerBase controller)
	{
		return "testUserId";
	}
}
