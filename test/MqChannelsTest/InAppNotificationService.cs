using Common.Postgres;
using Microsoft.Extensions.Logging;
using Moq;
using MqChannels.Models;
using MqChannels.Services;
using System.Data;


namespace MqChannelsTest;

/// <summary>
/// The in app notification service tests class
/// </summary>
public sealed class InAppAlertServiceTests
{
	/// <summary>
	/// The service
	/// </summary>
	private readonly IInAppHistoryService _service;

	/// <summary>
	/// The pg repository mock
	/// </summary>
	private readonly Mock<IPgRepository2> _pgRepositoryMock;

	/// <summary>
	/// Initializes a new instance of the <see cref="InAppAlertServiceTests"/> class
	/// </summary>
	public InAppAlertServiceTests()
	{
		Mock<ILogger<InAppHistoryService>> loggerMock = new();
		_pgRepositoryMock = new Mock<IPgRepository2>();
		_service = new InAppHistoryService(loggerMock.Object, _pgRepositoryMock.Object);
	}

	/// <summary>
	/// Tests that get all in app notifications async returns list of notifications
	/// </summary>
	[Fact]
	public async Task GetAllInAppAlertsAsync_ReturnsListOfNotifications()
	{
		var tenantId = "tenant1";
		var appId = "app1";
		var notifications = new List<InAppAlertView>
		{
			new InAppAlertView { id = "1", subject = "Notification1" },
			new InAppAlertView { id = "2", subject = "Notification2" }
		};

		_pgRepositoryMock
			.Setup(repo => repo.QueryAsync<InAppAlertView>(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<IDbTransaction>()))!
			.ReturnsAsync(notifications);


		var result = await _service.GetAllInAppAlertsAsync(tenantId, appId);
		Assert.Equal(notifications, result);
	}

	/// <summary>
	/// Tests that get in app notifications by user async returns notifications for user
	/// </summary>
	[Fact]
	public async Task GetInAppAlertsByUserAsync_ReturnsNotificationsForUser()
	{
		var tenantId = "tenant1";
		var appId = "app1";
		var userId = "user1";
		var notifications = new List<InAppAlertView>
		{
			new InAppAlertView { id = "1", subject = "Notification1", user_id = userId },
			new InAppAlertView { id = "2", subject = "Notification2", user_id = userId }
		};

		_pgRepositoryMock
			.Setup(repo => repo.QueryAsync<InAppAlertView>(It.IsAny<string>(), It.IsAny<object?>(),
				It.IsAny<IDbTransaction?>()))!
			.ReturnsAsync(notifications);


		var result = await _service.GetInAppAlertsByUserAsync(tenantId, appId, userId);
		Assert.Equal(notifications, result);
	}

	/// <summary>
	/// Tests that get in app notification by id async returns notification by id
	/// </summary>
	[Fact]
	public async Task GetInAppAlertByIdAsync_ReturnsNotificationById()
	{
		var tenantId = "tenant1";
		var appId = "app1";
		var id = "notif1";
		var notification = new InAppAlertView { id = id, subject = "Notification1" };

		_pgRepositoryMock
			.Setup(repo => repo.GetByIdAsync<InAppAlertView>(It.IsAny<TenantAppScopedObjectWithId>()))
			.ReturnsAsync(notification);

		var result = await _service.GetInAppAlertByIdAsync(tenantId, appId, id);
		Assert.Equal(notification, result);
		_pgRepositoryMock.Verify(repo => repo.GetByIdAsync<InAppAlertView>(
			It.Is<TenantAppScopedObjectWithId>(t =>
				t.tenant_id == tenantId && t.app_id == appId && t.id == id)
		), Times.Once);
	}

	/// <summary>
	/// Tests that insert in app notification async inserts notification
	/// </summary>
	[Fact]
	public async Task InsertInAppAlertAsync_InsertsNotification()
	{
		var notification = new InAppAlert { id = "notif1", subject = "Notification1" };

		_pgRepositoryMock
			.Setup(repo => repo.InsertAsync(It.IsAny<InAppAlert>()))
			.ReturnsAsync(true);


		var result = await _service.UpsertInAppAlertAsync(notification);
		Assert.True(result >= 0);
		_pgRepositoryMock.Verify(repo => repo.QueryAsync<UpsertResult>(
			It.IsAny<string>(), It.IsAny<object>(), It.IsAny<IDbTransaction>()
		), Times.Once);
	}

	/// <summary>
	/// Tests that update in app notification async updates notification
	/// </summary>
	[Fact]
	public async Task UpdateInAppAlertAsync_UpdatesNotification()
	{
		var notification = new InAppAlert { id = "notif1", subject = "UpdatedNotification" };

		_pgRepositoryMock
			.Setup(repo => repo.UpdateAsync(It.IsAny<InAppAlert>()))
			.ReturnsAsync(true);

		var result = await _service.UpsertInAppAlertAsync(notification);
		Assert.True(result >= 0);
		_pgRepositoryMock.Verify(repo => repo.QueryAsync<UpsertResult>(
			It.IsAny<string>(), It.IsAny<object>(), It.IsAny<IDbTransaction>()
		), Times.Once);
	}

	/// <summary>
	/// Tests that delete in app notification by id async deletes notification by id
	/// </summary>
	[Fact]
	public async Task DeleteInAppAlertByIdAsync_DeletesNotificationById()
	{
		var tenantId = "tenant1";
		var appId = "app1";
		var id = "notif1";
		var expectedResult = 1;

        _pgRepositoryMock
            .Setup(repo => repo.ExecuteNonQueryAsync(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<IDbTransaction>()))
            .ReturnsAsync(expectedResult);

		var result = await _service.DeleteInAppAlertByIdAsync(tenantId, appId, id);

		Assert.Equal(expectedResult, result);
	}

	/// <summary>
	/// Tests that delete in app notification in app async deletes notifications in app
	/// </summary>
	[Fact]
	public async Task DeleteInAppAlertInAppAsync_DeletesNotificationsInApp()
	{
		var tenantId = "tenant1";
		var appId = "app1";
		var expectedResult = 5; // Assume 5 records were deleted

		_pgRepositoryMock
			.Setup(repo => repo.DeleteAllAsync<InAppAlert>(It.IsAny<TenantAppScopedObject>()))
			.ReturnsAsync(expectedResult);

		var result = await _service.DeleteInAppAlertInAppAsync(tenantId, appId);

		Assert.Equal(expectedResult, result);
		_pgRepositoryMock.Verify(repo => repo.DeleteAllAsync<InAppAlert>(
			It.Is<TenantAppScopedObject>(t =>
				t.tenant_id == tenantId && t.app_id == appId)
		), Times.Once);
	}

	/// <summary>
	/// Tests that query in app notifications async returns query results
	/// </summary>
	[Fact]
	public async Task QueryInAppAlertsAsync_ReturnsQueryResults()
	{
		var sql = "SELECT * FROM in_app_notifications WHERE name = @name";
		var param = new { name = "Notification1" };
		var notifications = new List<InAppAlert>
		{
			new InAppAlert { id = "1", subject = "Notification1" },
		};

		_pgRepositoryMock
			.Setup(repo => repo.QueryAsync<InAppAlert>(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<IDbTransaction?>()))!
			.ReturnsAsync(notifications);

		var result = await _service.QueryInAppAlertsAsync(sql, param);

		Assert.Equal(notifications, result);
	}
}
