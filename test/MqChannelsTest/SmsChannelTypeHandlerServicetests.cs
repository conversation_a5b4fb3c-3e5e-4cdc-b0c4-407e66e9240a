using Apex.Models;
using Apex.Services;
using Microsoft.Extensions.Logging;
using Moq;
using MqChannels.Services;

namespace MqChannelsTest;

/// <summary>
/// The sms channel type handler tests class
/// </summary>
public sealed class SmsChannelTypeHandlerTests
{
	/// <summary>
	/// The handler
	/// </summary>
	private readonly SmsChannelTypeHandler _handler;

	/// <summary>
	/// Initializes a new instance of the <see cref="SmsChannelTypeHandlerTests"/> class
	/// </summary>
	public SmsChannelTypeHandlerTests()
	{
		var _loggerMock = new Mock<ILogger<SmsChannelTypeHandler>>();
		var _jdMock = new Mock<IJiffyDriveRestService>();
		var _omsMock = new Mock<IOrgManagementService>();
		var _serviceOptions = Microsoft.Extensions.Options.Options.Create(new Apex.Options.ServiceOptions());
		_handler = new SmsChannelTypeHandler(_loggerMock.Object, _serviceOptions, _jdMock.Object, _omsMock.Object);
	}

	/// <summary>
	/// Tests that process message async returns true and none
	/// </summary>
	[Fact]
	public async Task ProcessMessageAsync_ReturnsTrueAndNone()
	{

		var message = new Message("1", null, null, null, null);
		var result = await _handler.ProcessMessageAsync(message);
		Assert.True(result.Item1);
		Assert.Equal(MessageNack.None, result.Item2);
	}
}
