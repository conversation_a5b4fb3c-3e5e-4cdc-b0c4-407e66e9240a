<Project Sdk="Microsoft.NET.Sdk">
	<Import Project="../../src/Build.Common.properties" />

	<PropertyGroup>
		<IsPackable>false</IsPackable>
		<SonarQubeExclude>true</SonarQubeExclude>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="EmailChannelTypeHandlerServicetests.cs" />
	  <Compile Remove="InAppChannelTypeHandlerServiceTests.cs" />
	  <Compile Remove="MqChannelsControllerTests.cs" />
	</ItemGroup>

	<ItemGroup>
		<Using Include="Xunit" />
		<AssemblyAttribute Include="System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverageAttribute" />
	</ItemGroup>

	<ItemGroup>
	  <None Include="EmailChannelTypeHandlerServicetests.cs" />
	  <None Include="InAppChannelTypeHandlerServiceTests.cs" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.9" />
		<PackageReference Include="Microsoft.AspNetCore.TestHost" Version="9.0.9" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
		<PackageReference Include="RabbitMq.Client" Version="7.1.2" />
		<PackageReference Include="Moq" Version="[4.18.4]" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="3.1.5">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="coverlet.collector" Version="6.0.4">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\src\MqChannels\MqChannels.csproj" />
	</ItemGroup>

	<ItemGroup>
		<None Update="db.properties">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="extra.properties">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="principal.properties">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>


</Project>
