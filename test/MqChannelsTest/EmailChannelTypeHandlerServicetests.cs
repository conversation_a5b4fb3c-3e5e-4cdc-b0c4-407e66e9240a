using Apex.Models;
using Apex.Services;
using Microsoft.Extensions.Logging;
using Moq;
using MqChannels.Services;

namespace MqChannels.Tests;

/// <summary>
/// The email channel type handler tests class
/// </summary>
public sealed class EmailChannelTypeHandlerTests
{
	/// <summary>
	/// The handler
	/// </summary>
	private readonly EmailChannelTypeHandler _handler;

	/// <summary>
	/// Initializes a new instance of the <see cref="EmailChannelTypeHandlerTests"/> class
	/// </summary>
	public EmailChannelTypeHandlerTests()
	{
		var _loggerMock = new Mock<ILogger<EmailChannelTypeHandler>>();
		var _jdMock = new Mock<IJiffyDriveRestService>();
		var _omsMock = new Mock<IOrgManagementService>();
		var _alertMock = new Mock<IAlertdefPreferenceService>();
		var _serviceOptions = Microsoft.Extensions.Options.Options.Create(new Apex.Options.ServiceOptions());
		_handler = new EmailChannelTypeHandler(_loggerMock.Object, _serviceOptions, _jdMock.Object, _omsMock.Object, _alertMock.Object);
	}

	/// <summary>
	/// Tests that process message async returns true and none
	/// </summary>
	[Fact]
	public async Task ProcessMessageAsync_ReturnsTrueAndNone()
	{
		var message = new Message("1", null, null, null, null);
		var result = await _handler.ProcessMessageAsync(message);
		Assert.True(result.Item1);
		Assert.Equal(MessageNack.None, result.Item2);
	}
}
