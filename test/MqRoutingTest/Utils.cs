using Apex.Service.AuthHandlers.Scheme;
using System.Security.Claims;

namespace MqRouting.Tests;

/// <summary>
/// Class Utils. This class cannot be inherited.
/// </summary>
public sealed class Utils
{
	/// <summary>
	/// The user
	/// </summary>
	public static ClaimsPrincipal user = new ClaimsPrincipal(new ClaimsIdentity(
		new Claim[]
		{
			new Claim(JiffyAuthSchemeConstants.CLAIM_Jiffy_User_ID, "testUserId"),
			new Claim(JiffyAuthSchemeConstants.CLAIM_Jiffy_Tenant_ID, "testTenantId"),
			new Claim(JiffyAuthSchemeConstants.CLAIM_Jiffy_App_ID, "testAppId"),
		}));
}