namespace MqRoutingTest;

/// <summary>
/// Class TestFixure.
/// </summary>
public sealed partial class TestFixure
{
	/// <summary>
	/// Initializes a new instance of the <see cref="TestFixure"/> class.
	/// </summary>
	public TestFixure()
	{
		var userProfileFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), "secrets-store");
		userProfileFolder.CreateDirectoryIfNotExists();

		lock (_fileCopyLock)
		{
			File.Copy("db.properties", Path.Combine(userProfileFolder, "db.properties"), true);
			File.Copy("principal.properties", Path.Combine(userProfileFolder, "principal.properties"), true);
			File.Copy("extra.properties", Path.Combine(userProfileFolder, "extra.properties"), true);
		}
	}

	/// <summary>
	/// The file copy lock
	/// </summary>
	private static readonly object _fileCopyLock = new();
}