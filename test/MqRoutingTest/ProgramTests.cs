using Apex.Options;
using Apex.Services;
using Common.Postgres;
using Common.Postgres.Options;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using MqRouting.Services;
using System.Data;
using Options = Microsoft.Extensions.Options.Options;

namespace MqRoutingTest;

/// <summary>
/// Class ProgramTests.
/// </summary>
public sealed partial class ProgramTests : IClassFixture<TestFixure>
{
	/// <summary>
	/// Defines the test method TestRootEndpoint_BuildVersion.
	/// </summary>
	[Fact]
	public async Task TestRootEndpoint_BuildVersion()
	{
		await using var application = GetMockedWebApplication();
		using var client = application.CreateClient();
		var result = client.GetStringAsync("/version");
		Assert.NotNull(result);
	}

	/// <summary>
	/// Defines the test method TestExceptions.
	/// </summary>
	[Fact]
	public async Task TestExceptions()
	{
		await Assert.ThrowsAsync<MqRoutingException>(() => throw new MqRoutingException("test"));
	}

	/// <summary>
	/// Gets the mocked web application.
	/// </summary>
	/// <returns>WebApplicationFactory&lt;Program&gt;.</returns>
	private static WebApplicationFactory<Program> GetMockedWebApplication(Action<IWebHostBuilder>? callback = null)
	{
		return GetWebApplication((builder) =>
		{
			builder.ConfigureServices((context, services) =>
			{
				var pgRepositoryMock = new Mock<IPgRepository2>();
				pgRepositoryMock.Setup(x => x.ExecuteNonQueryAsync(It.IsAny<string>(), It.IsAny<object?>(), It.IsAny<IDbTransaction>()))
					.ReturnsAsync(() => 1);
				services.AddTransient(sp => pgRepositoryMock.Object);

				var disposableMock = new Mock<IAsyncDisposable>();
				disposableMock.Setup(x => x.DisposeAsync());

				var receiveService = new Mock<IMqReceiveService>();
				receiveService.Setup(x => x.StartReceiveAsync(It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<Func<Apex.Models.Message, Task<(bool, MessageNack)>>>()))
					.ReturnsAsync(() => disposableMock.Object);
				services.AddTransient(sp => receiveService.Object);

				services.AddSingleton(sp => Options.Create(new SecureMqOptions
				{
				}));

#if DEBUG
				services.AddSingleton(sp => Options.Create(new SecurePgOptions
				{
					DbName = "postgres",
					Host = "host.docker.internal",
					Port = 5432,
					Username = "postgres",
					Password = "secret123!!",
				}));
#else
				services.AddSingleton(sp => Options.Create(new SecurePgOptions
				{
					DbName = "postgres",
					Host = "localhost",
					Port = 5432,
					Username = "test_user",
					Password = "test_user_password",
				}));
#endif

				callback?.Invoke(builder);
			});
		});
	}

	/// <summary>
	/// Get web application.
	/// </summary>
	/// <param name="cb">The cb.</param>
	/// <returns>A Task&lt;WebApplicationFactory`1&gt; representing the asynchronous operation.</returns>
	internal static WebApplicationFactory<Program> GetWebApplication(Action<IWebHostBuilder>? cb = null)
	{
		var application = new WebApplicationFactory<Program>()
		.WithWebHostBuilder(builder =>
		{
			builder.UseSetting("ASPNETCORE_ENVIRONMENT", "Development");

			cb?.Invoke(builder);
		});
		return application;
	}
}