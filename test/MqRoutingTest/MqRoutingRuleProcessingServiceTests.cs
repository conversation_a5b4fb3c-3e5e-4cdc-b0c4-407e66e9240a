using Apex.Models;
using Apex.Services;
using Microsoft.Extensions.Logging;
using Moq;
using MqRouting.Services;

namespace MqRoutingTest;

/// <summary>
/// The mq mq routing rule processing service tests class
/// </summary>
public sealed class MqMqRoutingRuleProcessingServiceTests
{
	/// <summary>
	/// The rule service mock
	/// </summary>
	private readonly Mock<IMqRoutingRulesService> _ruleServiceMock;

	/// <summary>
	/// The service
	/// </summary>
	private readonly MqRoutingRuleProcessingService _service;

	/// <summary>
	/// Initializes a new instance of the <see cref="MqMqRoutingRuleProcessingServiceTests"/> class
	/// </summary>
	public MqMqRoutingRuleProcessingServiceTests()
	{
		_ruleServiceMock = new Mock<IMqRoutingRulesService>();
		Mock<ILogger<MqRoutingRuleProcessingService>> loggerMock = new();

		_service = new MqRoutingRuleProcessingService(loggerMock.Object, _ruleServiceMock.Object,
			new TestServiceProvider());
	}

	/// <summary>
	/// Tests that process message async successful processing returns true and none
	/// </summary>
	[Fact]
	public async Task ProcessMessageAsync_SuccessfulProcessing_ReturnsTrueAndNone()
	{
		var topicName = "testTopic";
		var message = new Message(

			"messageId",
			"messageType",
			null,
			new Auth
			(
				"tenantId",
				"appId"
			));

		var matchingRules = new List<MqRouting.Models.MqRoutingRule>
		{
			new MqRouting.Models.MqRoutingRule
			{
				id = "ruleId",
				name = "ruleName",
				emailinfo = new MqRouting.Models.EmailAlertInfo("mail1",
						true,
						"",
						""),
				tenant_id = null!,
				app_id = null!,
				type = null!
			}
		};
		_ruleServiceMock.Setup(r => r.GetRoutingRulesByTypeAsync(It.IsAny<string>(),
				It.IsAny<string>(), It.IsAny<string>()))!
			.ReturnsAsync(matchingRules);

		var result = await _service.ProcessAlertAsync(topicName, message);

		Assert.True(result.Item1);
		Assert.Equal(MessageNack.None, result.Item2);


	}

	/// <summary>
	/// Tests that process message async notification processing throws exception continues processing
	/// </summary>
	[Fact]
	public async Task ProcessMessageAsync_NotificationProcessingThrowsException_ContinuesProcessing()
	{
		var topicName = "testTopic";
		var message = new Message
		(
			"messageId",
			"messageType",
			null,
			new Auth
			(
				"tenantId",
				"appId"
			));

		var notification1 = new MqRouting.Models.EmailAlertInfo("EmailNotification", true, "", "");
		var notification2 = new MqRouting.Models.SmsAlertInfo("SmsNotification", true, "", "");

		var matchingRule = new MqRouting.Models.MqRoutingRule()
		{
			emailinfo = notification1,
			smsinfo = notification2,
			id = "ruleId",
			tenant_id = null!,
			app_id = null!,
			name = "ruleName",
			type = null!
		};

		var matchingRules = new List<MqRouting.Models.MqRoutingRule?> { matchingRule };

		_ruleServiceMock.Setup(r => r.GetRoutingRulesByTypeAsync(
			message.Auth.TenantId, message.Auth.AppId, message.Type))
			.ReturnsAsync(matchingRules);

		var result = await _service.ProcessAlertAsync(topicName, message);

		Assert.True(result.Item1);
		Assert.Equal(MessageNack.None, result.Item2);
	}

	/// <summary>
	/// Tests that process message async overall processing throws exception returns true and requeue
	/// </summary>
	[Fact]
	public async Task ProcessMessageAsync_OverallProcessingThrowsException_ReturnsTrueAndRequeue()
	{
		var topicName = "testTopic";
		var message = new Message
		(
			"messageId",
			"messageType",
			null,
			new Auth
			(
				"tenantId",
				"appId"
			));

		_ruleServiceMock.Setup(r => r.GetRoutingRulesByTypeAsync(
			message.Auth.TenantId, message.Auth.AppId, message.Type))
			.ThrowsAsync(new Exception("Rule service error"));

		var result = await _service.ProcessAlertAsync(topicName, message);

		Assert.False(result.Item1);
		Assert.Equal(MessageNack.Requeue, result.Item2);
	}
}

/// <summary>
/// The test service provider class
/// </summary>
/// <seealso cref="IServiceProvider"/>
public sealed class TestServiceProvider : IServiceProvider
{
	/// <summary>
	/// The type
	/// </summary>
	private readonly Dictionary<(Type, string), object> _services = new Dictionary<(Type, string), object>();

	/// <summary>
	/// Adds the service using the specified key
	/// </summary>
	/// <typeparam name="T">The </typeparam>
	/// <param name="key">The key</param>
	/// <param name="service">The service</param>
	public void AddService<T>(string key, T service)
	{
		_services[(typeof(T), key)] = service!;
	}

	/// <summary>
	/// Gets the service using the specified service type
	/// </summary>
	/// <param name="serviceType">The service type</param>
	/// <exception cref="NotImplementedException">Use GetRequiredKeyedService instead.</exception>
	/// <returns>The object</returns>
	public object GetService(Type serviceType)
	{
		throw new NotImplementedException("Use GetRequiredKeyedService instead.");
	}

	/// <summary>
	/// Gets the required keyed service using the specified key
	/// </summary>
	/// <typeparam name="T">The </typeparam>
	/// <param name="key">The key</param>
	/// <exception cref="InvalidOperationException">No service of type '{typeof(T)}' with key '{key}' has been registered.</exception>
	/// <returns>The</returns>
	public T GetRequiredKeyedService<T>(string key)
	{
		if (_services.TryGetValue((typeof(T), key), out var service))
		{
			return (T)service;
		}
		throw new InvalidOperationException($"No service of type '{typeof(T)}' with key '{key}' has been registered.");
	}
}
