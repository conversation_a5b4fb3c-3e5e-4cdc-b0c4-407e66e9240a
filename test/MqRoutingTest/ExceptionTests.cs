using MqRouting.Services;
using System.Runtime.Serialization;
namespace MqRoutingTest;

/// <summary>
/// Class ExceptionTests.
/// </summary>
public sealed partial class ExceptionTests : IClassFixture<TestFixure>
{
	/// <summary>
	/// Defines the test method MediatorException_ShouldSerialize.
	/// </summary>
	[Fact]
	public void MediatorException_ShouldSerialize()
	{
		var e2 = Clone(new MqRoutingException("message1"));
		Assert.NotNull(e2);
	}

	/// <summary>
	/// Serializes the specified source.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="source">The source.</param>
	/// <returns>Stream.</returns>
	private static Stream Serialize<T>(T source)
		where T : Exception
	{
		var formatter = new DataContractSerializer(typeof(T));
		var stream = new MemoryStream();
		formatter.WriteObject(stream, source);
		return stream;
	}

	/// <summary>
	/// Deserializes the specified stream.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="stream">The stream.</param>
	/// <returns>T.</returns>
	private static T? Deserialize<T>(Stream stream)
		where T : Exception
	{
		var formatter = new DataContractSerializer(typeof(T));
		stream.Position = 0;
		return (T?)formatter.ReadObject(stream)!;
	}

	/// <summary>
	/// Clones the specified source.
	/// </summary>
	/// <typeparam name="T"></typeparam>
	/// <param name="source">The source.</param>
	/// <returns>T.</returns>
	private static T? Clone<T>(T source)
		where T : Exception
	{
		return Deserialize<T>(Serialize(source));
	}
}