using Common.Postgres;
using Moq;
using MqRouting.Models;
using MqRouting.Services;
using System.Data;

namespace MqRoutingTest;

/// <summary>
/// The in app rule service tests class
/// </summary>
public sealed class MqRoutingRulesServiceTests
{
	/// <summary>
	/// The service
	/// </summary>
	private readonly MqRoutingRulesService _service;
	/// <summary>
	/// The pg repository mock
	/// </summary>
	private readonly Mock<IPgRepository2> _pgRepositoryMock;

	/// <summary>
	/// Initializes a new instance of the <see cref="MqRoutingRulesServiceTests"/> class
	/// </summary>
	public MqRoutingRulesServiceTests()
	{
		_pgRepositoryMock = new Mock<IPgRepository2>();
		_service = new MqRoutingRulesService(_pgRepositoryMock.Object);
	}

	/// <summary>
	/// Tests that get all in app rules async returns list of rules
	/// </summary>
	[Fact]
	public async Task GetAllMqRoutingRulesAsync_ReturnsListOfrules()
	{

		var tenantId = "tenant1";
		var appId = "app1";
		var rules = new List<MqRoutingRule>
		{
			new MqRoutingRule { id = "1", name = "rule1" },
			new MqRoutingRule { id = "2", name = "rule2" }
		};

		_pgRepositoryMock
			.Setup(repo => repo.GetAllAsync<MqRoutingRule>(It.IsAny<TenantAppScopedObject>()))!
			.ReturnsAsync(rules);


		var result = await _service.GetAllRoutingRulesAsync(tenantId, appId);


		Assert.Equal(rules, result);
		_pgRepositoryMock.Verify(repo => repo.GetAllAsync<MqRoutingRule>(
			It.Is<TenantAppScopedObject>(t => t.tenant_id == tenantId && t.app_id == appId)
		), Times.Once);
	}

	/// <summary>
	/// Tests that get in app rules by user async returns rules for user
	/// </summary>
	[Fact]
	public async Task GetMqRoutingRulesByUserAsync_ReturnsrulesForUser()
	{

		var tenantId = "tenant1";
		var appId = "app1";
		var type = "type1";
		var rules = new List<MqRoutingRule>
		{
			new MqRoutingRule { id = "1", name = "rule1", type = type },
			new MqRoutingRule { id = "2", name = "rule2", type = type }
		};

		_pgRepositoryMock
			.Setup(repo => repo.QueryAsync<MqRoutingRule>(It.IsAny<string>(), It.IsAny<object?>(),
				It.IsAny<IDbTransaction?>()))!
			.ReturnsAsync(rules);
		var result = await _service.GetRoutingRulesByTypeAsync(tenantId, appId, type);

		Assert.Equal(rules, result);
	}

	/// <summary>
	/// Tests that get in app rule by id async returns rule by id
	/// </summary>
	[Fact]
	public async Task GetMqRoutingRuleByIdAsync_ReturnsruleById()
	{

		var tenantId = "tenant1";
		var appId = "app1";
		var id = "rule1";
		var rule = new MqRoutingRule { id = id, name = "rule1" };

		_pgRepositoryMock
			.Setup(repo => repo.GetByIdAsync<MqRoutingRule>(It.IsAny<TenantAppScopedObjectWithId>()))
			.ReturnsAsync(rule);


		var result = await _service.GetRoutingRuleByIdAsync(tenantId, appId, id);


		Assert.Equal(rule, result);
		_pgRepositoryMock.Verify(repo => repo.GetByIdAsync<MqRoutingRule>(
			It.Is<TenantAppScopedObjectWithId>(t =>
				t.tenant_id == tenantId && t.app_id == appId && t.id == id)
		), Times.Once);
	}

	/// <summary>
	/// Tests that insert in app rule async inserts rule
	/// </summary>
	[Fact]
	public async Task InsertMqRoutingRuleAsync_Insertsrule()
	{
		var rule = new MqRoutingRule { id = "rule1", name = "rule1" };

		_pgRepositoryMock
			.Setup(repo => repo.InsertAsync(It.IsAny<MqRoutingRule>()))
			.ReturnsAsync(true);

		var result = await _service.UpsertRoutingRuleAsync(rule);
		Assert.Equal(0, result);
		_pgRepositoryMock.Verify(repo => repo.ExecuteNonQueryAsync(
			It.IsAny<string>(),
			It.IsAny<object>(),
			It.IsAny<IDbTransaction>()),
			Times.Once);
	}

	/// <summary>
	/// Tests that delete in app rule by id async deletes rule by id
	/// </summary>
	[Fact]
	public async Task DeleteMqRoutingRuleByIdAsync_DeletesruleById()
	{

		var tenantId = "tenant1";
		var appId = "app1";
		var id = "rule1";
		var expectedResult = 1;

		_pgRepositoryMock
			.Setup(repo => repo.DeleteByIdAsync<MqRoutingRule>(It.IsAny<TenantAppScopedObjectWithId>()))
			.ReturnsAsync(expectedResult);

		var result = await _service.DeleteRoutingRuleByIdAsync(tenantId, appId, id);

		Assert.Equal(expectedResult, result);
		_pgRepositoryMock.Verify(repo => repo.DeleteByIdAsync<MqRoutingRule>(
			It.Is<TenantAppScopedObjectWithId>(t =>
				t.tenant_id == tenantId && t.app_id == appId && t.id == id)
		), Times.Once);
	}

	/// <summary>
	/// Tests that delete in app rule in app async deletes rules in app
	/// </summary>
	[Fact]
	public async Task DeleteMqRoutingRuleInAppAsync_DeletesrulesInApp()
	{
		var tenantId = "tenant1";
		var appId = "app1";
		var expectedResult = 5; // Assume 5 records were deleted

		_pgRepositoryMock
			.Setup(repo => repo.DeleteAllAsync<MqRoutingRule>(It.IsAny<TenantAppScopedObject>()))
			.ReturnsAsync(expectedResult);

		var result = await _service.DeleteRoutingRuleInAppAsync(tenantId, appId);

		Assert.Equal(expectedResult, result);
		_pgRepositoryMock.Verify(repo => repo.DeleteAllAsync<MqRoutingRule>(
			It.Is<TenantAppScopedObject>(t =>
				t.tenant_id == tenantId && t.app_id == appId)
		), Times.Once);
	}

	/// <summary>
	/// Tests that query in app rules async returns query results
	/// </summary>
	[Fact]
	public async Task QueryMqRoutingRulesAsync_ReturnsQueryResults()
	{

		var sql = "SELECT * FROM mq_routing_rules WHERE name = @name";
		var param = new { name = "rule1" };
		var rules = new List<MqRoutingRule>
		{
			new MqRoutingRule { id = "1", name = "rule1" },
		};

		_pgRepositoryMock
			.Setup(repo => repo.QueryAsync<MqRoutingRule>(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<IDbTransaction?>()))!
			.ReturnsAsync(rules);


		var result = await _service.QueryRoutingRulesAsync(sql, param);
		Assert.Equal(rules, result);
	}
}
