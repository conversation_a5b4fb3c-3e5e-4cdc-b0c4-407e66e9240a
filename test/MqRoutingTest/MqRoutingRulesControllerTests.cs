using Apex.Service.AuthHandlers.Scheme;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using MqRouting.Controllers;
using MqRouting.Models;
using MqRouting.Services;
using MqRouting.Tests;

namespace MqRoutingTest;

/// <summary>
/// The mq routing rules controller tests class
/// </summary>
public sealed class MqRoutingRulesControllerTests
{
	/// <summary>
	/// The mq routing rules service mock
	/// </summary>
	private readonly Mock<IMqRoutingRulesService> _mqRoutingRulesServiceMock;

	/// <summary>
	/// The controller
	/// </summary>
	private readonly MqRoutingRulesController _controller;

	/// <summary>
	/// Initializes a new instance of the <see cref="MqRoutingRulesControllerTests"/> class
	/// </summary>
	public MqRoutingRulesControllerTests()
	{
		Mock<ILogger<MqRoutingRulesController>> loggerMock = new();
		_mqRoutingRulesServiceMock = new Mock<IMqRoutingRulesService>();

		_controller = new MqRoutingRulesController(loggerMock.Object, _mqRoutingRulesServiceMock.Object)
		{
			ControllerContext = new ControllerContext
			{
				HttpContext = new DefaultHttpContext()
			}
		};

		_controller.ControllerContext.HttpContext.User = Utils.user;
		var headers = _controller.ControllerContext.HttpContext.Request.Headers;
		headers[JiffyAuthSchemeConstants.HEADER_X_Jiffy_App_ID] = "testAppId";
		headers[JiffyAuthSchemeConstants.HEADER_X_Jiffy_Tenant_ID] = "testTenantId";
		headers[JiffyAuthSchemeConstants.HEADER_X_Jiffy_User_ID] = "testUserId";
	}

	/// <summary>
	/// Tests that get mq rules async with no type returns all rules
	/// </summary>
	[Fact]
	public async Task GetMqRulesAsync_WithNoType_ReturnsAllRules()
	{
		var rules = new List<MqRoutingRule>
		{
			new MqRoutingRule { id = "1", name = "Rule1" },
			new MqRoutingRule { id = "2", name = "Rule2" }
		};

		_mqRoutingRulesServiceMock.Setup(service =>
				service.GetAllRoutingRulesAsync("testTenantId", "testAppId"))!
			.ReturnsAsync(rules);


		var result = await _controller.GetMqRulesAsync();


		var okResult = Assert.IsType<OkObjectResult>(result);
		var returnValue = Assert.IsAssignableFrom<IEnumerable<MqRoutingRule>>(okResult.Value);
		Assert.Equal(2, returnValue.Count());
		_mqRoutingRulesServiceMock.Verify(service =>
			service.GetAllRoutingRulesAsync("testTenantId", "testAppId"), Times.Once);
	}

	/// <summary>
	/// Tests that get mq rules async with type returns filtered rules
	/// </summary>
	[Fact]
	public async Task GetMqRulesAsync_WithType_ReturnsFilteredRules()
	{
		var type = "Alert";
		var rules = new List<MqRoutingRule>
		{
			new MqRoutingRule { id = "1", name = "Rule1", type = type }
		};

		_mqRoutingRulesServiceMock.Setup(service =>
				service.GetRoutingRulesByTypeAsync("testTenantId", "testAppId", type))!
			.ReturnsAsync(rules);


		var result = await _controller.GetMqRulesAsync(type);


		var okResult = Assert.IsType<OkObjectResult>(result);
		var returnValue = Assert.IsAssignableFrom<IEnumerable<MqRoutingRule>>(okResult.Value);
		var mqRoutingRules = returnValue as MqRoutingRule[] ?? returnValue.ToArray();
		Assert.Single(mqRoutingRules);
		Assert.Equal(type, mqRoutingRules.First().type);
		_mqRoutingRulesServiceMock.Verify(service =>
			service.GetRoutingRulesByTypeAsync("testTenantId", "testAppId", type), Times.Once);
	}

	/// <summary>
	/// Tests that get mq rule by id async returns rule
	/// </summary>
	[Fact]
	public async Task GetMqRuleByIdAsync_ReturnsRule()
	{
		var id = "1";
		var rule = new MqRoutingRule { id = id, name = "Rule1" };

		_mqRoutingRulesServiceMock.Setup(service =>
			service.GetRoutingRuleByIdAsync("testTenantId", "testAppId", id))
			.ReturnsAsync(rule);


		var result = await _controller.GetMqRuleByIdAsync(id);


		var okResult = Assert.IsType<OkObjectResult>(result);
		var returnValue = Assert.IsType<MqRoutingRule>(okResult.Value);
		Assert.Equal(id, returnValue.id);
		_mqRoutingRulesServiceMock.Verify(service =>
			service.GetRoutingRuleByIdAsync("testTenantId", "testAppId", id), Times.Once);
	}

	/// <summary>
	/// Tests that insert mq rule async returns ok result
	/// </summary>
	[Fact]
	public async Task InsertMqRuleAsync_ReturnsOkResult()
	{
		var rule = new MqRoutingRule
		{
			id = "1",
			name = "Rule1",
			type = "Alert",
			description = "Test rule",
			category = "Test",
			tags = ["Tag1", "Tag2"],
			enable_condition = "Condition",
			condition_parameters = [],
			is_enabled = true,
			json_schema = null,
			sample = null,
		};

		_mqRoutingRulesServiceMock.Setup(service =>
			service.UpsertRoutingRuleAsync(It.IsAny<MqRoutingRule>()))
			.ReturnsAsync(1);

		var result = await _controller.UpsertMqRuleAsync(rule);
		var okResult = Assert.IsType<OkObjectResult>(result);
		var returnValue = Assert.IsType<int>(okResult.Value);

		_mqRoutingRulesServiceMock.Verify(service =>
			service.UpsertRoutingRuleAsync(It.Is<MqRoutingRule>(r =>
				r.tenant_id == "testTenantId" &&
				r.app_id == "testAppId" &&
				r.id == rule.id &&
				r.name == rule.name)), Times.Once);
	}

	/// <summary>
	/// Tests that update mq rule async returns ok result
	/// </summary>
	[Fact]
	public async Task UpdateMqRuleAsync_ReturnsOkResult()
	{

		var rule = new MqRoutingRule
		{
			id = "1",
			name = "UpdatedRule",
			type = "Alert",
			description = "Updated description",
			category = "Test",
			tags = new List<string> { "Tag1", "Tag2" },
			enable_condition = "NewCondition",
			condition_parameters = new List<ConditionParameter>(),
			is_enabled = true,
			json_schema = null,
			sample = null,
		};

		_mqRoutingRulesServiceMock.Setup(service =>
			service.UpsertRoutingRuleAsync(rule))
			.ReturnsAsync(1);

		var result = await _controller.UpsertMqRuleAsync(rule);

		var okResult = Assert.IsType<OkObjectResult>(result);
		var returnValue = Assert.IsType<int>(okResult.Value);

		_mqRoutingRulesServiceMock.Verify(service =>
			service.UpsertRoutingRuleAsync(It.IsAny<MqRoutingRule>()), Times.Once);
	}

	/// <summary>
	/// Tests that delete mq rule by id async returns ok result
	/// </summary>
	[Fact]
	public async Task DeleteMqRuleByIdAsync_ReturnsOkResult()
	{
		var id = "1";

		_mqRoutingRulesServiceMock.Setup(service =>
			service.DeleteRoutingRuleByIdAsync("testTenantId", "testAppId", id))
			.ReturnsAsync(1);

		var result = await _controller.DeleteMqRuleByIdAsync(id);

		var okResult = Assert.IsType<OkObjectResult>(result);
		var returnValue = Assert.IsType<int>(okResult.Value);
		Assert.True(returnValue == 1);

		_mqRoutingRulesServiceMock.Verify(service =>
			service.DeleteRoutingRuleByIdAsync("testTenantId", "testAppId", id), Times.Once);
	}
}
