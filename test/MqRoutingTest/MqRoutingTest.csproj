<Project Sdk="Microsoft.NET.Sdk">
	<Import Project="../../src/Build.Common.properties" />

	<PropertyGroup>
		<IsPackable>false</IsPackable>
		<SonarQubeExclude>true</SonarQubeExclude>
	</PropertyGroup>

	<ItemGroup>
		<Using Include="Xunit" />
		<AssemblyAttribute Include="System.Diagnostics.CodeAnalysis.ExcludeFromCodeCoverageAttribute" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="RabbitMq.Client" Version="7.1.2" />
		<PackageReference Include="Net.Common.PgRepository2" Version="3.0.19" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.9" />
		<PackageReference Include="Microsoft.AspNetCore.TestHost" Version="9.0.9" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
		<PackageReference Include="Moq" Version="4.20.72" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="3.1.5">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="coverlet.collector" Version="6.0.4">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\src\MqRouting\MqRouting.csproj" />
	</ItemGroup>

	<ItemGroup>
		<None Update="db.properties">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="principal.properties">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="extra.properties">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

</Project>
