using Apex.Options;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Options = Microsoft.Extensions.Options.Options;

namespace MqTest;

/// <summary>
/// Class ProgramTests.
/// </summary>
public sealed partial class ProgramTests : IClassFixture<TestFixure>
{
	/// <summary>
	/// Gets the mocked web application.
	/// </summary>
	/// <param name="callback">The callback.</param>
	/// <returns>WebApplicationFactory&lt;Program&gt;.</returns>
	internal static WebApplicationFactory<Program> GetMockedWebApplication(Action<IWebHostBuilder>? callback = null)
	{
		//var userProfileFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), "secrets-store");
		//userProfileFolder.CreateDirectoryIfNotExists();
		//File.Copy("db.properties", Path.Combine(userProfileFolder, "db.properties"), true);
		//File.Copy("principal.properties", Path.Combine(userProfileFolder, "principal.properties"), true);

		return GetWebApplication((builder) =>
		{
			builder.ConfigureServices((context, services) =>
			{
				//var subscriptionMock = new Mock<IAsyncSubscription>();
				//subscriptionMock.Setup(x => x.Start());

				//var connectionMock = new Mock<IConnection>();
				//connectionMock.Setup(x => x.SubscribeAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<EventHandler<MsgHandlerEventArgs>>()))
				//	.Returns(subscriptionMock.Object);

				//var connectionFactoryMock = new Mock<IConnectionFactory>();
				//connectionFactoryMock.Setup(x => x.CreateConnection(It.IsAny<string>()))
				//	.Returns(connectionMock.Object);

				//services.AddTransient((sp) => connectionFactoryMock.Object);

				services.AddSingleton(sp => Options.Create(new MqOptions
				{
				}));
			});

			callback?.Invoke(builder);
		});
	}

	/// <summary>
	/// Get web application.
	/// </summary>
	/// <param name="cb">The cb.</param>
	/// <returns>A Task&lt;WebApplicationFactory`1&gt; representing the asynchronous operation.</returns>
	internal static WebApplicationFactory<Program> GetWebApplication(Action<IWebHostBuilder>? cb = null)
	{
		var application = new WebApplicationFactory<Program>()
		.WithWebHostBuilder(builder =>
		{
			builder.UseSetting("ASPNETCORE_ENVIRONMENT", "Development");

			cb?.Invoke(builder);
		});
		return application;
	}

	/// <summary>
	/// Defines the test method TestRootEndpoint.
	/// </summary>
	[Fact]
	public async Task TestRootEndpoint()
	{
		await using var application = GetMockedWebApplication();
		using var client = application.CreateClient();
		Assert.NotNull(client);
	}

	/// <summary>
	/// Defines the test method TestRootEndpoint_BuildVersion.
	/// </summary>
	[Fact]
	public async Task TestRootEndpoint_BuildVersion()
	{
		await using var application = GetMockedWebApplication();
		using var client = application.CreateClient();
		var result = client.GetStringAsync("/version");
		Assert.NotNull(result);
	}
}