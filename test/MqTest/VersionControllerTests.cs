namespace MqTest;

using Microsoft.AspNetCore.Mvc;
using Mq.Controllers;

/// <summary>
/// Class VersionControllerTests.
/// </summary>
public sealed partial class VersionControllerTests
{
	/// <summary>
	/// Defines the test method GetVersion_Should_Return_Valid_OKResult.
	/// </summary>
	[Fact]
	public void GetVersion_Should_Return_Valid_OKResult()
	{
		var controller = new VersionController();
		var result = controller.GetVersion();
		Assert.IsType<OkObjectResult>(result);
	}
}