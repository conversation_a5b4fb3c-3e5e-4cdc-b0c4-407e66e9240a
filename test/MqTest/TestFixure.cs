namespace MqTest;

/// <summary>
/// Class TestFixure.
/// </summary>
public sealed partial class TestFixure
{
	/// <summary>
	/// Initializes a new instance of the <see cref="TestFixure"/> class.
	/// </summary>
	public TestFixure()
	{
		var secretsStore = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), "secrets-store");

		var principalPropertiesFile = Path.Combine(secretsStore, "principal.properties")
			.CreateDirectoryForFileIfNotExists();
		File.Copy("principal.properties", principalPropertiesFile, true);

		var extraPropertiesFile = Path.Combine(secretsStore, "extra.properties")
			.CreateDirectoryForFileIfNotExists();
		File.Copy("extra.properties", extraPropertiesFile, true);
	}
}