using Apex.Services;
using Moq;
using RabbitMQ.Client;

namespace MqTest;

/// <summary>
/// The mq service tests class
/// </summary>
public sealed class MqServiceTests
{
	/// <summary>
	/// The mq service
	/// </summary>
	private IMqSendService? _mqService;

#if TODO2
	/// <summary>
	/// The logger factory
	/// </summary>
	private LoggerFactory _lf = new();


	/// <summary>
	/// Initializes a new instance of the <see cref="MqServiceTests"/> class
	/// </summary>
	public MqServiceTests()
	{
		SetupMocks();
	}

	/// <summary>
	/// Tests that mq service post async durable should not throw for valid inputs
	/// </summary>
	[Fact]
	public async Task MqService_PostAsync_Durable_Should_Not_Throw_For_Valid_Inputs()
	{
		var message = new PublishNewEventExample().GetExamples();
		await _mqService!.PostMessageAsync("topic", message, isDurable: true);
	}

	/// <summary>
	/// Tests that mq service post async non durable should not throw for valid inputs
	/// </summary>
	[Fact]
	public async Task MqService_PostAsync_NonDurable_Should_Not_Throw_For_Valid_Inputs()
	{
		var message = new PublishNewEventExample().GetExamples();
		await _mqService!.PostMessageAsync("topic", message, isDurable: false);
	}

	/// <summary>
	/// Tests that mq service post async should throw if channel is closed
	/// </summary>
	[Fact]
	public async Task MqService_PostAsync_Should_Throw_If_Channel_Is_Closed()
	{
		var message = new PublishNewEventExample().GetExamples();
		var mqService = new MqSendService(_lf.CreateLogger<MqSendService>(), GetClosedChannel());
		await Assert.ThrowsAsync<InvalidOperationException>(() => mqService!.PostMessageAsync("topic", message, isDurable: true));
	}

	/// <summary>
	/// Setup the mocks
	/// </summary>
	private void SetupMocks()
	{
		var chanelMock = new Mock<IChannel>().SetupAllProperties();
		chanelMock.Setup(mck => mck.CreateBasicProperties()).Returns(new Mock<IBasicProperties>().Object);
		_mqService = new MqSendService(_lf.CreateLogger<MqSendService>(), chanelMock.Object);
	}
#endif

	private static IChannel GetClosedChannel()
	{
		var chanelMock = new Mock<IChannel>().SetupAllProperties();
		chanelMock.Setup(mck => (IBasicProperties)new BasicProperties()).Returns(new Mock<IBasicProperties>().Object);
		chanelMock.Setup(mck => mck.IsClosed).Returns(true);
		return chanelMock.Object;
	}
}