using Apex.Models;
using Apex.Service.AuthHandlers.Scheme;
using Apex.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Mq.Controllers;
using Mq.Controllers.Examples;
using System.Security.Claims;

namespace MqTest;

/// <summary>
/// The mq controller tests class
/// </summary>

public sealed class MqControllerTests
{
	/// <summary>
	/// The mq controller
	/// </summary>
	private MqController? _mqController;

	/// <summary>
	/// Initializes a new instance of the <see cref="MqControllerTests"/> class
	/// </summary>
	public MqControllerTests()
	{
		SetupMocks();
	}

	/// <summary>
	/// Tests that post durable message should not throw for valid inputs
	/// </summary>
	[Fact]
	public async Task PostDurableMessage_Should_Not_Throw_For_Valid_Inputs()
	{
		var messages = new PublishNewEventExample().GetExamples();
		foreach (var message in messages)
		{
			var result = await _mqController.PostDurableMessage(topicName: "testTopic", message: message.Value);
			Assert.NotNull(result);
		}
	}

	/// <summary>
	/// Tests that post non durable message should not throw for valid inputs
	/// </summary>
	[Fact]
	public async Task PostNonDurableMessage_Should_Not_Throw_For_Valid_Inputs()
	{
		var messages = new PublishNewEventExample().GetExamples();
		foreach (var message in messages)
		{
			var result = await _mqController.PostNonDurableMessage(topicName: "testTopic", message: message.Value);
			Assert.NotNull(result);
		}
	}

	/// <summary>
	/// Setup the mocks
	/// </summary>
	private void SetupMocks()
	{
		var mqServiceMock = new Mock<IMqSendService>().SetupAllProperties();
		mqServiceMock.Setup(mck => mck.PostMessageAsync(It.IsAny<string>(), It.IsAny<Message>(), It.IsAny<bool>()))
			.ReturnsAsync(new Message(Guid.NewGuid().ToString("n"), "test", null, null));

		var httpContext = new DefaultHttpContext();
		var controllerContext = new ControllerContext
		{
			HttpContext = httpContext
		};

		controllerContext.HttpContext.User = new ClaimsPrincipal(new ClaimsIdentity(
		[
			new(JiffyAuthSchemeConstants.CLAIM_Jiffy_Tenant_ID, "tid1"),
			new(JiffyAuthSchemeConstants.CLAIM_Jiffy_App_ID, "app1"),
			new(JiffyAuthSchemeConstants.CLAIM_Jiffy_User_ID, "user1"),
		], "mock"));

		_mqController = new MqController(mqServiceMock.Object)
		{
			ControllerContext = controllerContext
		};
	}
}
